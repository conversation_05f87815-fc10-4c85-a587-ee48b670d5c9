/**
 * 矩阵渲染优化Hook
 * 🎯 核心价值：专门针对矩阵组件的渲染优化，提供智能缓存和增量更新
 * 📦 功能范围：单元格缓存、增量渲染、视口裁剪、性能监控
 * 🔄 架构设计：专用优化策略，结合矩阵特性进行深度优化
 */

import { 
  useCallback, 
  useMemo, 
  useRef, 
  useState, 
  useEffect 
} from 'react';
import type { 
  CellData, 
  CellRenderData, 
  MatrixConfig, 
  MatrixData,
  Coordinate 
} from '@/core/matrix/MatrixTypes';
import { MATRIX_SIZE } from '@/core/matrix/MatrixTypes';
import { useRenderPerformance, useDebouncedValue } from './useRenderOptimization';

// ===== 类型定义 =====

interface MatrixRenderOptions {
  /** 是否启用视口裁剪 */
  enableViewportCulling?: boolean;
  /** 视口边界 */
  viewport?: {
    left: number;
    top: number;
    right: number;
    bottom: number;
  };
  /** 是否启用增量渲染 */
  enableIncrementalRender?: boolean;
  /** 每帧渲染的最大单元格数 */
  maxCellsPerFrame?: number;
  /** 是否启用单元格缓存 */
  enableCellCache?: boolean;
  /** 缓存大小限制 */
  cacheSize?: number;
  /** 防抖延迟 */
  debounceDelay?: number;
}

interface CellCacheEntry {
  key: string;
  renderData: CellRenderData;
  timestamp: number;
  accessCount: number;
}

interface RenderBatch {
  cells: Array<{ x: number; y: number; priority: number }>;
  startTime: number;
  frameIndex: number;
}

interface MatrixRenderMetrics {
  /** 总单元格数 */
  totalCells: number;
  /** 可见单元格数 */
  visibleCells: number;
  /** 缓存命中率 */
  cacheHitRate: number;
  /** 平均渲染时间 */
  averageRenderTime: number;
  /** 跳过的单元格数 */
  skippedCells: number;
  /** 当前帧渲染的单元格数 */
  cellsInCurrentFrame: number;
}

// ===== 单元格缓存管理 =====

class CellRenderCache {
  private cache = new Map<string, CellCacheEntry>();
  private maxSize: number;
  private hits = 0;
  private misses = 0;

  constructor(maxSize: number = 1000) {
    this.maxSize = maxSize;
  }

  set(key: string, renderData: CellRenderData): void {
    if (this.cache.size >= this.maxSize) {
      this.evictLRU();
    }

    this.cache.set(key, {
      key,
      renderData,
      timestamp: Date.now(),
      accessCount: 0,
    });
  }

  get(key: string): CellRenderData | null {
    const entry = this.cache.get(key);
    
    if (entry) {
      entry.accessCount++;
      entry.timestamp = Date.now();
      this.hits++;
      return entry.renderData;
    }
    
    this.misses++;
    return null;
  }

  has(key: string): boolean {
    return this.cache.has(key);
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
    this.hits = 0;
    this.misses = 0;
  }

  getHitRate(): number {
    const total = this.hits + this.misses;
    return total > 0 ? this.hits / total : 0;
  }

  private evictLRU(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }
}

// ===== 视口裁剪工具 =====

const isInViewport = (
  x: number,
  y: number,
  viewport: { left: number; top: number; right: number; bottom: number }
): boolean => {
  return x >= viewport.left && x <= viewport.right && 
         y >= viewport.top && y <= viewport.bottom;
};

const calculateViewport = (
  containerRect: DOMRect,
  cellSize: number,
  buffer: number = 2
): { left: number; top: number; right: number; bottom: number } => {
  const left = Math.max(0, Math.floor(containerRect.left / cellSize) - buffer);
  const top = Math.max(0, Math.floor(containerRect.top / cellSize) - buffer);
  const right = Math.min(MATRIX_SIZE - 1, Math.ceil(containerRect.right / cellSize) + buffer);
  const bottom = Math.min(MATRIX_SIZE - 1, Math.ceil(containerRect.bottom / cellSize) + buffer);

  return { left, top, right, bottom };
};

// ===== 增量渲染管理 =====

const createRenderBatches = (
  cells: Array<{ x: number; y: number; priority: number }>,
  maxCellsPerFrame: number
): RenderBatch[] => {
  // 按优先级排序
  const sortedCells = [...cells].sort((a, b) => b.priority - a.priority);
  
  const batches: RenderBatch[] = [];
  
  for (let i = 0; i < sortedCells.length; i += maxCellsPerFrame) {
    batches.push({
      cells: sortedCells.slice(i, i + maxCellsPerFrame),
      startTime: 0,
      frameIndex: batches.length,
    });
  }
  
  return batches;
};

// ===== 主Hook =====

export const useMatrixRenderOptimization = (
  matrixData: MatrixData,
  config: MatrixConfig,
  getCellRenderData: (x: number, y: number) => CellRenderData | undefined,
  options: MatrixRenderOptions = {}
) => {
  const {
    enableViewportCulling = true,
    viewport,
    enableIncrementalRender = false,
    maxCellsPerFrame = 50,
    enableCellCache = true,
    cacheSize = 1000,
    debounceDelay = 50,
  } = options;

  // 性能监控
  const renderMetrics = useRenderPerformance('MatrixRender');
  
  // 单元格缓存
  const cacheRef = useRef<CellRenderCache>();
  if (!cacheRef.current) {
    cacheRef.current = new CellRenderCache(cacheSize);
  }
  
  // 防抖的矩阵数据
  const debouncedMatrixData = useDebouncedValue(matrixData, debounceDelay);
  
  // 渲染批次状态
  const [currentBatchIndex, setCurrentBatchIndex] = useState(0);
  const [renderBatches, setRenderBatches] = useState<RenderBatch[]>([]);
  
  // 计算可见单元格
  const visibleCells = useMemo(() => {
    const cells: Array<{ x: number; y: number; priority: number }> = [];
    
    for (let y = 0; y < MATRIX_SIZE; y++) {
      for (let x = 0; x < MATRIX_SIZE; x++) {
        // 视口裁剪
        if (enableViewportCulling && viewport && !isInViewport(x, y, viewport)) {
          continue;
        }
        
        // 计算优先级
        let priority = 0;
        const cellData = debouncedMatrixData.cells.get(`${x},${y}`);
        
        if (cellData?.isSelected) priority += 100;
        if (cellData?.isFocused) priority += 50;
        if (cellData?.isHovered) priority += 30;
        if (cellData?.level === 1) priority += 20;
        if (cellData?.isActive) priority += 10;
        
        cells.push({ x, y, priority });
      }
    }
    
    return cells;
  }, [debouncedMatrixData, viewport, enableViewportCulling]);
  
  // 创建渲染批次
  useEffect(() => {
    if (enableIncrementalRender) {
      const batches = createRenderBatches(visibleCells, maxCellsPerFrame);
      setRenderBatches(batches);
      setCurrentBatchIndex(0);
    }
  }, [visibleCells, enableIncrementalRender, maxCellsPerFrame]);
  
  // 增量渲染调度
  useEffect(() => {
    if (enableIncrementalRender && renderBatches.length > 0) {
      const timer = setTimeout(() => {
        if (currentBatchIndex < renderBatches.length - 1) {
          setCurrentBatchIndex(prev => prev + 1);
        }
      }, 16); // ~60fps
      
      return () => clearTimeout(timer);
    }
  }, [currentBatchIndex, renderBatches, enableIncrementalRender]);
  
  // 获取优化后的渲染数据
  const getOptimizedRenderData = useCallback((x: number, y: number): CellRenderData | undefined => {
    const cacheKey = `${x},${y}-${config.mainMode}-${config.contentMode}`;
    
    // 尝试从缓存获取
    if (enableCellCache && cacheRef.current) {
      const cached = cacheRef.current.get(cacheKey);
      if (cached) {
        return cached;
      }
    }
    
    // 获取原始数据
    const renderData = getCellRenderData(x, y);
    
    // 缓存结果
    if (enableCellCache && renderData && cacheRef.current) {
      cacheRef.current.set(cacheKey, renderData);
    }
    
    return renderData;
  }, [config, getCellRenderData, enableCellCache]);
  
  // 获取当前应该渲染的单元格
  const getCurrentRenderCells = useCallback(() => {
    if (!enableIncrementalRender) {
      return visibleCells;
    }
    
    // 返回到当前批次为止的所有单元格
    const cellsToRender: Array<{ x: number; y: number; priority: number }> = [];
    
    for (let i = 0; i <= currentBatchIndex && i < renderBatches.length; i++) {
      cellsToRender.push(...renderBatches[i].cells);
    }
    
    return cellsToRender;
  }, [enableIncrementalRender, visibleCells, currentBatchIndex, renderBatches]);
  
  // 清理缓存
  const clearCache = useCallback(() => {
    if (cacheRef.current) {
      cacheRef.current.clear();
    }
  }, []);
  
  // 预加载单元格
  const preloadCells = useCallback((coordinates: Coordinate[]) => {
    coordinates.forEach(({ x, y }) => {
      getOptimizedRenderData(x, y);
    });
  }, [getOptimizedRenderData]);
  
  // 计算指标
  const metrics: MatrixRenderMetrics = useMemo(() => {
    const currentRenderCells = getCurrentRenderCells();
    
    return {
      totalCells: MATRIX_SIZE * MATRIX_SIZE,
      visibleCells: visibleCells.length,
      cacheHitRate: cacheRef.current?.getHitRate() || 0,
      averageRenderTime: renderMetrics.averageRenderTime,
      skippedCells: MATRIX_SIZE * MATRIX_SIZE - visibleCells.length,
      cellsInCurrentFrame: currentRenderCells.length,
    };
  }, [visibleCells, getCurrentRenderCells, renderMetrics]);
  
  return {
    // 优化后的数据获取函数
    getOptimizedRenderData,
    
    // 当前应该渲染的单元格
    currentRenderCells: getCurrentRenderCells(),
    
    // 可见单元格
    visibleCells,
    
    // 渲染指标
    metrics,
    
    // 工具函数
    clearCache,
    preloadCells,
    
    // 状态
    isIncrementalRenderComplete: !enableIncrementalRender || currentBatchIndex >= renderBatches.length - 1,
    renderProgress: enableIncrementalRender ? (currentBatchIndex + 1) / renderBatches.length : 1,
  };
};

// ===== 导出类型 =====

export type {
  MatrixRenderOptions,
  MatrixRenderMetrics,
  CellCacheEntry,
  RenderBatch,
};
