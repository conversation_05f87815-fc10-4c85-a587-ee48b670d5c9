/**
 * 滑动管理 Hook
 * 🎯 核心价值：简化的位置计算滑动逻辑，统一管理所有滑动需求
 * 📦 功能范围：记录当前位置，计算目标位置，执行滑动操作
 * 🔄 架构设计：基于位置差值的滑动控制，支持上移、下移、不移动三种状态
 */

'use client';

import { useCallback, useRef } from 'react';

// ===== 类型定义 =====

interface ScrollPosition {
  /** 当前滑动条位置 */
  current: number;
  /** 目标滑动条位置 */
  target: number;
  /** 位置差值：0=不移动，负数=上移，正数=下移 */
  delta: number;
}

interface ScrollManagerOptions {
  /** 滑动行为：smooth | auto */
  behavior?: ScrollBehavior;
  /** 滑动对齐方式：start | center | end */
  block?: ScrollLogicalPosition;
  /** 是否启用调试日志 */
  debug?: boolean;
}

interface UseScrollManagerReturn {
  /** 计算滑动位置差值 */
  calculateScrollDelta: (targetElement: HTMLElement, container?: HTMLElement) => ScrollPosition;
  /** 执行滑动操作 */
  scrollToTarget: (targetElement: HTMLElement, container?: HTMLElement) => boolean;
  /** 获取当前滑动位置 */
  getCurrentScrollPosition: (container?: HTMLElement) => number;
  /** 重置滑动状态 */
  resetScrollState: () => void;
}

// ===== 主Hook =====

/**
 * 滑动管理Hook
 * @param options 配置选项
 * @returns 滑动管理方法
 */
export const useScrollManager = (options: ScrollManagerOptions = {}): UseScrollManagerReturn => {
  const {
    behavior = 'smooth',
    block = 'center',
    debug = false
  } = options;

  // 记录当前滑动位置
  const currentScrollPosition = useRef<number>(0);
  const lastTargetElement = useRef<HTMLElement | null>(null);

  /**
   * 获取滚动容器
   */
  const getScrollContainer = useCallback((targetElement: HTMLElement, container?: HTMLElement): HTMLElement | null => {
    if (container) return container;
    
    // 自动查找最近的滚动容器
    let parent = targetElement.parentElement;
    while (parent) {
      const style = window.getComputedStyle(parent);
      if (style.overflowY === 'auto' || style.overflowY === 'scroll') {
        return parent;
      }
      parent = parent.parentElement;
    }
    
    return null;
  }, []);

  /**
   * 获取当前滑动位置
   */
  const getCurrentScrollPosition = useCallback((container?: HTMLElement): number => {
    if (container) {
      return container.scrollTop;
    }
    return currentScrollPosition.current;
  }, []);

  /**
   * 计算滑动位置差值
   */
  const calculateScrollDelta = useCallback((targetElement: HTMLElement, container?: HTMLElement): ScrollPosition => {
    const scrollContainer = getScrollContainer(targetElement, container);
    
    if (!scrollContainer) {
      if (debug) console.warn('[ScrollManager] 未找到滚动容器');
      return { current: 0, target: 0, delta: 0 };
    }

    // 获取当前滑动位置
    const current = scrollContainer.scrollTop;
    currentScrollPosition.current = current;

    // 获取目标元素位置信息
    const targetRect = targetElement.getBoundingClientRect();
    const containerRect = scrollContainer.getBoundingClientRect();

    // 计算目标位置（相对于容器顶部）
    let target: number;
    
    switch (block) {
      case 'start':
        target = current + (targetRect.top - containerRect.top);
        break;
      case 'end':
        target = current + (targetRect.bottom - containerRect.bottom);
        break;
      case 'center':
      default:
        const targetCenter = targetRect.top + targetRect.height / 2;
        const containerCenter = containerRect.top + containerRect.height / 2;
        target = current + (targetCenter - containerCenter);
        break;
    }

    // 确保目标位置在有效范围内
    const maxScroll = scrollContainer.scrollHeight - scrollContainer.clientHeight;
    target = Math.max(0, Math.min(target, maxScroll));

    // 计算位置差值
    const delta = target - current;

    const result: ScrollPosition = { current, target, delta };

    if (debug) {
      console.log('[ScrollManager] 位置计算:', {
        current,
        target,
        delta,
        action: delta === 0 ? '不移动' : delta < 0 ? '上移' : '下移'
      });
    }

    return result;
  }, [getScrollContainer, block, debug]);

  /**
   * 执行滑动操作
   */
  const scrollToTarget = useCallback((targetElement: HTMLElement, container?: HTMLElement): boolean => {
    const scrollContainer = getScrollContainer(targetElement, container);
    
    if (!scrollContainer) {
      if (debug) console.warn('[ScrollManager] 无法执行滑动：未找到滚动容器');
      return false;
    }

    // 计算滑动位置
    const position = calculateScrollDelta(targetElement, container);

    // 根据位置差值决定是否滑动
    if (position.delta === 0) {
      if (debug) console.log('[ScrollManager] 无需滑动：目标已在正确位置');
      return false;
    }

    // 执行滑动
    try {
      if (position.delta < 0) {
        if (debug) console.log('[ScrollManager] 执行上移滑动');
      } else {
        if (debug) console.log('[ScrollManager] 执行下移滑动');
      }

      scrollContainer.scrollTo({
        top: position.target,
        behavior
      });

      // 更新当前位置记录
      currentScrollPosition.current = position.target;
      lastTargetElement.current = targetElement;

      return true;
    } catch (error) {
      if (debug) console.error('[ScrollManager] 滑动执行失败:', error);
      return false;
    }
  }, [getScrollContainer, calculateScrollDelta, behavior, debug]);

  /**
   * 重置滑动状态
   */
  const resetScrollState = useCallback(() => {
    currentScrollPosition.current = 0;
    lastTargetElement.current = null;
    if (debug) console.log('[ScrollManager] 滑动状态已重置');
  }, [debug]);

  return {
    calculateScrollDelta,
    scrollToTarget,
    getCurrentScrollPosition,
    resetScrollState
  };
};

// ===== 导出类型 =====

export type {
  ScrollPosition,
  ScrollManagerOptions,
  UseScrollManagerReturn
};
