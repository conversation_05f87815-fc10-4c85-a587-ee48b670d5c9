/**
 * 单元格样式计算Hook
 * 🎯 核心价值：封装复杂的样式计算逻辑，提供高性能的样式缓存
 * 📦 功能范围：样式计算、缓存优化、主题支持、响应式设计
 * 🔄 架构设计：自定义Hook，支持依赖优化和记忆化
 */

import { getCellStyle } from '@/core/matrix/MatrixConfig';
import type { CellData, CellRenderData, Coordinate, MatrixConfig } from '@/core/matrix/MatrixTypes';
import { useMemo } from 'react';

// ===== 类型定义 =====

interface CellStyleOptions {
  /** 单元格坐标 */
  coordinate: Coordinate;

  /** 单元格数据 */
  cellData?: CellData;

  /** 渲染数据 */
  renderData?: CellRenderData;

  /** 矩阵配置 */
  config: MatrixConfig;

  /** 是否为增强模式 */
  isEnhanced?: boolean;

  /** 是否为填词模式活跃状态 */
  isWordInputActive?: boolean;

  /** 自定义样式 */
  customStyle?: React.CSSProperties;

  /** 主题配置 */
  theme?: {
    primaryColor?: string;
    secondaryColor?: string;
    backgroundColor?: string;
    textColor?: string;
    borderColor?: string;
  };
}

interface CellStyleResult {
  /** 计算后的完整样式 */
  style: React.CSSProperties;

  /** CSS类名 */
  className: string;

  /** 是否需要特殊处理 */
  isSpecial: boolean;

  /** 样式优先级 */
  priority: number;
}

// ===== 样式计算常量 =====

const DEFAULT_THEME = {
  primaryColor: '#007bff',
  secondaryColor: '#6c757d',
  backgroundColor: '#ffffff',
  textColor: '#000000',
  borderColor: '#dee2e6',
};

const ENHANCED_STYLE = {
  borderRadius: '50%',
  zIndex: 10,
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
  transform: 'scale(1.05)',
  transition: 'all 0.2s ease-in-out',
};

const WORD_INPUT_ACTIVE_STYLE = {
  outline: '2px solid #007bff',
  outlineOffset: '2px',
  backgroundColor: '#e3f2fd',
};

const SELECTED_STYLE = {
  backgroundColor: '#fff3cd',
  borderColor: '#ffc107',
  borderWidth: '2px',
  borderStyle: 'solid',
};

const HOVERED_STYLE = {
  backgroundColor: '#f8f9fa',
  transform: 'translateY(-1px)',
  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
};

const FOCUSED_STYLE = {
  outline: '2px solid #007bff',
  outlineOffset: '1px',
};

// ===== 主Hook =====

/**
 * 单元格样式计算Hook
 */
export const useCellStyle = (options: CellStyleOptions): CellStyleResult => {
  const {
    coordinate,
    cellData,
    renderData,
    config,
    isEnhanced = false,
    isWordInputActive = false,
    customStyle = {},
    theme = DEFAULT_THEME,
  } = options;

  return useMemo(() => {
    // 基础样式
    const baseStyle = getCellStyle(isEnhanced, coordinate);

    // 主题样式
    const themeStyle = {
      backgroundColor: theme.backgroundColor,
      color: theme.textColor,
      borderColor: theme.borderColor,
    };

    // 渲染数据样式
    const renderStyle = renderData?.style || {};

    // 状态样式
    const stateStyle: React.CSSProperties = {};

    // 选中状态
    if (cellData?.isSelected) {
      Object.assign(stateStyle, SELECTED_STYLE);
    }

    // 悬停状态
    if (cellData?.isHovered) {
      Object.assign(stateStyle, HOVERED_STYLE);
    }

    // 焦点状态
    if (cellData?.isFocused) {
      Object.assign(stateStyle, FOCUSED_STYLE);
    }

    // 填词模式活跃状态
    if (isWordInputActive) {
      Object.assign(stateStyle, WORD_INPUT_ACTIVE_STYLE);
    }

    // 增强样式（最高优先级）
    const enhancedStyle = isEnhanced ? ENHANCED_STYLE : {};

    // 合并所有样式
    const finalStyle: React.CSSProperties = {
      ...baseStyle,
      ...themeStyle,
      ...renderStyle,
      ...stateStyle,
      ...customStyle,
      ...enhancedStyle, // 确保增强样式不被覆盖
    };

    // 计算CSS类名
    const classNames = [
      renderData?.className || 'matrix-cell',
      isEnhanced && 'matrix-cell--enhanced',
      isWordInputActive && 'matrix-cell--word-input-active',
      cellData?.isSelected && 'matrix-cell--selected',
      cellData?.isHovered && 'matrix-cell--hovered',
      cellData?.isFocused && 'matrix-cell--focused',
      cellData?.level === 1 && 'matrix-cell--level-1',
      cellData?.color && `matrix-cell--color-${cellData.color}`,
    ].filter(Boolean).join(' ');

    // 计算优先级和特殊状态
    let priority = 0;
    let isSpecial = false;

    if (isEnhanced) {
      priority += 100;
      isSpecial = true;
    }

    if (isWordInputActive) {
      priority += 50;
      isSpecial = true;
    }

    if (cellData?.isSelected) {
      priority += 30;
    }

    if (cellData?.isFocused) {
      priority += 20;
    }

    if (cellData?.isHovered) {
      priority += 10;
    }

    return {
      style: finalStyle,
      className: classNames,
      isSpecial,
      priority,
    };
  }, [
    coordinate.x,
    coordinate.y,
    cellData?.isSelected,
    cellData?.isHovered,
    cellData?.isFocused,
    cellData?.level,
    cellData?.color,
    renderData?.style,
    renderData?.className,
    config.mainMode,
    config.contentMode,
    isEnhanced,
    isWordInputActive,
    customStyle,
    theme,
  ]);
};

// ===== 专用Hook =====

/**
 * 简化的单元格样式Hook
 */
export const useSimpleCellStyle = (
  x: number,
  y: number,
  isEnhanced: boolean = false,
  customStyle?: React.CSSProperties
) => {
  return useMemo(() => {
    const baseStyle = getCellStyle(isEnhanced, { x, y });
    const enhancedStyle = isEnhanced ? ENHANCED_STYLE : {};

    return {
      ...baseStyle,
      ...customStyle,
      ...enhancedStyle,
    };
  }, [x, y, isEnhanced, customStyle]);
};

/**
 * 主题样式Hook
 */
export const useThemeStyle = (theme?: CellStyleOptions['theme']) => {
  return useMemo(() => {
    const finalTheme = { ...DEFAULT_THEME, ...theme };

    return {
      '--cell-primary-color': finalTheme.primaryColor,
      '--cell-secondary-color': finalTheme.secondaryColor,
      '--cell-background-color': finalTheme.backgroundColor,
      '--cell-text-color': finalTheme.textColor,
      '--cell-border-color': finalTheme.borderColor,
    } as React.CSSProperties;
  }, [theme]);
};

/**
 * 响应式单元格样式Hook
 */
export const useResponsiveCellStyle = (
  options: CellStyleOptions,
  breakpoint?: 'sm' | 'md' | 'lg' | 'xl'
) => {
  const baseResult = useCellStyle(options);

  return useMemo(() => {
    const responsiveStyle: React.CSSProperties = { ...baseResult.style };

    // 根据断点调整样式
    switch (breakpoint) {
      case 'sm':
        responsiveStyle.fontSize = '0.75rem';
        responsiveStyle.padding = '2px';
        break;
      case 'md':
        responsiveStyle.fontSize = '0.875rem';
        responsiveStyle.padding = '4px';
        break;
      case 'lg':
        responsiveStyle.fontSize = '1rem';
        responsiveStyle.padding = '6px';
        break;
      case 'xl':
        responsiveStyle.fontSize = '1.125rem';
        responsiveStyle.padding = '8px';
        break;
    }

    return {
      ...baseResult,
      style: responsiveStyle,
    };
  }, [baseResult, breakpoint]);
};

// ===== 工具函数 =====

/**
 * 样式优先级比较函数
 */
export const compareStylePriority = (
  a: CellStyleResult,
  b: CellStyleResult
): number => {
  return b.priority - a.priority;
};

/**
 * 样式合并工具
 */
export const mergeStyles = (
  ...styles: Array<React.CSSProperties | undefined>
): React.CSSProperties => {
  return styles.reduce((merged, style) => {
    if (style) {
      return { ...merged, ...style };
    }
    return merged;
  }, {});
};

/**
 * CSS类名构建工具
 */
export const buildClassName = (
  baseClass: string,
  modifiers: Record<string, boolean>
): string => {
  const classes = [baseClass];

  Object.entries(modifiers).forEach(([modifier, condition]) => {
    if (condition) {
      classes.push(`${baseClass}--${modifier}`);
    }
  });

  return classes.join(' ');
};

// ===== 导出类型 =====

export type {
  CellStyleOptions,
  CellStyleResult
};

