/**
 * 渲染性能优化Hook
 * 🎯 核心价值：提供多种渲染优化策略，减少不必要的重渲染，提升性能
 * 📦 功能范围：虚拟化、防抖、节流、记忆化、批量更新
 * 🔄 架构设计：组合式Hook，支持多种优化策略的组合使用
 */

import { 
  useCallback, 
  useMemo, 
  useRef, 
  useState, 
  useEffect,
  startTransition,
  useDeferredValue
} from 'react';

// ===== 类型定义 =====

interface RenderOptimizationOptions {
  /** 是否启用虚拟化 */
  enableVirtualization?: boolean;
  /** 可见区域大小 */
  viewportSize?: { width: number; height: number };
  /** 缓冲区大小 */
  bufferSize?: number;
  /** 防抖延迟 */
  debounceDelay?: number;
  /** 节流间隔 */
  throttleInterval?: number;
  /** 是否启用批量更新 */
  enableBatchUpdates?: boolean;
  /** 批量更新大小 */
  batchSize?: number;
}

interface VirtualizationResult<T> {
  /** 可见项目 */
  visibleItems: T[];
  /** 开始索引 */
  startIndex: number;
  /** 结束索引 */
  endIndex: number;
  /** 总高度 */
  totalHeight: number;
  /** 偏移量 */
  offsetY: number;
}

interface PerformanceMetrics {
  /** 渲染次数 */
  renderCount: number;
  /** 平均渲染时间 */
  averageRenderTime: number;
  /** 最后渲染时间 */
  lastRenderTime: number;
  /** 跳过的渲染次数 */
  skippedRenders: number;
}

// ===== 虚拟化Hook =====

/**
 * 虚拟化Hook - 只渲染可见区域的项目
 */
export const useVirtualization = <T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  scrollTop: number = 0,
  bufferSize: number = 5
): VirtualizationResult<T> => {
  return useMemo(() => {
    const totalHeight = items.length * itemHeight;
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - bufferSize);
    const endIndex = Math.min(
      items.length - 1,
      startIndex + visibleCount + bufferSize * 2
    );
    
    const visibleItems = items.slice(startIndex, endIndex + 1);
    const offsetY = startIndex * itemHeight;
    
    return {
      visibleItems,
      startIndex,
      endIndex,
      totalHeight,
      offsetY,
    };
  }, [items, itemHeight, containerHeight, scrollTop, bufferSize]);
};

// ===== 防抖Hook =====

/**
 * 防抖Hook - 延迟执行函数直到停止调用
 */
export const useDebouncedCallback = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const timeoutRef = useRef<NodeJS.Timeout>();
  
  return useCallback(
    ((...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    }) as T,
    [callback, delay]
  );
};

/**
 * 防抖值Hook - 延迟更新值
 */
export const useDebouncedValue = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => clearTimeout(timer);
  }, [value, delay]);
  
  return debouncedValue;
};

// ===== 节流Hook =====

/**
 * 节流Hook - 限制函数执行频率
 */
export const useThrottledCallback = <T extends (...args: any[]) => any>(
  callback: T,
  interval: number
): T => {
  const lastCallRef = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout>();
  
  return useCallback(
    ((...args: Parameters<T>) => {
      const now = Date.now();
      const timeSinceLastCall = now - lastCallRef.current;
      
      if (timeSinceLastCall >= interval) {
        lastCallRef.current = now;
        callback(...args);
      } else {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        
        timeoutRef.current = setTimeout(() => {
          lastCallRef.current = Date.now();
          callback(...args);
        }, interval - timeSinceLastCall);
      }
    }) as T,
    [callback, interval]
  );
};

// ===== 批量更新Hook =====

/**
 * 批量更新Hook - 将多个更新合并为一次
 */
export const useBatchUpdates = <T>(
  batchSize: number = 10,
  flushInterval: number = 16 // ~60fps
) => {
  const [updates, setUpdates] = useState<T[]>([]);
  const timeoutRef = useRef<NodeJS.Timeout>();
  
  const addUpdate = useCallback((update: T) => {
    setUpdates(prev => {
      const newUpdates = [...prev, update];
      
      if (newUpdates.length >= batchSize) {
        // 立即刷新
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        return [];
      }
      
      // 设置定时刷新
      if (!timeoutRef.current) {
        timeoutRef.current = setTimeout(() => {
          setUpdates([]);
          timeoutRef.current = undefined;
        }, flushInterval);
      }
      
      return newUpdates;
    });
  }, [batchSize, flushInterval]);
  
  const flushUpdates = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }
    setUpdates([]);
  }, []);
  
  return {
    updates,
    addUpdate,
    flushUpdates,
    hasUpdates: updates.length > 0,
  };
};

// ===== 性能监控Hook =====

/**
 * 性能监控Hook - 监控组件渲染性能
 */
export const useRenderPerformance = (componentName?: string): PerformanceMetrics => {
  const metricsRef = useRef<PerformanceMetrics>({
    renderCount: 0,
    averageRenderTime: 0,
    lastRenderTime: 0,
    skippedRenders: 0,
  });
  
  const startTimeRef = useRef<number>(0);
  
  // 渲染开始
  useEffect(() => {
    startTimeRef.current = performance.now();
  });
  
  // 渲染结束
  useEffect(() => {
    const endTime = performance.now();
    const renderTime = endTime - startTimeRef.current;
    
    const metrics = metricsRef.current;
    metrics.renderCount++;
    metrics.lastRenderTime = renderTime;
    metrics.averageRenderTime = 
      (metrics.averageRenderTime * (metrics.renderCount - 1) + renderTime) / metrics.renderCount;
    
    if (componentName && renderTime > 16) { // 超过一帧时间
      console.warn(`${componentName} 渲染时间过长: ${renderTime.toFixed(2)}ms`);
    }
  });
  
  return metricsRef.current;
};

// ===== 智能记忆化Hook =====

/**
 * 智能记忆化Hook - 基于依赖深度比较的记忆化
 */
export const useDeepMemo = <T>(
  factory: () => T,
  deps: React.DependencyList
): T => {
  const ref = useRef<{ deps: React.DependencyList; value: T }>();
  
  if (!ref.current || !deepEqual(ref.current.deps, deps)) {
    ref.current = {
      deps,
      value: factory(),
    };
  }
  
  return ref.current.value;
};

/**
 * 深度比较函数
 */
const deepEqual = (a: any, b: any): boolean => {
  if (a === b) return true;
  
  if (a == null || b == null) return a === b;
  
  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false;
    return a.every((item, index) => deepEqual(item, b[index]));
  }
  
  if (typeof a === 'object' && typeof b === 'object') {
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    
    if (keysA.length !== keysB.length) return false;
    
    return keysA.every(key => deepEqual(a[key], b[key]));
  }
  
  return false;
};

// ===== 延迟渲染Hook =====

/**
 * 延迟渲染Hook - 使用React 18的并发特性
 */
export const useDeferredRender = <T>(value: T, delay?: number): T => {
  const deferredValue = useDeferredValue(value);
  const [displayValue, setDisplayValue] = useState(value);
  
  useEffect(() => {
    if (delay) {
      const timer = setTimeout(() => {
        startTransition(() => {
          setDisplayValue(deferredValue);
        });
      }, delay);
      
      return () => clearTimeout(timer);
    } else {
      startTransition(() => {
        setDisplayValue(deferredValue);
      });
    }
  }, [deferredValue, delay]);
  
  return displayValue;
};

// ===== 综合优化Hook =====

/**
 * 综合渲染优化Hook - 组合多种优化策略
 */
export const useRenderOptimization = <T>(
  data: T[],
  options: RenderOptimizationOptions = {}
) => {
  const {
    enableVirtualization = false,
    viewportSize = { width: 800, height: 600 },
    bufferSize = 5,
    debounceDelay = 100,
    throttleInterval = 16,
    enableBatchUpdates = false,
    batchSize = 10,
  } = options;
  
  // 性能监控
  const metrics = useRenderPerformance('RenderOptimization');
  
  // 防抖数据
  const debouncedData = useDebouncedValue(data, debounceDelay);
  
  // 延迟渲染
  const deferredData = useDeferredRender(debouncedData);
  
  // 批量更新
  const batchUpdates = useBatchUpdates(batchSize);
  
  // 虚拟化（如果启用）
  const virtualizedResult = useVirtualization(
    enableVirtualization ? deferredData : [],
    50, // 假设项目高度
    viewportSize.height,
    0,
    bufferSize
  );
  
  // 节流的更新函数
  const throttledUpdate = useThrottledCallback(
    (newData: T[]) => {
      if (enableBatchUpdates) {
        batchUpdates.addUpdate(newData);
      }
    },
    throttleInterval
  );
  
  return {
    // 优化后的数据
    optimizedData: enableVirtualization ? virtualizedResult.visibleItems : deferredData,
    
    // 虚拟化信息
    virtualization: enableVirtualization ? virtualizedResult : null,
    
    // 批量更新
    batchUpdates: enableBatchUpdates ? batchUpdates : null,
    
    // 性能指标
    metrics,
    
    // 工具函数
    throttledUpdate,
    
    // 状态
    isOptimizing: data !== deferredData,
  };
};

// ===== 导出类型 =====

export type {
  RenderOptimizationOptions,
  VirtualizationResult,
  PerformanceMetrics,
};
