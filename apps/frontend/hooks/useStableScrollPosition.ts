/**
 * 稳定滚动位置Hook
 * 🎯 核心价值：防止组件重新渲染时滚动位置被重置
 * 📦 功能范围：滚动位置保存、恢复、防抖处理
 * 🔄 架构设计：基于ref的位置记录，避免状态变化影响
 */

'use client';

import { useCallback, useEffect, useRef } from 'react';

// ===== 类型定义 =====

interface ScrollPositionState {
  scrollTop: number;
  timestamp: number;
  isScrolling: boolean;
}

interface UseStableScrollPositionOptions {
  /** 滚动容器选择器 */
  containerSelector: string;
  /** 防抖延迟时间（毫秒） */
  debounceDelay?: number;
  /** 是否启用调试日志 */
  debug?: boolean;
}

interface UseStableScrollPositionReturn {
  /** 保存当前滚动位置 */
  saveScrollPosition: () => void;
  /** 恢复滚动位置 */
  restoreScrollPosition: () => void;
  /** 获取当前滚动位置 */
  getCurrentScrollPosition: () => number;
  /** 设置滚动位置 */
  setScrollPosition: (position: number, smooth?: boolean) => void;
}

// ===== 主Hook =====

/**
 * 稳定滚动位置Hook
 * @param options 配置选项
 * @returns 滚动位置管理方法
 */
export const useStableScrollPosition = (
  options: UseStableScrollPositionOptions
): UseStableScrollPositionReturn => {
  const {
    containerSelector,
    debounceDelay = 100,
    debug = false
  } = options;

  // 使用ref保存滚动状态，避免重新渲染影响
  const scrollStateRef = useRef<ScrollPositionState>({
    scrollTop: 0,
    timestamp: 0,
    isScrolling: false
  });

  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const containerRef = useRef<HTMLElement | null>(null);

  /**
   * 获取滚动容器
   */
  const getScrollContainer = useCallback((): HTMLElement | null => {
    if (containerRef.current) {
      return containerRef.current;
    }

    const container = document.querySelector(containerSelector) as HTMLElement;
    if (container) {
      containerRef.current = container;
      return container;
    }

    return null;
  }, [containerSelector]);

  /**
   * 保存当前滚动位置
   */
  const saveScrollPosition = useCallback(() => {
    const container = getScrollContainer();
    if (container) {
      scrollStateRef.current = {
        scrollTop: container.scrollTop,
        timestamp: Date.now(),
        isScrolling: false
      };

      if (debug) {
        console.log('[useStableScrollPosition] 保存滚动位置:', scrollStateRef.current.scrollTop);
      }
    }
  }, [getScrollContainer, debug]);

  /**
   * 恢复滚动位置
   */
  const restoreScrollPosition = useCallback(() => {
    const container = getScrollContainer();
    if (container && scrollStateRef.current.scrollTop > 0) {
      // 检查时间差，避免恢复过旧的位置
      const timeDiff = Date.now() - scrollStateRef.current.timestamp;
      if (timeDiff < 5000) { // 5秒内的位置才恢复
        container.scrollTop = scrollStateRef.current.scrollTop;

        if (debug) {
          console.log('[useStableScrollPosition] 恢复滚动位置:', scrollStateRef.current.scrollTop);
        }
      }
    }
  }, [getScrollContainer, debug]);

  /**
   * 获取当前滚动位置
   */
  const getCurrentScrollPosition = useCallback((): number => {
    const container = getScrollContainer();
    return container ? container.scrollTop : 0;
  }, [getScrollContainer]);

  /**
   * 设置滚动位置
   */
  const setScrollPosition = useCallback((position: number, smooth = false) => {
    const container = getScrollContainer();
    if (container) {
      if (smooth) {
        container.scrollTo({
          top: position,
          behavior: 'smooth'
        });
      } else {
        container.scrollTop = position;
      }

      // 更新保存的位置
      scrollStateRef.current = {
        scrollTop: position,
        timestamp: Date.now(),
        isScrolling: smooth
      };

      if (debug) {
        console.log('[useStableScrollPosition] 设置滚动位置:', position, smooth ? '(平滑)' : '(立即)');
      }
    }
  }, [getScrollContainer, debug]);

  /**
   * 监听滚动事件，自动保存位置
   */
  useEffect(() => {
    const container = getScrollContainer();
    if (!container) return;

    const handleScroll = () => {
      // 清除之前的定时器
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      // 标记正在滚动
      scrollStateRef.current.isScrolling = true;

      // 防抖保存位置
      scrollTimeoutRef.current = setTimeout(() => {
        saveScrollPosition();
        scrollStateRef.current.isScrolling = false;
      }, debounceDelay);
    };

    container.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      container.removeEventListener('scroll', handleScroll);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [getScrollContainer, saveScrollPosition, debounceDelay]);

  /**
   * 组件卸载时保存位置
   */
  useEffect(() => {
    return () => {
      saveScrollPosition();
    };
  }, [saveScrollPosition]);

  return {
    saveScrollPosition,
    restoreScrollPosition,
    getCurrentScrollPosition,
    setScrollPosition
  };
};

// ===== 便捷Hook =====

/**
 * 词库滚动位置管理Hook
 * 专门用于词库面板的滚动位置管理
 */
export const useWordLibraryScrollPosition = (debug = false) => {
  return useStableScrollPosition({
    containerSelector: '.word-library-manager .overflow-y-auto',
    debounceDelay: 150,
    debug
  });
};

// ===== 导出类型 =====

export type {
  ScrollPositionState,
  UseStableScrollPositionOptions,
  UseStableScrollPositionReturn
};
