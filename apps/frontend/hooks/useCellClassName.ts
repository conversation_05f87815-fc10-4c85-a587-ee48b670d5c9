/**
 * 单元格CSS类名计算Hook
 * 🎯 核心价值：专门处理CSS类名的生成和管理，支持条件类名和BEM规范
 * 📦 功能范围：类名生成、条件类名、BEM规范、性能优化
 * 🔄 架构设计：轻量级Hook，专注类名逻辑
 */

import { useMemo } from 'react';
import type { CellData, CellRenderData, MatrixConfig } from '@/core/matrix/MatrixTypes';

// ===== 类型定义 =====

interface CellClassNameOptions {
  /** 基础类名 */
  baseClassName?: string;
  
  /** 单元格数据 */
  cellData?: CellData;
  
  /** 渲染数据 */
  renderData?: CellRenderData;
  
  /** 矩阵配置 */
  config?: MatrixConfig;
  
  /** 是否为增强模式 */
  isEnhanced?: boolean;
  
  /** 是否为填词模式活跃状态 */
  isWordInputActive?: boolean;
  
  /** 自定义类名 */
  customClassName?: string;
  
  /** 条件类名映射 */
  conditionalClasses?: Record<string, boolean>;
  
  /** 是否使用BEM规范 */
  useBEM?: boolean;
}

interface CellClassNameResult {
  /** 完整的CSS类名字符串 */
  className: string;
  
  /** 类名数组 */
  classArray: string[];
  
  /** BEM修饰符 */
  modifiers: string[];
  
  /** 状态类名 */
  stateClasses: string[];
}

// ===== 常量定义 =====

const DEFAULT_BASE_CLASS = 'matrix-cell';

const BEM_MODIFIERS = {
  enhanced: 'enhanced',
  wordInputActive: 'word-input-active',
  selected: 'selected',
  hovered: 'hovered',
  focused: 'focused',
  level1: 'level-1',
  level2: 'level-2',
  level3: 'level-3',
  colorMode: 'color-mode',
  wordMode: 'word-mode',
  numberMode: 'number-mode',
  coordinateMode: 'coordinate-mode',
} as const;

const COLOR_CLASSES = {
  red: 'color-red',
  blue: 'color-blue',
  green: 'color-green',
  yellow: 'color-yellow',
  purple: 'color-purple',
  orange: 'color-orange',
  pink: 'color-pink',
  cyan: 'color-cyan',
  black: 'color-black',
  white: 'color-white',
  gray: 'color-gray',
  brown: 'color-brown',
} as const;

// ===== 主Hook =====

/**
 * 单元格CSS类名计算Hook
 */
export const useCellClassName = (options: CellClassNameOptions): CellClassNameResult => {
  const {
    baseClassName = DEFAULT_BASE_CLASS,
    cellData,
    renderData,
    config,
    isEnhanced = false,
    isWordInputActive = false,
    customClassName,
    conditionalClasses = {},
    useBEM = true,
  } = options;

  return useMemo(() => {
    const classArray: string[] = [];
    const modifiers: string[] = [];
    const stateClasses: string[] = [];
    
    // 基础类名
    const finalBaseClass = renderData?.className || baseClassName;
    classArray.push(finalBaseClass);
    
    // BEM修饰符生成
    if (useBEM) {
      // 增强模式
      if (isEnhanced) {
        modifiers.push(BEM_MODIFIERS.enhanced);
        classArray.push(`${finalBaseClass}--${BEM_MODIFIERS.enhanced}`);
      }
      
      // 填词模式活跃状态
      if (isWordInputActive) {
        modifiers.push(BEM_MODIFIERS.wordInputActive);
        classArray.push(`${finalBaseClass}--${BEM_MODIFIERS.wordInputActive}`);
      }
      
      // 单元格状态
      if (cellData) {
        // 选中状态
        if (cellData.isSelected) {
          modifiers.push(BEM_MODIFIERS.selected);
          classArray.push(`${finalBaseClass}--${BEM_MODIFIERS.selected}`);
          stateClasses.push('selected');
        }
        
        // 悬停状态
        if (cellData.isHovered) {
          modifiers.push(BEM_MODIFIERS.hovered);
          classArray.push(`${finalBaseClass}--${BEM_MODIFIERS.hovered}`);
          stateClasses.push('hovered');
        }
        
        // 焦点状态
        if (cellData.isFocused) {
          modifiers.push(BEM_MODIFIERS.focused);
          classArray.push(`${finalBaseClass}--${BEM_MODIFIERS.focused}`);
          stateClasses.push('focused');
        }
        
        // 级别类名
        if (cellData.level) {
          const levelModifier = `level-${cellData.level}` as keyof typeof BEM_MODIFIERS;
          if (BEM_MODIFIERS[levelModifier]) {
            modifiers.push(BEM_MODIFIERS[levelModifier]);
            classArray.push(`${finalBaseClass}--${BEM_MODIFIERS[levelModifier]}`);
          }
        }
        
        // 颜色类名
        if (cellData.color && COLOR_CLASSES[cellData.color as keyof typeof COLOR_CLASSES]) {
          const colorClass = COLOR_CLASSES[cellData.color as keyof typeof COLOR_CLASSES];
          modifiers.push(colorClass);
          classArray.push(`${finalBaseClass}--${colorClass}`);
        }
      }
      
      // 配置相关类名
      if (config) {
        // 主模式
        if (config.mainMode) {
          const modeModifier = `${config.mainMode}Mode` as keyof typeof BEM_MODIFIERS;
          if (BEM_MODIFIERS[modeModifier]) {
            modifiers.push(BEM_MODIFIERS[modeModifier]);
            classArray.push(`${finalBaseClass}--${BEM_MODIFIERS[modeModifier]}`);
          }
        }
      }
    } else {
      // 非BEM模式，使用简单类名
      if (isEnhanced) {
        classArray.push('enhanced');
        stateClasses.push('enhanced');
      }
      
      if (isWordInputActive) {
        classArray.push('word-input-active');
        stateClasses.push('word-input-active');
      }
      
      if (cellData?.isSelected) {
        classArray.push('selected');
        stateClasses.push('selected');
      }
      
      if (cellData?.isHovered) {
        classArray.push('hovered');
        stateClasses.push('hovered');
      }
      
      if (cellData?.isFocused) {
        classArray.push('focused');
        stateClasses.push('focused');
      }
      
      if (cellData?.level) {
        classArray.push(`level-${cellData.level}`);
      }
      
      if (cellData?.color) {
        classArray.push(`color-${cellData.color}`);
      }
    }
    
    // 条件类名
    Object.entries(conditionalClasses).forEach(([className, condition]) => {
      if (condition) {
        classArray.push(className);
      }
    });
    
    // 自定义类名
    if (customClassName) {
      classArray.push(customClassName);
    }
    
    // 去重并过滤空值
    const uniqueClasses = Array.from(new Set(classArray.filter(Boolean)));
    
    return {
      className: uniqueClasses.join(' '),
      classArray: uniqueClasses,
      modifiers,
      stateClasses,
    };
  }, [
    baseClassName,
    renderData?.className,
    cellData?.isSelected,
    cellData?.isHovered,
    cellData?.isFocused,
    cellData?.level,
    cellData?.color,
    config?.mainMode,
    isEnhanced,
    isWordInputActive,
    customClassName,
    conditionalClasses,
    useBEM,
  ]);
};

// ===== 专用Hook =====

/**
 * 简化的类名Hook
 */
export const useSimpleCellClassName = (
  baseClass: string = DEFAULT_BASE_CLASS,
  modifiers: string[] = []
) => {
  return useMemo(() => {
    const classes = [baseClass, ...modifiers].filter(Boolean);
    return classes.join(' ');
  }, [baseClass, modifiers]);
};

/**
 * 条件类名Hook
 */
export const useConditionalClassName = (
  baseClass: string,
  conditions: Record<string, boolean>
) => {
  return useMemo(() => {
    const classes = [baseClass];
    
    Object.entries(conditions).forEach(([className, condition]) => {
      if (condition) {
        classes.push(className);
      }
    });
    
    return classes.join(' ');
  }, [baseClass, conditions]);
};

/**
 * BEM类名生成Hook
 */
export const useBEMClassName = (
  block: string,
  element?: string,
  modifiers: string[] = []
) => {
  return useMemo(() => {
    let baseClass = block;
    
    if (element) {
      baseClass += `__${element}`;
    }
    
    const classes = [baseClass];
    
    modifiers.forEach(modifier => {
      if (modifier) {
        classes.push(`${baseClass}--${modifier}`);
      }
    });
    
    return classes.join(' ');
  }, [block, element, modifiers]);
};

// ===== 工具函数 =====

/**
 * 类名数组转字符串
 */
export const classArrayToString = (classes: (string | undefined | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};

/**
 * 条件类名构建器
 */
export const conditionalClass = (
  className: string,
  condition: boolean
): string | undefined => {
  return condition ? className : undefined;
};

/**
 * 类名合并工具
 */
export const mergeClassNames = (...classNames: (string | undefined)[]): string => {
  return classNames.filter(Boolean).join(' ');
};

/**
 * BEM修饰符生成器
 */
export const createBEMModifier = (
  block: string,
  element: string | undefined,
  modifier: string
): string => {
  const base = element ? `${block}__${element}` : block;
  return `${base}--${modifier}`;
};

/**
 * 类名验证器
 */
export const isValidClassName = (className: string): boolean => {
  // CSS类名验证正则：字母、数字、连字符、下划线
  const validClassNameRegex = /^[a-zA-Z][\w-]*$/;
  return validClassNameRegex.test(className);
};

// ===== 导出类型 =====

export type {
  CellClassNameOptions,
  CellClassNameResult,
};

// ===== 导出常量 =====

export {
  DEFAULT_BASE_CLASS,
  BEM_MODIFIERS,
  COLOR_CLASSES,
};
