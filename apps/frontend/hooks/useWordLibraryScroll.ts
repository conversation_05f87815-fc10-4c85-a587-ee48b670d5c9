/**
 * 词库滑动 Hook
 * 🎯 核心价值：简化词库滑动功能的使用，提供React Hook接口
 * 📦 功能范围：封装滑动服务，提供便捷的Hook接口
 * 🔄 架构设计：基于滑动服务的React Hook封装
 */

'use client';

import type { WordLibraryKey } from '@/core/matrix/MatrixTypes';
import {
  getWordLibraryScrollService,
  resetWordLibraryScrollState,
  triggerWordLibraryScroll,
  type ScrollResult,
  type ScrollTriggerOptions
} from '@/core/services/WordLibraryScrollService';
import { useCallback, useEffect, useRef } from 'react';

// ===== 类型定义 =====

interface UseWordLibraryScrollOptions extends ScrollTriggerOptions {
  /** 是否自动初始化滚动容器 */
  autoInitContainer?: boolean;
}

interface UseWordLibraryScrollReturn {
  /** 触发滑动到指定词库 */
  scrollToLibrary: (libraryKey: WordLibraryKey, options?: ScrollTriggerOptions) => ScrollResult;
  /** 重置滑动状态 */
  resetScrollState: () => void;
  /** 获取当前激活的词库 */
  getCurrentActiveLibrary: () => WordLibraryKey | null;
  /** 设置当前激活的词库（不触发滑动） */
  setCurrentActiveLibrary: (libraryKey: WordLibraryKey | null) => void;
}

// ===== 主Hook =====

/**
 * 词库滑动Hook
 * @param options 配置选项
 * @returns 滑动控制方法
 */
export const useWordLibraryScroll = (
  options: UseWordLibraryScrollOptions = {}
): UseWordLibraryScrollReturn => {
  const {
    autoInitContainer = true,
    behavior = 'smooth',
    block = 'center',
    force = false,
    debug = false
  } = options;

  const scrollService = useRef(getWordLibraryScrollService());
  const containerInitialized = useRef(false);

  // 自动初始化滚动容器
  useEffect(() => {
    if (autoInitContainer && !containerInitialized.current) {
      let retryCount = 0;
      const maxRetries = 10;

      const tryInitialize = () => {
        const container = document.querySelector('.word-library-manager .overflow-y-auto') as HTMLElement;
        if (container) {
          scrollService.current.initializeContainer(container);
          containerInitialized.current = true;
          if (debug) console.log('[useWordLibraryScroll] 滚动容器已初始化');
        } else {
          retryCount++;
          if (retryCount < maxRetries) {
            if (debug) console.log(`[useWordLibraryScroll] 容器未找到，重试 ${retryCount}/${maxRetries}`);
            setTimeout(tryInitialize, 200);
          } else {
            if (debug) console.warn('[useWordLibraryScroll] 容器初始化失败，已达到最大重试次数');
          }
        }
      };

      // 延迟初始化，确保DOM已渲染
      const timer = setTimeout(tryInitialize, 100);

      return () => clearTimeout(timer);
    }
  }, [autoInitContainer, debug]);

  /**
   * 触发滑动到指定词库
   */
  const scrollToLibrary = useCallback((
    libraryKey: WordLibraryKey,
    overrideOptions?: ScrollTriggerOptions
  ): ScrollResult => {
    const finalOptions: ScrollTriggerOptions = {
      behavior,
      block,
      force,
      debug,
      ...overrideOptions
    };

    return triggerWordLibraryScroll(libraryKey, finalOptions);
  }, [behavior, block, force, debug]);

  /**
   * 重置滑动状态
   */
  const resetScrollState = useCallback(() => {
    resetWordLibraryScrollState();
    if (debug) console.log('[useWordLibraryScroll] 滑动状态已重置');
  }, [debug]);

  /**
   * 获取当前激活的词库
   */
  const getCurrentActiveLibrary = useCallback(() => {
    return scrollService.current.getCurrentActiveLibrary();
  }, []);

  /**
   * 设置当前激活的词库（不触发滑动）
   */
  const setCurrentActiveLibrary = useCallback((libraryKey: WordLibraryKey | null) => {
    scrollService.current.setCurrentActiveLibrary(libraryKey);
    if (debug) console.log('[useWordLibraryScroll] 当前激活词库已设置:', libraryKey);
  }, [debug]);

  return {
    scrollToLibrary,
    resetScrollState,
    getCurrentActiveLibrary,
    setCurrentActiveLibrary
  };
};

// ===== 便捷Hook =====

/**
 * 自动滑动Hook - 监听词库激活状态并自动滑动
 * @param isActive 是否激活
 * @param libraryKey 词库键
 * @param options 配置选项
 */
export const useAutoWordLibraryScroll = (
  isActive: boolean,
  libraryKey: WordLibraryKey | null,
  options: UseWordLibraryScrollOptions = {}
): void => {
  const { scrollToLibrary } = useWordLibraryScroll(options);
  const { debug = false } = options;

  useEffect(() => {
    if (debug) {
      console.log('[useAutoWordLibraryScroll] 状态变化:', {
        isActive,
        libraryKey,
        shouldTriggerScroll: isActive && libraryKey
      });
    }

    if (isActive && libraryKey) {
      if (debug) {
        console.log('[useAutoWordLibraryScroll] 触发滑动:', libraryKey);
      }
      const result = scrollToLibrary(libraryKey);
      if (debug) {
        console.log('[useAutoWordLibraryScroll] 滑动结果:', result);
      }
    }
  }, [isActive, libraryKey, scrollToLibrary, debug]);
};

// ===== 导出类型 =====

export type {
  UseWordLibraryScrollOptions,
  UseWordLibraryScrollReturn
};
