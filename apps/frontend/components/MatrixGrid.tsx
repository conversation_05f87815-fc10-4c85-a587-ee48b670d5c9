/**
 * 矩阵网格组件
 * 🎯 核心价值：管理整个矩阵网格的渲染，协调单元格组件
 * 📦 功能范围：网格布局、单元格管理、批量渲染优化
 * 🔄 架构设计：容器组件，负责数据分发和事件聚合
 */

'use client';

import type {
  CellData,
  CellRenderData,
  Coordinate,
  MatrixConfig,
  MatrixData
} from '@/core/matrix/MatrixTypes';
import { MATRIX_SIZE } from '@/core/matrix/MatrixTypes';
import { CellStateChecker } from '@/core/utils/ConditionalLogicUtils';
import { useMatrixRenderOptimization } from '@/hooks/useMatrixRenderOptimization';
import React, { memo, useCallback, useMemo } from 'react';
import { MatrixCell } from './MatrixCell';

// ===== 组件属性接口 =====

interface MatrixGridProps {
  /** 矩阵数据 */
  matrixData: MatrixData;

  /** 矩阵配置 */
  config: MatrixConfig;

  /** 获取单元格渲染数据的函数 */
  getCellRenderData: (x: number, y: number) => CellRenderData | undefined;

  /** 填词模式状态 */
  wordInputState?: {
    isActive: boolean;
    selectedCell?: Coordinate | null;
    temporaryWord?: string;
  };

  /** 网格容器样式 */
  style?: React.CSSProperties;

  /** 网格容器CSS类名 */
  className?: string;

  /** 渲染优化选项 */
  enableRenderOptimization?: boolean;

  /** 交互事件回调 */
  onCellClick?: (x: number, y: number, event: React.MouseEvent) => void;
  onCellDoubleClick?: (x: number, y: number, event: React.MouseEvent) => void;
  onCellMouseEnter?: (x: number, y: number, event: React.MouseEvent) => void;
  onCellMouseLeave?: (x: number, y: number, event: React.MouseEvent) => void;
  onCellFocus?: (x: number, y: number, event: React.FocusEvent) => void;
  onCellBlur?: (x: number, y: number, event: React.FocusEvent) => void;
}

// ===== 辅助函数 =====

/**
 * 检查单元格是否为增强模式
 */
const isCellEnhanced = (
  cellData: CellData | undefined,
  config: MatrixConfig
): boolean => {
  return CellStateChecker.isEnhanced(config, cellData);
};

/**
 * 检查单元格是否为填词模式下的活跃单元格
 */
const isCellWordInputActive = (
  x: number,
  y: number,
  wordInputState?: MatrixGridProps['wordInputState']
): boolean => {
  return CellStateChecker.isWordInputActive(x, y, wordInputState);
};

/**
 * 生成网格样式
 */
const getGridStyle = (customStyle?: React.CSSProperties): React.CSSProperties => {
  return {
    display: 'grid',
    gridTemplateColumns: `repeat(${MATRIX_SIZE}, 1fr)`,
    gridTemplateRows: `repeat(${MATRIX_SIZE}, 1fr)`,
    gap: '1px',
    width: '100%',
    height: '100%',
    ...customStyle,
  };
};

// ===== 主组件 =====

const MatrixGridComponent: React.FC<MatrixGridProps> = ({
  matrixData,
  config,
  getCellRenderData,
  wordInputState,
  style: customStyle,
  className = 'matrix-grid',
  enableRenderOptimization = true,
  onCellClick,
  onCellDoubleClick,
  onCellMouseEnter,
  onCellMouseLeave,
  onCellFocus,
  onCellBlur,
}) => {
  // 渲染优化
  const renderOptimization = useMatrixRenderOptimization(
    matrixData,
    config,
    getCellRenderData,
    {
      enableViewportCulling: enableRenderOptimization,
      enableCellCache: enableRenderOptimization,
      enableIncrementalRender: false, // 对于矩阵来说，增量渲染可能不太适用
      debounceDelay: 50,
    }
  );

  // 计算网格样式
  const gridStyle = useMemo(() => getGridStyle(customStyle), [customStyle]);

  // 生成单元格数据（使用优化后的渲染单元格列表）
  const cellsData = useMemo(() => {
    const cells: Array<{
      x: number;
      y: number;
      key: string;
      cellData?: CellData;
      renderData?: CellRenderData;
      isEnhanced: boolean;
      isWordInputActive: boolean;
    }> = [];

    // 使用优化后的渲染单元格列表
    const cellsToRender = enableRenderOptimization
      ? renderOptimization.currentRenderCells
      : Array.from({ length: MATRIX_SIZE * MATRIX_SIZE }, (_, i) => ({
        x: i % MATRIX_SIZE,
        y: Math.floor(i / MATRIX_SIZE),
        priority: 0
      }));

    cellsToRender.forEach(({ x, y }) => {
      const key = `${x},${y}`;
      const cellData = matrixData.cells.get(key);
      const renderData = enableRenderOptimization
        ? renderOptimization.getOptimizedRenderData(x, y)
        : getCellRenderData(x, y);
      const isEnhanced = isCellEnhanced(cellData, config);
      const isWordInputActive = isCellWordInputActive(x, y, wordInputState);

      cells.push({
        x,
        y,
        key,
        cellData,
        renderData,
        isEnhanced,
        isWordInputActive,
      });
    });

    return cells;
  }, [
    matrixData.cells,
    config,
    getCellRenderData,
    wordInputState,
    enableRenderOptimization,
    renderOptimization
  ]);

  // 事件处理器
  const handleCellClick = useCallback((x: number, y: number, event: React.MouseEvent) => {
    onCellClick?.(x, y, event);
  }, [onCellClick]);

  const handleCellDoubleClick = useCallback((x: number, y: number, event: React.MouseEvent) => {
    onCellDoubleClick?.(x, y, event);
  }, [onCellDoubleClick]);

  const handleCellMouseEnter = useCallback((x: number, y: number, event: React.MouseEvent) => {
    onCellMouseEnter?.(x, y, event);
  }, [onCellMouseEnter]);

  const handleCellMouseLeave = useCallback((x: number, y: number, event: React.MouseEvent) => {
    onCellMouseLeave?.(x, y, event);
  }, [onCellMouseLeave]);

  const handleCellFocus = useCallback((x: number, y: number, event: React.FocusEvent) => {
    onCellFocus?.(x, y, event);
  }, [onCellFocus]);

  const handleCellBlur = useCallback((x: number, y: number, event: React.FocusEvent) => {
    onCellBlur?.(x, y, event);
  }, [onCellBlur]);

  // 渲染单元格
  const renderCells = useMemo(() => {
    return cellsData.map(({ x, y, key, cellData, renderData, isEnhanced, isWordInputActive }) => (
      <MatrixCell
        key={key}
        x={x}
        y={y}
        cellData={cellData}
        renderData={renderData}
        config={config}
        isEnhanced={isEnhanced}
        isWordInputActive={isWordInputActive}
        temporaryWord={wordInputState?.temporaryWord}
        onClick={handleCellClick}
        onDoubleClick={handleCellDoubleClick}
        onMouseEnter={handleCellMouseEnter}
        onMouseLeave={handleCellMouseLeave}
        onFocus={handleCellFocus}
        onBlur={handleCellBlur}
      />
    ));
  }, [
    cellsData,
    config,
    wordInputState?.temporaryWord,
    handleCellClick,
    handleCellDoubleClick,
    handleCellMouseEnter,
    handleCellMouseLeave,
    handleCellFocus,
    handleCellBlur,
  ]);

  return (
    <div
      className={className}
      style={gridStyle}
      role="grid"
      aria-label={`${MATRIX_SIZE}x${MATRIX_SIZE} 矩阵网格`}
    >
      {renderCells}
    </div>
  );
};

// ===== 性能优化 =====

/**
 * 自定义比较函数
 */
const arePropsEqual = (
  prevProps: MatrixGridProps,
  nextProps: MatrixGridProps
): boolean => {
  // 基础属性比较
  if (
    prevProps.matrixData !== nextProps.matrixData ||
    prevProps.config !== nextProps.config ||
    prevProps.getCellRenderData !== nextProps.getCellRenderData ||
    prevProps.className !== nextProps.className
  ) {
    return false;
  }

  // 填词状态比较
  const prevWordState = prevProps.wordInputState;
  const nextWordState = nextProps.wordInputState;

  if (prevWordState !== nextWordState) {
    if (
      prevWordState?.isActive !== nextWordState?.isActive ||
      prevWordState?.temporaryWord !== nextWordState?.temporaryWord ||
      JSON.stringify(prevWordState?.selectedCell) !== JSON.stringify(nextWordState?.selectedCell)
    ) {
      return false;
    }
  }

  // 样式比较
  if (JSON.stringify(prevProps.style) !== JSON.stringify(nextProps.style)) {
    return false;
  }

  // 事件处理器比较（通常这些会变化，但我们假设它们是稳定的）
  if (
    prevProps.onCellClick !== nextProps.onCellClick ||
    prevProps.onCellDoubleClick !== nextProps.onCellDoubleClick ||
    prevProps.onCellMouseEnter !== nextProps.onCellMouseEnter ||
    prevProps.onCellMouseLeave !== nextProps.onCellMouseLeave ||
    prevProps.onCellFocus !== nextProps.onCellFocus ||
    prevProps.onCellBlur !== nextProps.onCellBlur
  ) {
    return false;
  }

  return true;
};

// ===== 导出组件 =====

export const MatrixGrid = memo(MatrixGridComponent, arePropsEqual);

// ===== 导出类型 =====

export type { MatrixGridProps };

// ===== 导出辅助函数 =====

export {
  getGridStyle, isCellEnhanced,
  isCellWordInputActive
};

