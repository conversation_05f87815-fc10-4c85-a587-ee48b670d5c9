/**
 * 矩阵主组件 - 重构版本
 * 🎯 核心价值：协调器组件，管理数据流和事件处理，使用组合模式
 * 📦 功能范围：数据管理、事件协调、组件组合
 * 🔄 架构设计：容器组件，将渲染职责委托给专用组件
 */

'use client';

import { getContainerStyle, getViewportStyle } from '@/core/matrix/MatrixConfig';
import { matrixCore } from '@/core/matrix/MatrixCore';
import { useMatrixConfig, useMatrixData, useMatrixStore } from '@/core/matrix/MatrixStore';
import type {
  BusinessMode,
  Coordinate,
  MatrixConfig,
} from '@/core/matrix/MatrixTypes';
import { useWordInputStore } from '@/core/wordLibrary/WordLibraryStore';
import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { createInteractionEvent } from '@/core/matrix/MatrixCore';
import { MatrixGrid } from './MatrixGrid';

// ===== 组件属性 =====

interface MatrixProps {
  /** 自定义配置覆盖 */
  configOverride?: Partial<MatrixConfig>;

  /** 容器样式 */
  className?: string;
  style?: React.CSSProperties;

  /** 交互事件回调 */
  onCellClick?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellDoubleClick?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellHover?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onCellFocus?: (coordinate: Coordinate, event: React.FocusEvent) => void;
  onModeChange?: (mode: BusinessMode) => void;
}

// ===== 主组件 =====

const MatrixComponent: React.FC<MatrixProps> = ({
  configOverride,
  className = '',
  style,
  onCellClick,
  onCellDoubleClick,
  onCellFocus,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const isInitialized = useRef(false);
  const [isClient, setIsClient] = useState(false);

  // 获取状态
  const matrixData = useMatrixData();
  const matrixConfig = useMatrixConfig();
  const {
    initializeMatrix,
    selectCell,
    // hoverCell, // 已禁用：防止触发词库滑动
    focusCell,
    getCellRenderData,
  } = useMatrixStore();

  // 获取填词模式状态
  const { isActive: isWordInputActive, selectedCell, temporaryWord } = useWordInputStore();

  // 合并配置 - 使用useMemo优化
  const finalConfig = useMemo(() => ({
    ...matrixConfig,
    ...configOverride,
  }), [matrixConfig, configOverride]);

  // 确保客户端渲染一致性
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 初始化矩阵数据
  useEffect(() => {
    if (isClient && !isInitialized.current && !matrixData) {
      initializeMatrix();
      isInitialized.current = true;
    }
  }, [isClient, matrixData, initializeMatrix]);

  // 初始化
  useEffect(() => {
    if (!isInitialized.current && containerRef.current && isClient) {
      // 初始化矩阵数据
      if (matrixData && matrixData.cells.size === 0) {
        initializeMatrix();
      }
      isInitialized.current = true;
    }
  }, [initializeMatrix, matrixData?.cells.size, isClient]);

  // 准备填词状态数据
  const wordInputState = {
    isActive: isWordInputActive,
    selectedCell,
    temporaryWord: temporaryWord || undefined,
  };

  // 处理单元格点击 - 完全避免不必要的状态更新
  const handleCellClick = useCallback((x: number, y: number, event: React.MouseEvent) => {
    const coordinate = { x, y };

    // 如果当前是填词模式，完全跳过状态更新，避免词库面板重置
    if (isWordInputActive) {
      console.log('[Matrix] 填词模式下跳过状态更新，避免词库面板重置');
      // 仍然调用外部回调，但不更新任何状态
      onCellClick?.(coordinate, event);
      return;
    }

    // 只有在非填词模式下才更新状态
    selectCell(x, y, event.ctrlKey || event.metaKey);

    // 创建交互事件
    const interactionEvent = createInteractionEvent('click', coordinate, {
      ctrl: event.ctrlKey,
      shift: event.shiftKey,
      alt: event.altKey,
    });

    // 处理业务逻辑
    const cell = matrixData?.cells.get(`${x},${y}`);
    if (cell) {
      matrixCore.handleInteraction(interactionEvent, cell, finalConfig);
    }

    // 调用外部回调
    onCellClick?.(coordinate, event);
  }, [matrixData?.cells, finalConfig, selectCell, onCellClick, isWordInputActive]);

  // 处理双击
  const handleCellDoubleClick = useCallback((x: number, y: number, event: React.MouseEvent) => {
    const coordinate = { x, y };
    onCellDoubleClick?.(coordinate, event);
  }, [onCellDoubleClick]);

  // 处理悬停 - 已禁用：防止触发词库滑动
  // const handleCellMouseEnter = useCallback((event: React.MouseEvent) => {
  //   const target = event.target as HTMLElement;
  //   const x = parseInt(target.dataset.x || '0', 10);
  //   const y = parseInt(target.dataset.y || '0', 10);

  //   if (!isNaN(x) && !isNaN(y)) {
  //     const coordinate = { x, y };
  //     hoverCell(x, y);
  //     onCellHover?.(coordinate, event);
  //   }
  // }, [hoverCell, onCellHover]);

  // 处理焦点
  const handleCellFocus = useCallback((x: number, y: number, event: React.FocusEvent) => {
    const coordinate = { x, y };
    focusCell(x, y);
    onCellFocus?.(coordinate, event);
  }, [focusCell, onCellFocus]);



  // 使用统一的样式配置
  const viewportStyle = getViewportStyle(style);
  const containerStyle = getContainerStyle();



  // 在客户端渲染完成前显示占位符
  if (!isClient) {
    return (
      <div
        className={`matrix-viewport ${className}`}
        style={{ ...viewportStyle, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
      >
        <div className="text-gray-500">矩阵加载中...</div>
      </div>
    );
  }

  return (
    <div
      className={`matrix-viewport ${className}`}
      style={viewportStyle}
    >
      <div
        ref={containerRef}
        className="matrix-container"
        style={containerStyle}
      >
        {matrixData && (
          <MatrixGrid
            matrixData={matrixData}
            config={finalConfig}
            getCellRenderData={getCellRenderData}
            wordInputState={wordInputState}
            onCellClick={handleCellClick}
            onCellDoubleClick={handleCellDoubleClick}
            onCellFocus={handleCellFocus}
          />
        )}
      </div>
    </div>
  );
};

// ===== 性能优化 =====

const Matrix = memo(MatrixComponent);

Matrix.displayName = 'Matrix';

export default Matrix;
