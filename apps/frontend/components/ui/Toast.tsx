/**
 * Toast通知组件
 * 🎯 核心价值：单个Toast消息的渲染组件
 * 📦 功能范围：显示消息、动画效果、手动关闭
 * 🔄 架构设计：轻量级、可复用、支持多种类型
 */

'use client';

import React, { useEffect, useState } from 'react';
import type { ToastMessage, ToastType } from '@/core/ui/ToastStore';

// ===== 类型定义 =====

export interface ToastProps {
  /** Toast消息数据 */
  toast: ToastMessage;
  /** 关闭回调 */
  onClose: (id: string) => void;
  /** 在列表中的索引（用于动画延迟） */
  index?: number;
}

// ===== 样式配置 =====

const toastStyles: Record<ToastType, string> = {
  success: 'bg-gray-900 border-gray-700 text-white',
  error: 'bg-black border-gray-800 text-white',
  warning: 'bg-gray-100 border-gray-300 text-gray-800',
  info: 'bg-white border-gray-200 text-gray-700'
};

const toastIcons: Record<ToastType, string> = {
  success: '✓',
  error: '✕',
  warning: '⚠',
  info: 'ℹ'
};

const iconStyles: Record<ToastType, string> = {
  success: 'bg-gray-700 text-white',
  error: 'bg-gray-900 text-white',
  warning: 'bg-gray-200 text-gray-700',
  info: 'bg-gray-50 text-gray-600'
};

// ===== Toast组件 =====

const Toast: React.FC<ToastProps> = ({
  toast,
  onClose,
  index = 0
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  // 进入动画
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, index * 100); // 错开动画时间

    return () => clearTimeout(timer);
  }, [index]);

  // 处理关闭
  const handleClose = () => {
    setIsLeaving(true);
    setTimeout(() => {
      onClose(toast.id);
    }, 300); // 等待动画完成
  };

  // 根据位置调整动画方向
  const isCenter = toast.position === 'center';
  const animationClass = isCenter
    ? (isVisible && !isLeaving
        ? 'scale-100 opacity-100'
        : 'scale-95 opacity-0')
    : (isVisible && !isLeaving
        ? 'translate-x-0 opacity-100 scale-100'
        : 'translate-x-full opacity-0 scale-95');

  return (
    <div
      className={`
        transform transition-all duration-300 ease-in-out
        ${isCenter ? '' : 'mb-3'}
        ${animationClass}
        ${toast.className || ''}
      `}
    >
      <div
        className={`
          px-4 py-3 rounded-lg border shadow-lg backdrop-blur-sm
          ${isCenter ? 'max-w-xs' : 'max-w-sm'} w-full
          ${toastStyles[toast.type]}
        `}
      >
        <div className="flex items-start space-x-3">
          {/* 图标 */}
          <div className={`
            w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5
            ${iconStyles[toast.type]}
          `}>
            <span className="text-sm font-bold">
              {toastIcons[toast.type]}
            </span>
          </div>

          {/* 消息内容 */}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium leading-5">
              {toast.message}
            </p>
          </div>

          {/* 关闭按钮 */}
          {toast.closable && (
            <button
              onClick={handleClose}
              className="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="关闭"
            >
              <span className="text-lg leading-none">×</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Toast;
