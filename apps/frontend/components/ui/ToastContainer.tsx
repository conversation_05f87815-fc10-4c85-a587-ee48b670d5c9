/**
 * Toast容器组件
 * 🎯 核心价值：全局Toast消息容器，管理多个Toast的显示
 * 📦 功能范围：Toast布局管理、动画协调、全局渲染
 * 🔄 架构设计：基于Zustand状态的全局容器
 */

'use client';

import React from 'react';
import { useToastStore, type ToastPosition } from '@/core/ui/ToastStore';
import Toast from './Toast';

// ===== 类型定义 =====

export interface ToastContainerProps {
  /** 最大显示数量 */
  maxToasts?: number;
  /** 自定义类名 */
  className?: string;
}

// ===== 位置样式配置 =====

const positionStyles: Record<ToastPosition, string> = {
  'top-right': 'top-4 right-4',
  'top-left': 'top-4 left-4',
  'bottom-right': 'bottom-4 right-4',
  'bottom-left': 'bottom-4 left-4',
  'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
  'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2',
  'center': 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2',
};

// ===== ToastContainer组件 =====

const ToastContainer: React.FC<ToastContainerProps> = ({
  maxToasts = 5,
  className = ''
}) => {
  const { toasts, removeToast } = useToastStore();

  // 按位置分组toast
  const toastsByPosition = toasts.reduce((acc, toast) => {
    if (!acc[toast.position]) {
      acc[toast.position] = [];
    }
    acc[toast.position].push(toast);
    return acc;
  }, {} as Record<ToastPosition, typeof toasts>);

  // 如果没有toast，不渲染
  if (toasts.length === 0) {
    return null;
  }

  return (
    <>
      {Object.entries(toastsByPosition).map(([position, positionToasts]) => {
        // 限制每个位置的显示数量
        const visibleToasts = positionToasts.slice(-maxToasts);

        return (
          <div
            key={position}
            className={`
              fixed pointer-events-none
              ${positionStyles[position as ToastPosition]}
              ${position === 'center' ? 'z-[60]' : 'z-50'}
              ${className}
            `}
            aria-live="polite"
            aria-label="通知消息"
          >
            <div className={`
              flex flex-col space-y-2 pointer-events-auto
              ${position === 'center' ? 'items-center' : ''}
            `}>
              {visibleToasts.map((toast, index) => (
                <Toast
                  key={toast.id}
                  toast={toast}
                  onClose={removeToast}
                  index={index}
                />
              ))}
            </div>
          </div>
        );
      })}
    </>
  );
};

export default ToastContainer;
