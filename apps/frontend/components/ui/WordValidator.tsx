/**
 * 词语验证器组件
 * 🎯 核心价值：实时词语验证和重复检测，提供视觉反馈
 * 📦 功能范围：格式验证、重复检测、错误提示、批量验证
 * 🔄 架构设计：独立的验证组件，可复用于不同场景
 */

'use client';

import React, { memo, useEffect, useState } from 'react';
import type { 
  WordValidationResult, 
  WordLibraryKey,
  BasicColorType,
  DataLevel
} from '@/core/matrix/MatrixTypes';
import { 
  validateWord,
  getWordLibraryDisplayName,
  WORD_LENGTH_LIMITS
} from '@/core/wordLibrary/WordLibraryCore';
import { useWordLibraryStore } from '@/core/wordLibrary/WordLibraryStore';

// ===== 组件属性 =====

interface WordValidatorProps {
  /** 要验证的词语 */
  word: string;
  /** 词库标识 */
  libraryKey: WordLibraryKey;
  /** 验证结果回调 */
  onValidation?: (result: WordValidationResult) => void;
  /** 是否显示详细信息 */
  showDetails?: boolean;
  /** 自定义类名 */
  className?: string;
}

// ===== 验证状态图标 =====

const ValidationIcon: React.FC<{ 
  isValid: boolean; 
  isDuplicate: boolean; 
  size?: number 
}> = ({ isValid, isDuplicate, size = 16 }) => {
  if (!isValid) {
    return (
      <svg width={size} height={size} viewBox="0 0 16 16" className="text-red-500">
        <circle cx="8" cy="8" r="7" fill="currentColor" opacity="0.2"/>
        <path d="M8 4v4M8 10h.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
      </svg>
    );
  }
  
  if (isDuplicate) {
    return (
      <svg width={size} height={size} viewBox="0 0 16 16" className="text-orange-500">
        <circle cx="8" cy="8" r="7" fill="currentColor" opacity="0.2"/>
        <path d="M8 4v4M8 10h.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
      </svg>
    );
  }
  
  return (
    <svg width={size} height={size} viewBox="0 0 16 16" className="text-green-500">
      <circle cx="8" cy="8" r="7" fill="currentColor" opacity="0.2"/>
      <path d="M6 8l2 2 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );
};

// ===== 验证详情组件 =====

const ValidationDetails: React.FC<{ 
  result: WordValidationResult;
  word: string;
}> = ({ result, word }) => {
  if (result.isValid && !result.isDuplicate) {
    return (
      <div className="text-xs text-green-600 mt-1">
        ✓ 词语格式正确
      </div>
    );
  }

  return (
    <div className="text-xs mt-1 space-y-1">
      {/* 格式错误 */}
      {result.errors.map((error, index) => (
        <div key={index} className="text-red-600">
          ✗ {error}
        </div>
      ))}
      
      {/* 重复提醒 */}
      {result.isDuplicate && (
        <div className="text-orange-600">
          ⚠ 词语重复于: {result.duplicateLibraries.join(', ')}
        </div>
      )}
    </div>
  );
};

// ===== 主组件 =====

const WordValidatorComponent: React.FC<WordValidatorProps> = ({
  word,
  libraryKey,
  onValidation,
  showDetails = false,
  className = ''
}) => {
  const [validationResult, setValidationResult] = useState<WordValidationResult | null>(null);
  const { libraries } = useWordLibraryStore();

  // 执行验证
  useEffect(() => {
    if (!word.trim()) {
      setValidationResult(null);
      return;
    }

    const result = validateWord(word, libraryKey, libraries);
    setValidationResult(result);
    onValidation?.(result);
  }, [word, libraryKey, libraries, onValidation]);

  if (!validationResult || !word.trim()) {
    return null;
  }

  return (
    <div className={`word-validator ${className}`}>
      <div className="flex items-center space-x-2">
        <ValidationIcon 
          isValid={validationResult.isValid}
          isDuplicate={validationResult.isDuplicate}
        />
        <span className="text-sm font-medium">
          {word}
        </span>
      </div>
      
      {showDetails && (
        <ValidationDetails 
          result={validationResult}
          word={word}
        />
      )}
    </div>
  );
};

// ===== 批量验证组件 =====

interface BatchValidatorProps {
  /** 要验证的词语列表 */
  words: string[];
  /** 词库标识 */
  libraryKey: WordLibraryKey;
  /** 验证结果回调 */
  onValidation?: (results: WordValidationResult[]) => void;
  /** 自定义类名 */
  className?: string;
}

const BatchValidator: React.FC<BatchValidatorProps> = ({
  words,
  libraryKey,
  onValidation,
  className = ''
}) => {
  const [results, setResults] = useState<WordValidationResult[]>([]);
  const { libraries } = useWordLibraryStore();

  useEffect(() => {
    const validationResults = words.map(word => 
      validateWord(word, libraryKey, libraries)
    );
    
    setResults(validationResults);
    onValidation?.(validationResults);
  }, [words, libraryKey, libraries, onValidation]);

  const validCount = results.filter(r => r.isValid && !r.isDuplicate).length;
  const invalidCount = results.filter(r => !r.isValid).length;
  const duplicateCount = results.filter(r => r.isDuplicate).length;

  return (
    <div className={`batch-validator ${className}`}>
      <div className="flex items-center space-x-4 text-xs">
        <div className="flex items-center space-x-1">
          <ValidationIcon isValid={true} isDuplicate={false} size={12} />
          <span className="text-green-600">{validCount} 有效</span>
        </div>
        
        {invalidCount > 0 && (
          <div className="flex items-center space-x-1">
            <ValidationIcon isValid={false} isDuplicate={false} size={12} />
            <span className="text-red-600">{invalidCount} 错误</span>
          </div>
        )}
        
        {duplicateCount > 0 && (
          <div className="flex items-center space-x-1">
            <ValidationIcon isValid={true} isDuplicate={true} size={12} />
            <span className="text-orange-600">{duplicateCount} 重复</span>
          </div>
        )}
      </div>
    </div>
  );
};

// ===== 验证规则说明组件 =====

const ValidationRules: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`validation-rules ${className}`}>
      <div className="text-xs text-gray-600 space-y-1">
        <div className="font-medium">词语规则：</div>
        <ul className="space-y-0.5 ml-2">
          <li>• 长度：{WORD_LENGTH_LIMITS.MIN}-{WORD_LENGTH_LIMITS.MAX} 个字符</li>
          <li>• 必须包含中文字符</li>
          <li>• 不能包含标点符号</li>
          <li>• 不能为空或纯空格</li>
        </ul>
      </div>
    </div>
  );
};

// ===== 性能优化 =====

const WordValidator = memo(WordValidatorComponent);
WordValidator.displayName = 'WordValidator';

export default WordValidator;
export { BatchValidator, ValidationRules };
export type { WordValidatorProps, BatchValidatorProps };
