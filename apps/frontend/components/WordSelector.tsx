/**
 * 词语选择器组件
 * 🎯 核心价值：矩阵填词模式的词语选择界面，支持键盘导航
 * 📦 功能范围：词语列表显示、键盘导航、词语选择确认
 * 🔄 架构设计：悬浮式组件，支持键盘交互和实时预览
 */

'use client';

import React, { useEffect, useCallback, useRef } from 'react';
import type { 
  BasicColorType, 
  DataLevel, 
  WordEntry 
} from '@/core/matrix/MatrixTypes';
import { 
  getWordLibraryDisplayName,
  getWordLibraryBackgroundColor,
  getWordLibraryTextColor
} from '@/core/wordLibrary/WordLibraryCore';
import { useWordInputStore } from '@/core/wordLibrary/WordLibraryStore';

// ===== 组件属性 =====

interface WordSelectorProps {
  /** 是否显示 */
  visible: boolean;
  /** 选中的单元格坐标 */
  cellPosition: { x: number; y: number } | null;
  /** 匹配的颜色 */
  color: BasicColorType | null;
  /** 匹配的级别 */
  level: DataLevel | null;
  /** 可选词语列表 */
  words: WordEntry[];
  /** 当前选中的词语索引 */
  selectedIndex: number;
  /** 选择确认回调 */
  onConfirm: (word: WordEntry) => void;
  /** 取消回调 */
  onCancel: () => void;
  /** 删除当前词语回调 */
  onDelete: () => void;
  /** 位置样式 */
  style?: React.CSSProperties;
}

// ===== 主组件 =====

const WordSelector: React.FC<WordSelectorProps> = ({
  visible,
  cellPosition,
  color,
  level,
  words,
  selectedIndex,
  onConfirm,
  onCancel,
  onDelete,
  style
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { selectNextWord, selectPreviousWord } = useWordInputStore();

  // 键盘事件处理
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!visible) return;

    switch (event.key) {
      case 'ArrowRight':
      case 'ArrowDown':
        event.preventDefault();
        selectNextWord();
        break;
      case 'ArrowLeft':
      case 'ArrowUp':
        event.preventDefault();
        selectPreviousWord();
        break;
      case 'Enter':
        event.preventDefault();
        if (words[selectedIndex]) {
          onConfirm(words[selectedIndex]);
        }
        break;
      case 'Escape':
        event.preventDefault();
        onCancel();
        break;
      case 'Delete':
      case 'Backspace':
        event.preventDefault();
        onDelete();
        break;
    }
  }, [visible, words, selectedIndex, selectNextWord, selectPreviousWord, onConfirm, onCancel, onDelete]);

  // 绑定键盘事件
  useEffect(() => {
    if (visible) {
      document.addEventListener('keydown', handleKeyDown);
      // 聚焦到容器
      containerRef.current?.focus();
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [visible, handleKeyDown]);

  // 不显示时返回null
  if (!visible || !cellPosition || !color || !level) {
    return null;
  }

  const displayName = getWordLibraryDisplayName(color, level);
  const backgroundColor = getWordLibraryBackgroundColor(color);
  const textColor = getWordLibraryTextColor(backgroundColor);

  return (
    <div
      ref={containerRef}
      className="fixed z-50 bg-white border border-gray-300 rounded-lg shadow-lg p-3 min-w-[200px] max-w-[300px]"
      style={{
        left: `${cellPosition.x * 34 + 50}px`,
        top: `${cellPosition.y * 34 + 50}px`,
        ...style
      }}
      tabIndex={-1}
      role="listbox"
      aria-label={`选择${displayName}词语`}
    >
      {/* 标题栏 */}
      <div 
        className="flex items-center justify-between mb-2 px-2 py-1 rounded text-sm font-medium"
        style={{ backgroundColor: backgroundColor + '20', color: backgroundColor }}
      >
        <span>{displayName}</span>
        <span className="text-xs opacity-70">
          {words.length} 词
        </span>
      </div>

      {/* 词语列表 */}
      {words.length > 0 ? (
        <div className="space-y-1 max-h-[200px] overflow-y-auto">
          {words.map((word, index) => (
            <div
              key={word.id}
              className={`px-2 py-1 rounded cursor-pointer text-sm transition-colors ${
                index === selectedIndex
                  ? 'ring-2 ring-blue-500'
                  : 'hover:bg-gray-100'
              }`}
              style={{
                backgroundColor: index === selectedIndex ? backgroundColor + '30' : 'transparent',
                color: index === selectedIndex ? backgroundColor : '#374151'
              }}
              onClick={() => onConfirm(word)}
              role="option"
              aria-selected={index === selectedIndex}
            >
              {word.text}
              {word.usageCount > 0 && (
                <span className="ml-2 text-xs opacity-60">
                  ({word.usageCount})
                </span>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center text-gray-500 text-sm py-4">
          暂无词语
        </div>
      )}

      {/* 操作提示 */}
      <div className="mt-3 pt-2 border-t border-gray-200 text-xs text-gray-500 space-y-1">
        <div>↑↓ 或 ←→ 选择词语</div>
        <div>Enter 确认 | Esc 取消 | Del 删除</div>
      </div>
    </div>
  );
};

export default WordSelector;
export type { WordSelectorProps };
