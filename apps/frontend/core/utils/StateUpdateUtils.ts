/**
 * 状态更新工具
 * 🎯 核心价值：统一状态更新模式，减少重复代码，提高一致性
 * 📦 功能范围：状态更新封装、批量更新、条件更新、性能优化
 * 🔄 架构设计：纯函数式工具，支持Immer和Zustand集成
 */

import { produce } from 'immer';

// ===== 类型定义 =====

interface StateMetadata {
  lastUpdate?: number;
  isDirty?: boolean;
  version?: number;
}

interface UpdateOptions {
  /** 是否更新时间戳 */
  updateTimestamp?: boolean;
  /** 是否标记为脏数据 */
  markDirty?: boolean;
  /** 是否增加版本号 */
  incrementVersion?: boolean;
  /** 自定义元数据更新函数 */
  customMetadataUpdate?: (metadata: StateMetadata) => void;
}

interface BatchUpdateOptions extends UpdateOptions {
  /** 是否在所有更新完成后才更新元数据 */
  deferMetadataUpdate?: boolean;
}

type StateUpdater<T> = (state: T) => void;
type ConditionalUpdater<T> = (state: T) => boolean;

// ===== 默认配置 =====

const DEFAULT_UPDATE_OPTIONS: UpdateOptions = {
  updateTimestamp: true,
  markDirty: true,
  incrementVersion: false,
};

// ===== 核心状态更新工具 =====

/**
 * 创建状态更新器
 * 统一的状态更新模式，自动处理元数据更新
 */
export const createStateUpdater = <T extends StateMetadata>(
  updateFn: StateUpdater<T>,
  options: UpdateOptions = {}
) => {
  const opts = { ...DEFAULT_UPDATE_OPTIONS, ...options };
  
  return produce((state: T) => {
    // 执行用户定义的更新逻辑
    updateFn(state);
    
    // 更新元数据
    updateStateMetadata(state, opts);
  });
};

/**
 * 创建批量状态更新器
 * 支持多个更新函数的批量执行
 */
export const createBatchStateUpdater = <T extends StateMetadata>(
  updateFns: Array<StateUpdater<T>>,
  options: BatchUpdateOptions = {}
) => {
  const opts = { ...DEFAULT_UPDATE_OPTIONS, ...options };
  
  return produce((state: T) => {
    // 执行所有更新函数
    updateFns.forEach(fn => fn(state));
    
    // 批量更新元数据（如果不延迟的话）
    if (!opts.deferMetadataUpdate) {
      updateStateMetadata(state, opts);
    }
  });
};

/**
 * 创建条件状态更新器
 * 只有在条件满足时才执行更新
 */
export const createConditionalStateUpdater = <T extends StateMetadata>(
  condition: ConditionalUpdater<T>,
  updateFn: StateUpdater<T>,
  options: UpdateOptions = {}
) => {
  const opts = { ...DEFAULT_UPDATE_OPTIONS, ...options };
  
  return produce((state: T) => {
    // 检查条件
    if (condition(state)) {
      updateFn(state);
      updateStateMetadata(state, opts);
    }
  });
};

/**
 * 创建安全状态更新器
 * 包含错误处理和回滚机制
 */
export const createSafeStateUpdater = <T extends StateMetadata>(
  updateFn: StateUpdater<T>,
  options: UpdateOptions = {},
  onError?: (error: Error, state: T) => void
) => {
  const opts = { ...DEFAULT_UPDATE_OPTIONS, ...options };
  
  return produce((state: T) => {
    try {
      updateFn(state);
      updateStateMetadata(state, opts);
    } catch (error) {
      console.error('状态更新失败:', error);
      
      if (onError) {
        onError(error as Error, state);
      }
      
      // 可以在这里添加回滚逻辑
      throw error;
    }
  });
};

// ===== 专用更新工具 =====

/**
 * 更新单个属性
 */
export const createPropertyUpdater = <T extends StateMetadata, K extends keyof T>(
  property: K,
  value: T[K],
  options: UpdateOptions = {}
) => {
  return createStateUpdater<T>(
    (state) => {
      state[property] = value;
    },
    options
  );
};

/**
 * 更新多个属性
 */
export const createPropertiesUpdater = <T extends StateMetadata>(
  updates: Partial<T>,
  options: UpdateOptions = {}
) => {
  return createStateUpdater<T>(
    (state) => {
      Object.assign(state, updates);
    },
    options
  );
};

/**
 * 切换布尔属性
 */
export const createToggleUpdater = <T extends StateMetadata>(
  property: keyof T,
  options: UpdateOptions = {}
) => {
  return createStateUpdater<T>(
    (state) => {
      (state as any)[property] = !(state as any)[property];
    },
    options
  );
};

/**
 * 数组操作更新器
 */
export const createArrayUpdater = <T extends StateMetadata, K extends keyof T>(
  property: K,
  operation: 'push' | 'pop' | 'shift' | 'unshift' | 'splice',
  ...args: any[]
) => {
  return createStateUpdater<T>(
    (state) => {
      const array = state[property] as any[];
      if (Array.isArray(array)) {
        (array as any)[operation](...args);
      }
    }
  );
};

/**
 * Map操作更新器
 */
export const createMapUpdater = <T extends StateMetadata, K extends keyof T>(
  property: K,
  operation: 'set' | 'delete' | 'clear',
  key?: any,
  value?: any
) => {
  return createStateUpdater<T>(
    (state) => {
      const map = state[property] as Map<any, any>;
      if (map instanceof Map) {
        switch (operation) {
          case 'set':
            map.set(key, value);
            break;
          case 'delete':
            map.delete(key);
            break;
          case 'clear':
            map.clear();
            break;
        }
      }
    }
  );
};

/**
 * Set操作更新器
 */
export const createSetUpdater = <T extends StateMetadata, K extends keyof T>(
  property: K,
  operation: 'add' | 'delete' | 'clear',
  value?: any
) => {
  return createStateUpdater<T>(
    (state) => {
      const set = state[property] as Set<any>;
      if (set instanceof Set) {
        switch (operation) {
          case 'add':
            set.add(value);
            break;
          case 'delete':
            set.delete(value);
            break;
          case 'clear':
            set.clear();
            break;
        }
      }
    }
  );
};

// ===== 辅助函数 =====

/**
 * 更新状态元数据
 */
export const updateStateMetadata = <T extends StateMetadata>(
  state: T,
  options: UpdateOptions
): void => {
  if (options.updateTimestamp) {
    state.lastUpdate = Date.now();
  }
  
  if (options.markDirty) {
    state.isDirty = true;
  }
  
  if (options.incrementVersion && typeof state.version === 'number') {
    state.version += 1;
  }
  
  if (options.customMetadataUpdate) {
    options.customMetadataUpdate(state);
  }
};

/**
 * 重置状态元数据
 */
export const resetStateMetadata = <T extends StateMetadata>(
  state: T,
  options: Partial<StateMetadata> = {}
): void => {
  if (options.lastUpdate !== undefined) {
    state.lastUpdate = options.lastUpdate;
  }
  
  if (options.isDirty !== undefined) {
    state.isDirty = options.isDirty;
  }
  
  if (options.version !== undefined) {
    state.version = options.version;
  }
};

/**
 * 创建状态快照
 */
export const createStateSnapshot = <T>(state: T): T => {
  return JSON.parse(JSON.stringify(state));
};

/**
 * 比较状态差异
 */
export const compareStates = <T>(
  oldState: T,
  newState: T
): { hasChanges: boolean; changedKeys: string[] } => {
  const changedKeys: string[] = [];
  
  const oldStr = JSON.stringify(oldState);
  const newStr = JSON.stringify(newState);
  
  if (oldStr === newStr) {
    return { hasChanges: false, changedKeys: [] };
  }
  
  // 简单的键级别比较
  const oldObj = oldState as Record<string, any>;
  const newObj = newState as Record<string, any>;
  
  const allKeys = new Set([...Object.keys(oldObj), ...Object.keys(newObj)]);
  
  for (const key of allKeys) {
    if (JSON.stringify(oldObj[key]) !== JSON.stringify(newObj[key])) {
      changedKeys.push(key);
    }
  }
  
  return { hasChanges: true, changedKeys };
};

// ===== 性能优化工具 =====

/**
 * 防抖状态更新器
 */
export const createDebouncedStateUpdater = <T extends StateMetadata>(
  updateFn: StateUpdater<T>,
  delay: number = 300,
  options: UpdateOptions = {}
) => {
  let timeoutId: NodeJS.Timeout | null = null;
  
  return (state: T) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    timeoutId = setTimeout(() => {
      const updater = createStateUpdater(updateFn, options);
      updater(state);
    }, delay);
  };
};

/**
 * 节流状态更新器
 */
export const createThrottledStateUpdater = <T extends StateMetadata>(
  updateFn: StateUpdater<T>,
  interval: number = 100,
  options: UpdateOptions = {}
) => {
  let lastUpdate = 0;
  
  return (state: T) => {
    const now = Date.now();
    
    if (now - lastUpdate >= interval) {
      const updater = createStateUpdater(updateFn, options);
      updater(state);
      lastUpdate = now;
    }
  };
};
