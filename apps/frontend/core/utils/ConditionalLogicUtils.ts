/**
 * 条件判断逻辑简化工具
 * 🎯 核心价值：简化复杂的条件判断逻辑，提高代码可读性和维护性
 * 📦 功能范围：条件链、策略模式、规则引擎、逻辑组合
 * 🔄 架构设计：函数式编程，支持链式调用和组合模式
 */

// ===== 类型定义 =====

type Predicate<T = any> = (value: T) => boolean;
type PredicateResult<T = any> = boolean | Promise<boolean>;
type Action<T = any, R = any> = (value: T) => R;
type AsyncAction<T = any, R = any> = (value: T) => Promise<R>;

interface Rule<T = any, R = any> {
  condition: Predicate<T>;
  action: Action<T, R>;
  priority?: number;
  name?: string;
}

interface ConditionalChainOptions {
  /** 是否在第一个匹配后停止 */
  stopOnFirstMatch?: boolean;
  /** 默认值 */
  defaultValue?: any;
  /** 是否启用调试模式 */
  debug?: boolean;
}

interface StrategyMap<T = any, R = any> {
  [key: string]: Action<T, R>;
}

// ===== 条件链构建器 =====

export class ConditionalChain<T = any, R = any> {
  private rules: Rule<T, R>[] = [];
  private options: ConditionalChainOptions = {
    stopOnFirstMatch: true,
    debug: false,
  };

  constructor(options?: ConditionalChainOptions) {
    this.options = { ...this.options, ...options };
  }

  /**
   * 添加条件规则
   */
  when(condition: Predicate<T>, action: Action<T, R>, name?: string): this {
    this.rules.push({
      condition,
      action,
      name,
      priority: this.rules.length,
    });
    return this;
  }

  /**
   * 添加带优先级的规则
   */
  whenWithPriority(
    condition: Predicate<T>,
    action: Action<T, R>,
    priority: number,
    name?: string
  ): this {
    this.rules.push({
      condition,
      action,
      priority,
      name,
    });
    return this;
  }

  /**
   * 添加默认规则（条件总是true）
   */
  otherwise(action: Action<T, R>): this {
    this.rules.push({
      condition: () => true,
      action,
      name: 'default',
      priority: Number.MAX_SAFE_INTEGER,
    });
    return this;
  }

  /**
   * 执行条件链
   */
  execute(value: T): R | R[] | undefined {
    // 按优先级排序
    const sortedRules = [...this.rules].sort((a, b) => (a.priority || 0) - (b.priority || 0));
    
    const results: R[] = [];
    
    for (const rule of sortedRules) {
      try {
        if (rule.condition(value)) {
          const result = rule.action(value);
          results.push(result);
          
          if (this.options.debug) {
            console.log(`条件链匹配: ${rule.name || '未命名规则'}`, { value, result });
          }
          
          if (this.options.stopOnFirstMatch) {
            return result;
          }
        }
      } catch (error) {
        if (this.options.debug) {
          console.error(`条件链执行错误: ${rule.name || '未命名规则'}`, error);
        }
      }
    }
    
    if (results.length === 0) {
      return this.options.defaultValue;
    }
    
    return this.options.stopOnFirstMatch ? results[0] : results;
  }

  /**
   * 清空所有规则
   */
  clear(): this {
    this.rules = [];
    return this;
  }

  /**
   * 获取规则数量
   */
  size(): number {
    return this.rules.length;
  }
}

// ===== 策略模式工具 =====

export class StrategySelector<T = any, R = any> {
  private strategies: StrategyMap<T, R> = {};
  private defaultStrategy?: Action<T, R>;

  /**
   * 注册策略
   */
  register(key: string, strategy: Action<T, R>): this {
    this.strategies[key] = strategy;
    return this;
  }

  /**
   * 批量注册策略
   */
  registerMany(strategies: StrategyMap<T, R>): this {
    Object.assign(this.strategies, strategies);
    return this;
  }

  /**
   * 设置默认策略
   */
  setDefault(strategy: Action<T, R>): this {
    this.defaultStrategy = strategy;
    return this;
  }

  /**
   * 执行策略
   */
  execute(key: string, value: T): R | undefined {
    const strategy = this.strategies[key] || this.defaultStrategy;
    
    if (!strategy) {
      throw new Error(`未找到策略: ${key}`);
    }
    
    return strategy(value);
  }

  /**
   * 检查策略是否存在
   */
  has(key: string): boolean {
    return key in this.strategies;
  }

  /**
   * 获取所有策略键
   */
  keys(): string[] {
    return Object.keys(this.strategies);
  }
}

// ===== 逻辑组合工具 =====

/**
 * 逻辑与组合
 */
export const and = <T>(...predicates: Predicate<T>[]): Predicate<T> => {
  return (value: T) => predicates.every(predicate => predicate(value));
};

/**
 * 逻辑或组合
 */
export const or = <T>(...predicates: Predicate<T>[]): Predicate<T> => {
  return (value: T) => predicates.some(predicate => predicate(value));
};

/**
 * 逻辑非
 */
export const not = <T>(predicate: Predicate<T>): Predicate<T> => {
  return (value: T) => !predicate(value);
};

/**
 * 条件组合器
 */
export const when = <T, R>(
  condition: Predicate<T>,
  thenAction: Action<T, R>,
  elseAction?: Action<T, R>
): Action<T, R | undefined> => {
  return (value: T) => {
    if (condition(value)) {
      return thenAction(value);
    }
    return elseAction ? elseAction(value) : undefined;
  };
};

// ===== 常用谓词函数 =====

/**
 * 检查值是否存在
 */
export const exists = <T>(value: T): boolean => {
  return value !== null && value !== undefined;
};

/**
 * 检查值是否为空
 */
export const isEmpty = <T>(value: T): boolean => {
  if (value === null || value === undefined) return true;
  if (typeof value === 'string') return value.trim() === '';
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
};

/**
 * 检查值是否相等
 */
export const equals = <T>(expected: T) => (value: T): boolean => {
  return value === expected;
};

/**
 * 检查值是否在数组中
 */
export const isIn = <T>(array: T[]) => (value: T): boolean => {
  return array.includes(value);
};

/**
 * 检查值是否匹配正则表达式
 */
export const matches = (regex: RegExp) => (value: string): boolean => {
  return regex.test(value);
};

/**
 * 检查数值是否在范围内
 */
export const inRange = (min: number, max: number) => (value: number): boolean => {
  return value >= min && value <= max;
};

/**
 * 检查对象是否有指定属性
 */
export const hasProperty = <T extends object>(property: keyof T) => (obj: T): boolean => {
  return property in obj;
};

// ===== 矩阵专用条件判断 =====

/**
 * 矩阵单元格状态检查器
 */
export class CellStateChecker {
  /**
   * 检查是否为增强单元格
   */
  static isEnhanced = (config: any, cellData: any): boolean => {
    return config?.mainMode === 'color' && cellData?.level === 1;
  };

  /**
   * 检查是否为填词模式活跃单元格
   */
  static isWordInputActive = (
    x: number,
    y: number,
    wordInputState: any
  ): boolean => {
    return !!(
      wordInputState?.isActive &&
      wordInputState.selectedCell &&
      wordInputState.selectedCell.x === x &&
      wordInputState.selectedCell.y === y
    );
  };

  /**
   * 检查单元格是否有数据
   */
  static hasData = (cellData: any): boolean => {
    return exists(cellData) && cellData.isActive;
  };

  /**
   * 检查单元格是否被选中
   */
  static isSelected = (cellData: any): boolean => {
    return cellData?.isSelected === true;
  };

  /**
   * 检查单元格是否被悬停
   */
  static isHovered = (cellData: any): boolean => {
    return cellData?.isHovered === true;
  };

  /**
   * 检查单元格是否获得焦点
   */
  static isFocused = (cellData: any): boolean => {
    return cellData?.isFocused === true;
  };
}

/**
 * 矩阵模式检查器
 */
export class ModeChecker {
  /**
   * 检查是否为颜色模式
   */
  static isColorMode = (config: any): boolean => {
    return config?.mainMode === 'color' || config?.isColorMode === true;
  };

  /**
   * 检查是否为词语模式
   */
  static isWordMode = (config: any): boolean => {
    return config?.contentMode === 'word' || config?.isWordMode === true;
  };

  /**
   * 检查是否为数字模式
   */
  static isNumberMode = (config: any): boolean => {
    return config?.contentMode === 'number' || config?.isNumberMode === true;
  };

  /**
   * 检查是否为坐标模式
   */
  static isCoordinateMode = (config: any): boolean => {
    return config?.contentMode === 'coordinate' || config?.isCoordinateMode === true;
  };

  /**
   * 检查是否为颜色+词语模式
   */
  static isColorWordMode = (config: any): boolean => {
    return this.isColorMode(config) && this.isWordMode(config);
  };
}

// ===== 便捷函数 =====

/**
 * 创建条件链
 */
export const createConditionalChain = <T = any, R = any>(
  options?: ConditionalChainOptions
): ConditionalChain<T, R> => {
  return new ConditionalChain<T, R>(options);
};

/**
 * 创建策略选择器
 */
export const createStrategySelector = <T = any, R = any>(): StrategySelector<T, R> => {
  return new StrategySelector<T, R>();
};

/**
 * 简单的条件执行
 */
export const ifThen = <T, R>(
  condition: boolean,
  thenValue: R,
  elseValue?: R
): R | undefined => {
  return condition ? thenValue : elseValue;
};

/**
 * 安全的条件执行
 */
export const safeExecute = <T, R>(
  condition: Predicate<T>,
  action: Action<T, R>,
  value: T,
  fallback?: R
): R | undefined => {
  try {
    return condition(value) ? action(value) : fallback;
  } catch (error) {
    console.error('条件执行错误:', error);
    return fallback;
  }
};

// ===== 导出类型 =====

export type {
  Predicate,
  PredicateResult,
  Action,
  AsyncAction,
  Rule,
  ConditionalChainOptions,
  StrategyMap,
};
