/**
 * 单元格词语绑定状态管理
 * 🎯 核心价值：专门管理单元格与词语的绑定关系，职责单一
 * 📦 功能范围：词语绑定、解绑、查询、批量操作
 * 🔄 架构设计：基于Zustand的独立状态管理，支持持久化
 */

import { enableMapSet, produce } from 'immer';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { coordinateKey } from './MatrixTypes';

// 启用 Immer 的 MapSet 插件
enableMapSet();

// ===== 接口定义 =====

interface CellWordBindingState {
  /** 单元格词语绑定映射：坐标key -> 词语ID */
  cellWordBindings: Map<string, string>;
  
  /** 词语使用统计：词语ID -> 使用次数 */
  wordUsageCount: Map<string, number>;
  
  /** 最后更新时间 */
  lastUpdate: number;
}

interface CellWordBindingActions {
  /** 绑定词语到单元格 */
  bindWordToCell: (x: number, y: number, wordId: string) => void;
  
  /** 解绑单元格的词语 */
  unbindWordFromCell: (x: number, y: number) => void;
  
  /** 获取单元格绑定的词语 */
  getCellWord: (x: number, y: number) => string | null;
  
  /** 获取所有词语绑定 */
  getAllWordBindings: () => Map<string, string>;
  
  /** 清空所有词语绑定 */
  clearAllWordBindings: () => void;
  
  /** 清理无效的词语绑定 */
  cleanupInvalidWordBindings: () => void;
  
  /** 批量绑定词语 */
  batchBindWords: (bindings: Array<{ x: number; y: number; wordId: string }>) => void;
  
  /** 获取词语使用统计 */
  getWordUsageCount: (wordId: string) => number;
  
  /** 获取使用特定词语的所有单元格 */
  getCellsWithWord: (wordId: string) => Array<{ x: number; y: number }>;
}

type CellWordBindingStore = CellWordBindingState & CellWordBindingActions;

// ===== 辅助函数 =====

/**
 * 更新状态元数据
 */
const updateMetadata = (state: CellWordBindingState) => {
  state.lastUpdate = Date.now();
};

/**
 * 更新词语使用统计
 */
const updateWordUsage = (state: CellWordBindingState, wordId: string, delta: number) => {
  const currentCount = state.wordUsageCount.get(wordId) || 0;
  const newCount = Math.max(0, currentCount + delta);
  
  if (newCount === 0) {
    state.wordUsageCount.delete(wordId);
  } else {
    state.wordUsageCount.set(wordId, newCount);
  }
};

/**
 * 解析坐标key
 */
const parseCoordinateKey = (key: string): { x: number; y: number } => {
  const [x, y] = key.split(',').map(Number);
  return { x, y };
};

// ===== Store实现 =====

export const useCellWordBindingStore = create<CellWordBindingStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      cellWordBindings: new Map(),
      wordUsageCount: new Map(),
      lastUpdate: Date.now(),

      // 基础操作
      bindWordToCell: (x: number, y: number, wordId: string) => {
        set(produce((state) => {
          const key = coordinateKey(x, y);
          const existingWordId = state.cellWordBindings.get(key);
          
          // 如果已有绑定，先减少旧词语的使用计数
          if (existingWordId && existingWordId !== wordId) {
            updateWordUsage(state, existingWordId, -1);
          }
          
          // 设置新绑定
          state.cellWordBindings.set(key, wordId);
          
          // 增加新词语的使用计数
          updateWordUsage(state, wordId, 1);
          
          updateMetadata(state);
        }));
      },

      unbindWordFromCell: (x: number, y: number) => {
        set(produce((state) => {
          const key = coordinateKey(x, y);
          const existingWordId = state.cellWordBindings.get(key);
          
          if (existingWordId) {
            // 删除绑定
            state.cellWordBindings.delete(key);
            
            // 减少词语使用计数
            updateWordUsage(state, existingWordId, -1);
            
            updateMetadata(state);
          }
        }));
      },

      getCellWord: (x: number, y: number) => {
        const key = coordinateKey(x, y);
        return get().cellWordBindings.get(key) || null;
      },

      getAllWordBindings: () => {
        return new Map(get().cellWordBindings);
      },

      clearAllWordBindings: () => {
        set(produce((state) => {
          state.cellWordBindings.clear();
          state.wordUsageCount.clear();
          updateMetadata(state);
        }));
      },

      cleanupInvalidWordBindings: () => {
        set(produce((state) => {
          // 这里可以添加清理逻辑，比如清理无效的词语ID
          // 目前保持简单实现
          updateMetadata(state);
        }));
      },

      batchBindWords: (bindings: Array<{ x: number; y: number; wordId: string }>) => {
        set(produce((state) => {
          bindings.forEach(({ x, y, wordId }) => {
            const key = coordinateKey(x, y);
            const existingWordId = state.cellWordBindings.get(key);
            
            // 处理旧绑定
            if (existingWordId && existingWordId !== wordId) {
              updateWordUsage(state, existingWordId, -1);
            }
            
            // 设置新绑定
            state.cellWordBindings.set(key, wordId);
            updateWordUsage(state, wordId, 1);
          });
          
          updateMetadata(state);
        }));
      },

      getWordUsageCount: (wordId: string) => {
        return get().wordUsageCount.get(wordId) || 0;
      },

      getCellsWithWord: (wordId: string) => {
        const bindings = get().cellWordBindings;
        const cells: Array<{ x: number; y: number }> = [];
        
        for (const [key, boundWordId] of bindings.entries()) {
          if (boundWordId === wordId) {
            cells.push(parseCoordinateKey(key));
          }
        }
        
        return cells;
      },
    }),
    {
      name: 'cell-word-binding-store',
      version: 1,
      partialize: (state) => ({
        cellWordBindings: Array.from(state.cellWordBindings.entries()),
        wordUsageCount: Array.from(state.wordUsageCount.entries()),
        lastUpdate: state.lastUpdate,
      }),
      onRehydrateStorage: () => (state) => {
        if (state?.cellWordBindings) {
          state.cellWordBindings = new Map(state.cellWordBindings as any);
        }
        if (state?.wordUsageCount) {
          state.wordUsageCount = new Map(state.wordUsageCount as any);
        }
      },
    }
  )
);

// ===== 导出类型 =====
export type { CellWordBindingStore };
