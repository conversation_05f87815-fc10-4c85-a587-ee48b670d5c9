/**
 * 矩阵缓存服务
 * 🎯 核心价值：高性能缓存管理，减少重复计算，提升渲染性能
 * 📦 功能范围：数据缓存、计算结果缓存、LRU策略、TTL管理
 * 🔄 架构设计：单例模式，支持多种缓存策略和自动清理
 */

import type { ProcessedMatrixData, MatrixConfig, CellRenderData } from './MatrixTypes';

// ===== 缓存条目接口 =====

interface CacheEntry<T = any> {
  /** 缓存的值 */
  value: T;
  /** 创建时间戳 */
  timestamp: number;
  /** 生存时间 (毫秒) */
  ttl: number;
  /** 访问次数 */
  accessCount: number;
  /** 最后访问时间 */
  lastAccessed: number;
}

interface CacheStats {
  /** 总缓存条目数 */
  totalEntries: number;
  /** 缓存命中次数 */
  hits: number;
  /** 缓存未命中次数 */
  misses: number;
  /** 命中率 */
  hitRate: number;
  /** 内存使用估算 (字节) */
  memoryUsage: number;
}

// ===== 缓存配置 =====

interface CacheConfig {
  /** 最大缓存条目数 */
  maxSize: number;
  /** 默认TTL (毫秒) */
  defaultTTL: number;
  /** 清理间隔 (毫秒) */
  cleanupInterval: number;
  /** 是否启用统计 */
  enableStats: boolean;
}

const DEFAULT_CACHE_CONFIG: CacheConfig = {
  maxSize: 1000,
  defaultTTL: 5 * 60 * 1000, // 5分钟
  cleanupInterval: 60 * 1000, // 1分钟
  enableStats: true,
};

// ===== 缓存服务实现 =====

export class MatrixCacheService {
  private static instance: MatrixCacheService;
  private cache = new Map<string, CacheEntry>();
  private config: CacheConfig;
  private stats = {
    hits: 0,
    misses: 0,
  };
  private cleanupTimer?: NodeJS.Timeout;

  private constructor(config: Partial<CacheConfig> = {}) {
    this.config = { ...DEFAULT_CACHE_CONFIG, ...config };
    this.startCleanupTimer();
  }

  static getInstance(config?: Partial<CacheConfig>): MatrixCacheService {
    if (!this.instance) {
      this.instance = new MatrixCacheService(config);
    }
    return this.instance;
  }

  /**
   * 设置缓存值
   */
  set<T>(key: string, value: T, customTTL?: number): void {
    this.cleanup();

    const entry: CacheEntry<T> = {
      value,
      timestamp: Date.now(),
      ttl: customTTL || this.config.defaultTTL,
      accessCount: 0,
      lastAccessed: Date.now(),
    };

    this.cache.set(key, entry);

    // LRU清理：如果超过最大大小，删除最少使用的条目
    if (this.cache.size > this.config.maxSize) {
      this.evictLRU();
    }
  }

  /**
   * 获取缓存值
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);

    if (!entry) {
      if (this.config.enableStats) {
        this.stats.misses++;
      }
      return null;
    }

    // 检查是否过期
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      if (this.config.enableStats) {
        this.stats.misses++;
      }
      return null;
    }

    // 更新访问统计
    entry.accessCount++;
    entry.lastAccessed = Date.now();

    if (this.config.enableStats) {
      this.stats.hits++;
    }

    return entry.value as T;
  }

  /**
   * 删除缓存条目
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear();
    this.stats.hits = 0;
    this.stats.misses = 0;
  }

  /**
   * 检查缓存是否存在且未过期
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? this.stats.hits / totalRequests : 0;

    return {
      totalEntries: this.cache.size,
      hits: this.stats.hits,
      misses: this.stats.misses,
      hitRate: Math.round(hitRate * 100) / 100,
      memoryUsage: this.estimateMemoryUsage(),
    };
  }

  /**
   * 获取或设置缓存（如果不存在则计算）
   */
  getOrSet<T>(key: string, factory: () => T, customTTL?: number): T {
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const value = factory();
    this.set(key, value, customTTL);
    return value;
  }

  /**
   * 批量设置缓存
   */
  setMany<T>(entries: Array<{ key: string; value: T; ttl?: number }>): void {
    entries.forEach(({ key, value, ttl }) => {
      this.set(key, value, ttl);
    });
  }

  /**
   * 批量获取缓存
   */
  getMany<T>(keys: string[]): Map<string, T> {
    const result = new Map<string, T>();
    
    keys.forEach(key => {
      const value = this.get<T>(key);
      if (value !== null) {
        result.set(key, value);
      }
    });

    return result;
  }

  // ===== 私有方法 =====

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
  }

  private evictLRU(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  private estimateMemoryUsage(): number {
    // 简单的内存使用估算
    let totalSize = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      totalSize += key.length * 2; // 字符串大小估算
      totalSize += JSON.stringify(entry.value).length * 2; // 值大小估算
      totalSize += 64; // 条目元数据大小估算
    }

    return totalSize;
  }

  private startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * 销毁缓存服务
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
    this.clear();
  }
}

// ===== 专用缓存方法 =====

/**
 * 矩阵处理数据缓存
 */
export const cacheProcessedMatrixData = (
  config: MatrixConfig,
  data: ProcessedMatrixData
): void => {
  const cache = MatrixCacheService.getInstance();
  const key = `processed-matrix-${JSON.stringify(config)}`;
  cache.set(key, data, 2 * 60 * 1000); // 2分钟TTL
};

/**
 * 获取缓存的矩阵处理数据
 */
export const getCachedProcessedMatrixData = (
  config: MatrixConfig
): ProcessedMatrixData | null => {
  const cache = MatrixCacheService.getInstance();
  const key = `processed-matrix-${JSON.stringify(config)}`;
  return cache.get<ProcessedMatrixData>(key);
};

/**
 * 单元格渲染数据缓存
 */
export const cacheCellRenderData = (
  x: number,
  y: number,
  config: MatrixConfig,
  renderData: CellRenderData
): void => {
  const cache = MatrixCacheService.getInstance();
  const key = `cell-render-${x}-${y}-${JSON.stringify(config)}`;
  cache.set(key, renderData, 1 * 60 * 1000); // 1分钟TTL
};

/**
 * 获取缓存的单元格渲染数据
 */
export const getCachedCellRenderData = (
  x: number,
  y: number,
  config: MatrixConfig
): CellRenderData | null => {
  const cache = MatrixCacheService.getInstance();
  const key = `cell-render-${x}-${y}-${JSON.stringify(config)}`;
  return cache.get<CellRenderData>(key);
};

// ===== 导出默认实例 =====
export const matrixCache = MatrixCacheService.getInstance();
