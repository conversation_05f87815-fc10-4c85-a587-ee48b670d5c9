/**
 * 矩阵数据初始化器
 * 🎯 核心价值：专门负责矩阵数据的初始化逻辑，提供高性能的数据生成
 * 📦 功能范围：矩阵数据初始化、单元格数据生成、批量数据处理
 * 🔄 架构设计：静态工具类，提供纯函数式的数据初始化方法
 */

import type { 
  CellData, 
  MatrixData, 
  MatrixDataSet, 
  Coordinate,
  MatrixDataPoint 
} from './MatrixTypes';
import { 
  MATRIX_SIZE, 
  coordinateKey, 
  createDefaultCell 
} from './MatrixTypes';
import { matrixCache } from './MatrixCacheService';

// ===== 初始化配置 =====

interface InitializationConfig {
  /** 是否启用缓存 */
  enableCache: boolean;
  /** 是否启用性能监控 */
  enablePerfMonitoring: boolean;
  /** 批处理大小 */
  batchSize: number;
  /** 是否预填充数据 */
  prefillData: boolean;
}

const DEFAULT_INIT_CONFIG: InitializationConfig = {
  enableCache: true,
  enablePerfMonitoring: true,
  batchSize: 100,
  prefillData: true,
};

// ===== 初始化结果接口 =====

interface InitializationResult {
  /** 初始化的单元格数据 */
  cells: Map<string, CellData>;
  /** 包含数据的单元格数量 */
  cellsWithData: number;
  /** 初始化耗时 (毫秒) */
  initTime: number;
  /** 内存使用估算 (字节) */
  memoryUsage: number;
}

interface BatchProcessResult {
  /** 处理的数据点数量 */
  processedCount: number;
  /** 成功创建的单元格数量 */
  successCount: number;
  /** 跳过的数据点数量 */
  skippedCount: number;
  /** 处理耗时 (毫秒) */
  processTime: number;
}

// ===== 矩阵数据初始化器 =====

export class MatrixDataInitializer {
  private static config: InitializationConfig = DEFAULT_INIT_CONFIG;

  /**
   * 配置初始化器
   */
  static configure(config: Partial<InitializationConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 创建初始矩阵数据
   */
  static createInitialData(): MatrixData {
    const startTime = performance.now();

    const data: MatrixData = {
      cells: new Map(),
      selectedCells: new Set(),
      hoveredCell: null,
      focusedCell: null,
      lastUpdate: Date.now(),
    };

    if (this.config.enablePerfMonitoring) {
      const endTime = performance.now();
      console.log(`矩阵初始数据创建耗时: ${(endTime - startTime).toFixed(2)}ms`);
    }

    return data;
  }

  /**
   * 初始化单元格数据
   */
  static initializeCellsData(
    matrixData: MatrixDataSet,
    config?: Partial<InitializationConfig>
  ): InitializationResult {
    const startTime = performance.now();
    const currentConfig = { ...this.config, ...config };

    // 检查缓存
    if (currentConfig.enableCache) {
      const cacheKey = `cells-init-${matrixData.metadata.totalPoints}`;
      const cached = matrixCache.get<InitializationResult>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const cells = new Map<string, CellData>();
    let cellsWithData = 0;

    // 批量处理数据点
    const dataPoints = Array.from(matrixData.byCoordinate.values());
    const batches = this.createBatches(dataPoints, currentConfig.batchSize);

    for (const batch of batches) {
      const batchResult = this.processBatch(batch, cells);
      cellsWithData += batchResult.successCount;
    }

    // 如果启用预填充，为没有数据的单元格创建默认数据
    if (currentConfig.prefillData) {
      this.prefillEmptyCells(cells, matrixData);
    }

    const endTime = performance.now();
    const initTime = endTime - startTime;

    const result: InitializationResult = {
      cells,
      cellsWithData,
      initTime,
      memoryUsage: this.estimateMemoryUsage(cells),
    };

    // 缓存结果
    if (currentConfig.enableCache) {
      const cacheKey = `cells-init-${matrixData.metadata.totalPoints}`;
      matrixCache.set(cacheKey, result, 10 * 60 * 1000); // 10分钟缓存
    }

    if (currentConfig.enablePerfMonitoring) {
      console.log(`单元格数据初始化完成:`, {
        totalCells: cells.size,
        cellsWithData,
        initTime: `${initTime.toFixed(2)}ms`,
        memoryUsage: `${(result.memoryUsage / 1024).toFixed(2)}KB`,
      });
    }

    return result;
  }

  /**
   * 从数据点创建单元格数据
   */
  static createCellFromDataPoint(dataPoint: MatrixDataPoint): CellData {
    const cell = createDefaultCell(dataPoint.x, dataPoint.y);
    
    // 从数据点复制属性
    return {
      ...cell,
      color: dataPoint.color,
      level: dataPoint.level,
      group: dataPoint.group,
      displayCoordinate: dataPoint.displayCoordinate,
      isActive: true,
    };
  }

  /**
   * 批量创建单元格数据
   */
  static createCellsBatch(
    dataPoints: MatrixDataPoint[]
  ): Map<string, CellData> {
    const cells = new Map<string, CellData>();

    for (const dataPoint of dataPoints) {
      const key = coordinateKey(dataPoint.x, dataPoint.y);
      const cell = this.createCellFromDataPoint(dataPoint);
      cells.set(key, cell);
    }

    return cells;
  }

  /**
   * 验证单元格数据完整性
   */
  static validateCellsData(cells: Map<string, CellData>): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查坐标范围
    for (const [key, cell] of cells.entries()) {
      if (cell.x < 0 || cell.x >= MATRIX_SIZE || cell.y < 0 || cell.y >= MATRIX_SIZE) {
        errors.push(`单元格坐标超出范围: ${key} (${cell.x}, ${cell.y})`);
      }

      // 检查key与坐标的一致性
      const expectedKey = coordinateKey(cell.x, cell.y);
      if (key !== expectedKey) {
        errors.push(`单元格key不匹配: ${key} vs ${expectedKey}`);
      }
    }

    // 检查数据完整性
    if (cells.size === 0) {
      warnings.push('没有单元格数据');
    }

    if (cells.size > MATRIX_SIZE * MATRIX_SIZE) {
      warnings.push(`单元格数量超过最大值: ${cells.size} > ${MATRIX_SIZE * MATRIX_SIZE}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 重置单元格状态
   */
  static resetCellsState(cells: Map<string, CellData>): void {
    for (const cell of cells.values()) {
      cell.isSelected = false;
      cell.isHovered = false;
      cell.isFocused = false;
    }
  }

  // ===== 私有方法 =====

  private static createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  private static processBatch(
    dataPoints: MatrixDataPoint[],
    cells: Map<string, CellData>
  ): BatchProcessResult {
    const startTime = performance.now();
    let successCount = 0;
    let skippedCount = 0;

    for (const dataPoint of dataPoints) {
      try {
        const key = coordinateKey(dataPoint.x, dataPoint.y);
        
        // 检查是否已存在
        if (cells.has(key)) {
          skippedCount++;
          continue;
        }

        const cell = this.createCellFromDataPoint(dataPoint);
        cells.set(key, cell);
        successCount++;
      } catch (error) {
        skippedCount++;
        console.warn(`处理数据点失败:`, dataPoint, error);
      }
    }

    const endTime = performance.now();

    return {
      processedCount: dataPoints.length,
      successCount,
      skippedCount,
      processTime: endTime - startTime,
    };
  }

  private static prefillEmptyCells(
    cells: Map<string, CellData>,
    matrixData: MatrixDataSet
  ): void {
    // 只为有数据的坐标创建单元格，避免创建过多空单元格
    // 这里可以根据需要调整策略
    
    // 当前策略：不预填充空单元格，保持性能
    // 如果需要完整的33x33网格，可以取消注释下面的代码
    
    /*
    for (let y = 0; y < MATRIX_SIZE; y++) {
      for (let x = 0; x < MATRIX_SIZE; x++) {
        const key = coordinateKey(x, y);
        if (!cells.has(key)) {
          const cell = createDefaultCell(x, y);
          cells.set(key, cell);
        }
      }
    }
    */
  }

  private static estimateMemoryUsage(cells: Map<string, CellData>): number {
    // 简单的内存使用估算
    const avgCellSize = 200; // 估算每个单元格对象的大小
    return cells.size * avgCellSize;
  }
}

// ===== 导出便捷函数 =====

/**
 * 快速初始化矩阵数据
 */
export const initializeMatrixData = (matrixData: MatrixDataSet): MatrixData => {
  const data = MatrixDataInitializer.createInitialData();
  const result = MatrixDataInitializer.initializeCellsData(matrixData);
  
  data.cells = result.cells;
  data.lastUpdate = Date.now();
  
  return data;
};

/**
 * 快速创建单元格数据
 */
export const createCellsFromDataPoints = (
  dataPoints: MatrixDataPoint[]
): Map<string, CellData> => {
  return MatrixDataInitializer.createCellsBatch(dataPoints);
};
