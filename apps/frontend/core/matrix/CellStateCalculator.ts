/**
 * 单元格状态计算器
 * 🎯 核心价值：统一的单元格状态计算逻辑，简化复杂的条件判断
 * 📦 功能范围：状态计算、条件判断、逻辑组合、性能优化
 * 🔄 架构设计：策略模式 + 工厂模式，支持扩展和缓存
 */

import type { 
  CellData, 
  CellRenderData, 
  MatrixConfig, 
  Coordinate 
} from './MatrixTypes';
import { 
  createConditionalChain, 
  createStrategySelector,
  CellStateChecker,
  ModeChecker,
  and,
  or,
  not,
  exists
} from '../utils/ConditionalLogicUtils';

// ===== 类型定义 =====

interface CellStateContext {
  x: number;
  y: number;
  cellData?: CellData;
  renderData?: CellRenderData;
  config: MatrixConfig;
  wordInputState?: {
    isActive: boolean;
    selectedCell?: Coordinate | null;
    temporaryWord?: string;
  };
}

interface CellStateResult {
  isEnhanced: boolean;
  isWordInputActive: boolean;
  isInteractive: boolean;
  isVisible: boolean;
  hasContent: boolean;
  displayPriority: number;
  stateFlags: string[];
}

interface CellDisplayContext extends CellStateContext {
  temporaryWord?: string;
}

interface CellDisplayResult {
  content: string;
  shouldShowTemporary: boolean;
  contentType: 'original' | 'temporary' | 'empty';
  contentPriority: number;
}

// ===== 状态计算策略 =====

/**
 * 增强状态计算策略
 */
const enhancedStateStrategy = createStrategySelector<CellStateContext, boolean>()
  .register('color-level1', (ctx) => 
    ModeChecker.isColorMode(ctx.config) && ctx.cellData?.level === 1
  )
  .register('special-cell', (ctx) =>
    Boolean(ctx.cellData?.isActive && ctx.cellData?.level === 1)
  )
  .setDefault(() => false);

/**
 * 填词活跃状态计算策略
 */
const wordInputActiveStrategy = createConditionalChain<CellStateContext, boolean>()
  .when(
    (ctx) => !ctx.wordInputState?.isActive,
    () => false,
    'word-input-inactive'
  )
  .when(
    (ctx) => !ctx.wordInputState?.selectedCell,
    () => false,
    'no-selected-cell'
  )
  .when(
    and(
      (ctx) => ctx.wordInputState?.selectedCell?.x === ctx.x,
      (ctx) => ctx.wordInputState?.selectedCell?.y === ctx.y
    ),
    () => true,
    'coordinates-match'
  )
  .otherwise(() => false);

/**
 * 交互性计算策略
 */
const interactiveStrategy = createConditionalChain<CellStateContext, boolean>()
  .when(
    (ctx) => CellStateChecker.hasData(ctx.cellData),
    () => true,
    'has-data'
  )
  .when(
    (ctx) => ModeChecker.isColorWordMode(ctx.config),
    () => true,
    'color-word-mode'
  )
  .otherwise(() => false);

/**
 * 可见性计算策略
 */
const visibilityStrategy = createConditionalChain<CellStateContext, boolean>()
  .when(
    (ctx) => exists(ctx.renderData?.content),
    () => true,
    'has-render-content'
  )
  .when(
    (ctx) => CellStateChecker.hasData(ctx.cellData),
    () => true,
    'has-cell-data'
  )
  .when(
    (ctx) => CellStateChecker.isWordInputActive(ctx.x, ctx.y, ctx.wordInputState),
    () => true,
    'word-input-active'
  )
  .otherwise(() => false);

// ===== 显示内容计算策略 =====

/**
 * 内容显示策略
 */
const contentDisplayStrategy = createConditionalChain<CellDisplayContext, CellDisplayResult>()
  .when(
    and(
      (ctx) => CellStateChecker.isWordInputActive(ctx.x, ctx.y, ctx.wordInputState),
      (ctx) => exists(ctx.temporaryWord),
      (ctx) => ModeChecker.isColorWordMode(ctx.config)
    ),
    (ctx) => ({
      content: ctx.temporaryWord || '',
      shouldShowTemporary: true,
      contentType: 'temporary' as const,
      contentPriority: 100,
    }),
    'temporary-word-display'
  )
  .when(
    (ctx) => exists(ctx.renderData?.content),
    (ctx) => ({
      content: ctx.renderData?.content || '',
      shouldShowTemporary: false,
      contentType: 'original' as const,
      contentPriority: 50,
    }),
    'original-content-display'
  )
  .otherwise(() => ({
    content: '',
    shouldShowTemporary: false,
    contentType: 'empty' as const,
    contentPriority: 0,
  }));

// ===== 主计算器类 =====

export class CellStateCalculator {
  private static cache = new Map<string, CellStateResult>();
  private static displayCache = new Map<string, CellDisplayResult>();

  /**
   * 计算单元格状态
   */
  static calculateState(context: CellStateContext): CellStateResult {
    const cacheKey = this.generateStateKey(context);
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    const result = this.computeState(context);
    this.cache.set(cacheKey, result);
    
    return result;
  }

  /**
   * 计算显示内容
   */
  static calculateDisplay(context: CellDisplayContext): CellDisplayResult {
    const cacheKey = this.generateDisplayKey(context);
    
    if (this.displayCache.has(cacheKey)) {
      return this.displayCache.get(cacheKey)!;
    }

    const result = contentDisplayStrategy.execute(context) as CellDisplayResult;
    this.displayCache.set(cacheKey, result);
    
    return result;
  }

  /**
   * 批量计算状态
   */
  static calculateBatchStates(contexts: CellStateContext[]): CellStateResult[] {
    return contexts.map(context => this.calculateState(context));
  }

  /**
   * 清理缓存
   */
  static clearCache(): void {
    this.cache.clear();
    this.displayCache.clear();
  }

  /**
   * 获取缓存统计
   */
  static getCacheStats(): { stateCache: number; displayCache: number } {
    return {
      stateCache: this.cache.size,
      displayCache: this.displayCache.size,
    };
  }

  // ===== 私有方法 =====

  private static computeState(context: CellStateContext): CellStateResult {
    const stateFlags: string[] = [];
    
    // 计算增强状态
    const isEnhanced = Boolean(enhancedStateStrategy.execute('color-level1', context));
    if (isEnhanced) stateFlags.push('enhanced');

    // 计算填词活跃状态
    const isWordInputActive = wordInputActiveStrategy.execute(context) as boolean;
    if (isWordInputActive) stateFlags.push('word-input-active');

    // 计算交互性
    const isInteractive = interactiveStrategy.execute(context) as boolean;
    if (isInteractive) stateFlags.push('interactive');

    // 计算可见性
    const isVisible = visibilityStrategy.execute(context) as boolean;
    if (isVisible) stateFlags.push('visible');

    // 计算内容存在性
    const hasContent = exists(context.renderData?.content) || CellStateChecker.hasData(context.cellData);
    if (hasContent) stateFlags.push('has-content');

    // 计算显示优先级
    let displayPriority = 0;
    if (isEnhanced) displayPriority += 100;
    if (isWordInputActive) displayPriority += 50;
    if (CellStateChecker.isSelected(context.cellData)) displayPriority += 30;
    if (CellStateChecker.isFocused(context.cellData)) displayPriority += 20;
    if (CellStateChecker.isHovered(context.cellData)) displayPriority += 10;

    return {
      isEnhanced,
      isWordInputActive,
      isInteractive,
      isVisible,
      hasContent,
      displayPriority,
      stateFlags,
    };
  }

  private static generateStateKey(context: CellStateContext): string {
    return JSON.stringify({
      x: context.x,
      y: context.y,
      cellLevel: context.cellData?.level,
      cellColor: context.cellData?.color,
      cellSelected: context.cellData?.isSelected,
      cellHovered: context.cellData?.isHovered,
      cellFocused: context.cellData?.isFocused,
      configMode: context.config.mainMode,
      configContent: context.config.contentMode,
      wordInputActive: context.wordInputState?.isActive,
      selectedCellX: context.wordInputState?.selectedCell?.x,
      selectedCellY: context.wordInputState?.selectedCell?.y,
    });
  }

  private static generateDisplayKey(context: CellDisplayContext): string {
    return JSON.stringify({
      x: context.x,
      y: context.y,
      renderContent: context.renderData?.content,
      temporaryWord: context.temporaryWord,
      configMode: context.config.mainMode,
      configContent: context.config.contentMode,
      wordInputActive: context.wordInputState?.isActive,
      selectedCellX: context.wordInputState?.selectedCell?.x,
      selectedCellY: context.wordInputState?.selectedCell?.y,
    });
  }
}

// ===== 便捷函数 =====

/**
 * 快速计算单元格是否增强
 */
export const isEnhancedCell = (
  cellData: CellData | undefined,
  config: MatrixConfig
): boolean => {
  return CellStateCalculator.calculateState({
    x: cellData?.x || 0,
    y: cellData?.y || 0,
    cellData,
    config,
  }).isEnhanced;
};

/**
 * 快速计算填词活跃状态
 */
export const isWordInputActiveCell = (
  x: number,
  y: number,
  wordInputState: any
): boolean => {
  return CellStateCalculator.calculateState({
    x,
    y,
    config: {} as MatrixConfig,
    wordInputState,
  }).isWordInputActive;
};

/**
 * 快速计算显示内容
 */
export const calculateDisplayContent = (
  x: number,
  y: number,
  renderData: CellRenderData | undefined,
  config: MatrixConfig,
  wordInputState?: any,
  temporaryWord?: string
): string => {
  return CellStateCalculator.calculateDisplay({
    x,
    y,
    renderData,
    config,
    wordInputState,
    temporaryWord,
  }).content;
};

// ===== 导出类型 =====

export type {
  CellStateContext,
  CellStateResult,
  CellDisplayContext,
  CellDisplayResult,
};
