/**
 * 矩阵系统数据管理 - A-M组完整数据系统
 *
 * 🎯 核心价值：33×33矩阵的完整数据源，支持9种颜色和4个层级，A-M组偏移系统
 * 📦 功能范围：坐标映射、颜色分组、层级管理、组偏移、数据查询、验证统计
 * 🔄 架构设计：基于A组基础数据和偏移配置的高性能数据结构
 *
 * <AUTHOR> System
 * @version 2.0.0
 */

import type {
  BasicColorType,
  ColorValue,
  DataLevel,
  GroupOffsetConfig,
  GroupType
} from '../matrix/MatrixTypes';

import {
  MATRIX_SIZE,
  coordinateKey
} from '../matrix/MatrixTypes';

// ===== 核心数据类型 =====

/** 矩阵数据点 - 单个数据点的完整信息 */
export interface MatrixDataPoint {
  /** X坐标 (0-32) */
  x: number;
  /** Y坐标 (0-32) */
  y: number;
  /** 颜色类型 */
  color: BasicColorType;
  /** 数据级别 (1-4) */
  level: DataLevel;
  /** 所属组 (A-M) */
  group: GroupType;
  /** 唯一标识符 */
  id: string;
}

/** 矩阵数据集合 - 包含所有数据点和索引的完整数据结构 */
export interface MatrixDataSet {
  /** 所有数据点的数组 */
  points: MatrixDataPoint[];
  /** 按颜色分组的索引 */
  byColor: Map<BasicColorType, MatrixDataPoint[]>;
  /** 按级别分组的索引 */
  byLevel: Map<DataLevel, MatrixDataPoint[]>;
  /** 按组分组的索引 */
  byGroup: Map<GroupType, MatrixDataPoint[]>;
  /** 按坐标索引的映射 */
  byCoordinate: Map<string, MatrixDataPoint>;
  /** 元数据信息 */
  metadata: MatrixDataSetMetadata;
}

/** 数据集元数据 */
interface MatrixDataSetMetadata {
  /** 总数据点数量 */
  totalPoints: number;
  /** 各颜色数据点数量统计 */
  colorCounts: Record<BasicColorType, number>;
  /** 各级别数据点数量统计 */
  levelCounts: Record<DataLevel, number>;
  /** 各组数据点数量统计 */
  groupCounts: Record<GroupType, number>;
  /** 最后更新时间戳 */
  lastUpdate: number;
}

// ===== A组基础数据结构 =====

/** A组基础数据结构 - 使用网格坐标 (0-32) 直接存储，便于高效匹配 */
export const GROUP_A_DATA = Object.freeze({
  black: {
    1: [[16, 16] as [number, number]]  // 原显示坐标 [0, 0] -> 网格坐标 [16, 16]
  },
  red: {
    1: [[24, 16] as [number, number]],  // 原显示坐标 [8, 0] -> 网格坐标 [24, 16]
    2: [[20, 16] as [number, number]],  // 原显示坐标 [4, 0] -> 网格坐标 [20, 16]
    3: [[18, 16], [22, 16], [20, 18], [20, 14]] as [number, number][],  // 原显示坐标 [2, 0], [6, 0], [4, 2], [4, -2]
    4: [[17, 16], [19, 16], [21, 16], [23, 16], [18, 17], [18, 15], [19, 18], [19, 14], [20, 17], [20, 19], [20, 15], [20, 13], [22, 17], [22, 15], [21, 18], [21, 14]] as [number, number][]
  },
  orange: {
    1: [[20, 20] as [number, number]],  // 原显示坐标 [4, 4] -> 网格坐标 [20, 20]
    3: [[14, 22], [18, 18], [22, 14]] as [number, number][],  // 原显示坐标 [-2, 6], [2, 2], [6, -2]
    4: [[15, 23], [15, 21], [13, 21], [19, 19], [19, 17], [17, 17], [17, 19], [23, 15], [21, 13], [21, 15]] as [number, number][]
  },
  yellow: {
    1: [[16, 24] as [number, number]],  // 原显示坐标 [0, 8] -> 网格坐标 [16, 24]
    2: [[16, 20] as [number, number]],  // 原显示坐标 [0, 4] -> 网格坐标 [16, 20]
    3: [[16, 18], [16, 22], [18, 20], [14, 20]] as [number, number][],  // 原显示坐标 [0, 2], [0, 6], [2, 4], [-2, 4]
    4: [[16, 17], [16, 19], [16, 21], [16, 23], [17, 18], [17, 20], [17, 22], [15, 18], [15, 20], [15, 22], [18, 19], [18, 21], [14, 19], [14, 21], [13, 20], [19, 20]] as [number, number][]
  },
  green: {
    1: [[12, 20] as [number, number]],  // 原显示坐标 [-4, 4] -> 网格坐标 [12, 20]
    3: [[18, 22], [14, 18], [10, 14]] as [number, number][],  // 原显示坐标 [2, 6], [-2, 2], [-6, -2]
    4: [[17, 23], [19, 21], [17, 21], [13, 19], [15, 19], [15, 17], [13, 17], [9, 15], [11, 15], [11, 13]] as [number, number][]
  },
  cyan: {
    1: [[8, 16] as [number, number]],   // 原显示坐标 [-8, 0] -> 网格坐标 [8, 16]
    2: [[12, 16] as [number, number]],  // 原显示坐标 [-4, 0] -> 网格坐标 [12, 16]
    3: [[14, 16], [10, 16], [12, 18], [12, 14]] as [number, number][],  // 原显示坐标 [-2, 0], [-6, 0], [-4, 2], [-4, -2]
    4: [[9, 16], [11, 16], [13, 16], [15, 16], [10, 17], [10, 15], [11, 18], [11, 14], [12, 17], [12, 19], [12, 15], [12, 13], [14, 17], [14, 15], [13, 18], [13, 14]] as [number, number][]
  },
  blue: {
    1: [[12, 12] as [number, number]],  // 原显示坐标 [-4, -4] -> 网格坐标 [12, 12]
    3: [[10, 18], [14, 14], [18, 10]] as [number, number][],  // 原显示坐标 [-6, 2], [-2, -2], [2, -6]
    4: [[11, 19], [9, 17], [11, 17], [15, 15], [13, 15], [13, 13], [15, 13], [19, 11], [17, 11], [17, 9]] as [number, number][]
  },
  purple: {
    1: [[16, 8] as [number, number]],   // 原显示坐标 [0, -8] -> 网格坐标 [16, 8]
    2: [[16, 12] as [number, number]],  // 原显示坐标 [0, -4] -> 网格坐标 [16, 12]
    3: [[16, 14], [16, 10], [18, 12], [14, 12]] as [number, number][],  // 原显示坐标 [0, -2], [0, -6], [2, -4], [-2, -4]
    4: [[16, 15], [15, 14], [16, 13], [17, 14], [14, 13], [13, 12], [14, 11], [15, 12], [18, 13], [17, 12], [18, 11], [19, 12], [16, 11], [15, 10], [16, 9], [17, 10]] as [number, number][]
  },
  pink: {
    1: [[20, 12] as [number, number]],  // 原显示坐标 [4, -4] -> 网格坐标 [20, 12]
    3: [[22, 18], [18, 14], [14, 10]] as [number, number][],  // 原显示坐标 [6, 2], [2, -2], [-2, -6]
    4: [[13, 11], [15, 9], [15, 11], [17, 15], [17, 13], [19, 13], [19, 15], [21, 19], [23, 17], [21, 17]] as [number, number][]
  }
} as const);

// ===== 可用级别映射 =====

/** 可用级别映射 - 统一的权威数据源 */
export const AVAILABLE_LEVELS: Record<BasicColorType, readonly number[]> = Object.freeze({
  red: [1, 2, 3, 4],
  cyan: [1, 2, 3, 4],
  yellow: [1, 2, 3, 4],
  purple: [1, 2, 3, 4],
  orange: [1, 3, 4],
  green: [1, 3, 4],
  blue: [1, 3, 4],
  pink: [1, 3, 4],
  black: [1], // 黑色只有一个级别
  white: [1], // 白色只有一个级别
  gray: [1], // 灰色只有一个级别
  brown: [1], // 棕色只有一个级别
} as const);

// ===== 默认颜色值定义 =====

/** 默认颜色值与颜色数字映射 */
export const DEFAULT_COLOR_VALUES = Object.freeze({
  black: { name: '黑色', hex: '#000000', rgb: [0, 0, 0], hsl: [0, 0, 0], mappingValue: 'group' as const }, // 黑色使用组字符映射
  red: { name: '红色', hex: '#ef4444', rgb: [239, 68, 68], hsl: [0, 84, 60], mappingValue: 1 as const },
  cyan: { name: '青色', hex: '#06b6d4', rgb: [6, 182, 212], hsl: [189, 94, 43], mappingValue: 5 as const },
  yellow: { name: '黄色', hex: '#eab308', rgb: [234, 179, 8], hsl: [45, 93, 47], mappingValue: 3 as const },
  purple: { name: '紫色', hex: '#a855f7', rgb: [168, 85, 247], hsl: [271, 91, 65], mappingValue: 7 as const },
  orange: { name: '橙色', hex: '#f97316', rgb: [249, 115, 22], hsl: [25, 95, 53], mappingValue: 2 as const },
  green: { name: '绿色', hex: '#22c55e', rgb: [34, 197, 94], hsl: [142, 71, 45], mappingValue: 4 as const },
  blue: { name: '蓝色', hex: '#3b82f6', rgb: [59, 130, 246], hsl: [217, 91, 60], mappingValue: 6 as const },
  pink: { name: '粉色', hex: '#ec4899', rgb: [236, 72, 153], hsl: [327, 82, 60], mappingValue: 8 as const },
} as const) as Record<BasicColorType, ColorValue>;

// ===== 矩阵计算常量 =====

/** 最大数据级别 */
export const MAX_LEVEL = 4;



/** 网格中心点坐标 */
export const GRID_CENTER = {
  X: Math.floor(MATRIX_SIZE / 2), // 中心列索引
  Y: Math.floor(MATRIX_SIZE / 2)  // 中心行索引
} as const;

// ===== A到M组偏移配置 =====

/**
 * 创建统一偏移配置 - 所有颜色使用相同偏移值
 * @param offset 偏移值 [x, y]
 * @returns 统一偏移配置
 */
const createUniformOffsetConfig = (offset: [number, number]): GroupOffsetConfig => ({
  defaultOffset: offset,
  level1Offsets: {
    red: offset, orange: offset, yellow: offset, purple: offset,
    green: offset, cyan: offset, blue: offset, pink: offset, black: offset,
    white: offset, gray: offset, brown: offset
  }
});

/** A到M组偏移配置 - 基于对称性和规律性设计 */
export const GROUP_OFFSET_CONFIGS: Record<GroupType, GroupOffsetConfig> = {
  // 基础组 - A组无偏移，作为所有其他组的基础
  A: {
    defaultOffset: [0, 0]
  },

  // 方向偏移组 - 基于四个主要方向
  B: {
    defaultOffset: [16, 0], // 向右偏移
    level1Offsets: {
      red: [0, 0], orange: [8, -8], yellow: [16, -16], green: [24, 0], cyan: [32, 0],
      blue: [24, 0], purple: [16, 16], pink: [8, 8], black: [16, 0],
      white: [16, 0], gray: [16, 0], brown: [16, 0]
    }
  },
  C: {
    defaultOffset: [-16, 0], // 向左偏移
    level1Offsets: {
      red: [32, 0], orange: [-24, -8], yellow: [-16, -16], green: [-8, -8], cyan: [0, 0],
      blue: [-8, 8], purple: [-16, 16], pink: [-24, 8], black: [-16, 0],
      white: [-16, 0], gray: [-16, 0], brown: [-16, 0]
    }
  },
  D: {
    defaultOffset: [0, -16], // 向下偏移
    level1Offsets: {red: [-16, -16], orange: [-8, -24], yellow: [0, -32], green: [8, -24],
      cyan: [16, -16], blue: [8, -8], purple: [0, 0], pink: [-8, -8], black: [0, -16],
      white: [0, -16], gray: [0, -16], brown: [0, -16]}
  },
  E: {
    defaultOffset: [0, 16], // 向上偏移
    level1Offsets: {red: [-16, 16], orange: [-8, 8], yellow: [0, 16], green: [8, 8],
      cyan: [16, 16], blue: [8, 24], purple: [-32, 0], pink: [-8, 24], black: [0, 16],
      white: [0, 16], gray: [0, 16], brown: [0, 16]}
  },

  // 对角偏移组 - 基于四个对角方向
  F: {
    defaultOffset: [8, -8], // 右下偏移
    level1Offsets: {red: [0, 0], orange: [8, -8], yellow: [16, -16], green: [16, -16], cyan: [16, -16],
      blue: [8, -8], purple: [0, 0], pink: [0, 0], black: [8, -8],
      white: [8, -8], gray: [8, -8], brown: [8, -8]}
  },
  G: {
    defaultOffset: [-8, 8], // 左上偏移
    level1Offsets: {red: [-16, 16], orange: [-8, 8], yellow: [0, 0], green: [0, 0], cyan: [0, 0],
      blue: [-8, 8], purple: [-16, 16], pink: [-16, 16], black: [-8, 8],
      white: [-8, 8], gray: [-8, 8], brown: [-8, 8]}
  },
  H: {
    defaultOffset: [8, 8], // 右上偏移
    level1Offsets: {red: [0, 0], orange: [0, 0], yellow: [0, 0], green: [8, 8], cyan: [16, 16],
      blue: [16, 16], purple: [16, 16], pink: [8, 8], black: [8, 8],
      white: [8, 8], gray: [8, 8], brown: [8, 8]}
  },
  I: {
    defaultOffset: [-8, -8], // 左下偏移
    level1Offsets: {red: [-16, -16], orange: [-16, -16], yellow: [-16, -16], green: [-8, -8], cyan: [0, 0],
      blue: [0, 0], purple: [0, 0], pink: [-8, -8], black: [-8, -8],
      white: [-8, -8], gray: [-8, -8], brown: [-8, -8]}
  },

  // 统一偏移组 - 所有颜色使用相同偏移值，便于批量处理
  J: createUniformOffsetConfig([16, -16]),   // 右下大偏移
  K: createUniformOffsetConfig([-16, 16]), // 左上大偏移
  L: createUniformOffsetConfig([16, 16]),  // 右上大偏移
  M: createUniformOffsetConfig([-16, -16])   // 左下大偏移
};

// ===== 通用工具函数 =====



// ===== 数据处理工具函数 =====

/**
 * 将网格坐标转换为以(16,16)为中心点(0,0)的显示坐标
 * @param gridX 网格X坐标 (0-32)
 * @param gridY 网格Y坐标 (0-32)
 * @returns 显示坐标 [displayX, displayY]，范围(-16到16)
 */
export const toDisplayCoordinate = (gridX: number, gridY: number): [number, number] => {
  return [gridX - GRID_CENTER.X, GRID_CENTER.Y - gridY];
};

/**
 * 将显示坐标转换为网格坐标
 * @param displayX 显示X坐标 (-16到16)
 * @param displayY 显示Y坐标 (-16到16)
 * @returns 网格坐标 [gridX, gridY]，范围(0-32)
 */
export const fromDisplayCoordinate = (displayX: number, displayY: number): [number, number] => {
  return [displayX + GRID_CENTER.X, displayY + GRID_CENTER.Y];
};





/** 检查网格坐标是否在有效范围内 */
export const isValidCoordinate = (x: number, y: number): boolean => {
  return x >= 0 && x < MATRIX_SIZE && y >= 0 && y < MATRIX_SIZE;
};



// ===== 数据生成函数 =====

/**
 * 从A组基础数据生成指定组的数据点
 * @param group 目标组类型，默认为'A'
 * @returns 生成的数据点数组
 * @throws {Error} 当组类型无效时抛出错误
 */
export const generateGroupData = (group: GroupType = 'A'): MatrixDataPoint[] => {
  // 输入验证
  if (!ALL_GROUPS.includes(group)) {
    throw new Error(`无效的组类型: ${group}. 有效值为: ${ALL_GROUPS.join(', ')}`);
  }

  const points: MatrixDataPoint[] = [];
  const config = GROUP_OFFSET_CONFIGS[group];

  if (!config) {
    throw new Error(`组 ${group} 的偏移配置未找到`);
  }

  // 遍历所有颜色和级别，生成数据点
  for (const [colorKey, levels] of Object.entries(GROUP_A_DATA)) {
    const color = colorKey as BasicColorType;

    for (const [levelKey, coordinates] of Object.entries(levels)) {
      const level = parseInt(levelKey) as DataLevel;

      // 验证级别有效性
      if (!ALL_LEVELS.includes(level)) {
        continue;
      }

      // 为当前颜色和级别的每个坐标生成数据点
      coordinates.forEach((gridCoord, index) => {
        const dataPoint = createDataPoint(
          gridCoord,
          color,
          level,
          group,
          config,
          index
        );

        if (dataPoint) {
          points.push(dataPoint);
        }
      });
    }
  }

  return points;
};

/**
 * 创建单个数据点
 * @param gridCoord 网格坐标 (已经是绝对坐标)
 * @param color 颜色类型
 * @param level 数据级别
 * @param group 组类型
 * @param config 组偏移配置
 * @param index 坐标索引
 * @returns 数据点对象，如果坐标无效则返回null
 */
function createDataPoint(
  gridCoord: [number, number],
  color: BasicColorType,
  level: DataLevel,
  group: GroupType,
  config: GroupOffsetConfig,
  index: number
): MatrixDataPoint | null {
  // A组数据现在直接使用网格坐标，无需转换
  const [baseX, baseY] = gridCoord;

  // 确定偏移值：level1使用特殊偏移，其他级别使用默认偏移
  const offset = (config.level1Offsets && level === 1)
    ? config.level1Offsets[color] || config.defaultOffset
    : config.defaultOffset;

  // 应用偏移
  const [finalX, finalY] = [baseX + offset[0], baseY + offset[1]];

  // 验证坐标有效性
  if (!isValidCoordinate(finalX, finalY)) {
    return null;
  }

  return {
    x: finalX,
    y: finalY,
    color,
    level,
    group,
    id: `${group.toLowerCase()}${level}-${color}-${index + 1}`
  };
}



/**
 * 创建完整的矩阵数据集
 * @param groups 要包含的组类型数组，默认为['A']
 * @returns 完整的矩阵数据集，包含所有数据点和索引
 * @throws {Error} 当输入参数无效时抛出错误
 */
export const createMatrixDataSet = (groups: GroupType[] = ['A']): MatrixDataSet => {
  // 输入验证
  if (!Array.isArray(groups) || groups.length === 0) {
    throw new Error('组数组不能为空');
  }

  // 验证所有组类型
  const invalidGroups = groups.filter(group => !ALL_GROUPS.includes(group));
  if (invalidGroups.length > 0) {
    throw new Error(`无效的组类型: ${invalidGroups.join(', ')}`);
  }

  // 去重并排序组数组
  const uniqueGroups = [...new Set(groups)].sort();

  // 生成所有组的数据点
  const allPoints: MatrixDataPoint[] = [];
  for (const group of uniqueGroups) {
    const groupPoints = generateGroupData(group);
    allPoints.push(...groupPoints);
  }

  // 创建索引映射
  const indices = createDataIndices(allPoints);

  // 生成统计信息
  const metadata = generateMetadata(allPoints);

  return {
    points: allPoints,
    ...indices,
    metadata
  };
};

/**
 * 创建数据索引映射
 * @param points 数据点数组
 * @returns 索引映射对象
 */
function createDataIndices(points: MatrixDataPoint[]) {
  const byColor = new Map<BasicColorType, MatrixDataPoint[]>();
  const byLevel = new Map<DataLevel, MatrixDataPoint[]>();
  const byGroup = new Map<GroupType, MatrixDataPoint[]>();
  const byCoordinate = new Map<string, MatrixDataPoint>();

  for (const point of points) {
    // 按颜色分组
    const colorArray = byColor.get(point.color);
    colorArray ? colorArray.push(point) : byColor.set(point.color, [point]);

    // 按级别分组
    const levelArray = byLevel.get(point.level);
    levelArray ? levelArray.push(point) : byLevel.set(point.level, [point]);

    // 按组分组
    const groupArray = byGroup.get(point.group);
    groupArray ? groupArray.push(point) : byGroup.set(point.group, [point]);

    // 按坐标索引（坐标唯一，直接设置）
    byCoordinate.set(coordinateKey(point.x, point.y), point);
  }

  return { byColor, byLevel, byGroup, byCoordinate };
}



/**
 * 生成数据集元数据
 * @param points 数据点数组
 * @returns 元数据对象
 */
function generateMetadata(points: MatrixDataPoint[]): MatrixDataSetMetadata {
  // 初始化计数器
  const colorCounts = Object.fromEntries(ALL_COLORS.map(key => [key, 0])) as Record<BasicColorType, number>;
  const levelCounts = Object.fromEntries(ALL_LEVELS.map(key => [key, 0])) as Record<DataLevel, number>;
  const groupCounts = Object.fromEntries(ALL_GROUPS.map(key => [key, 0])) as Record<GroupType, number>;

  // 统计各类数据
  for (const point of points) {
    colorCounts[point.color]++;
    levelCounts[point.level]++;
    groupCounts[point.group]++;
  }

  return {
    totalPoints: points.length,
    colorCounts,
    levelCounts,
    groupCounts,
    lastUpdate: Date.now()
  };
}

/** 所有颜色类型的常量数组 */
const ALL_COLORS = Object.keys(DEFAULT_COLOR_VALUES) as BasicColorType[];

/** 所有数据级别的常量数组 */
const ALL_LEVELS: readonly DataLevel[] = [1, 2, 3, 4];

/** 所有组类型的常量数组 */
const ALL_GROUPS = Object.keys(GROUP_OFFSET_CONFIGS) as GroupType[];



// ===== 数据查询函数 =====

/**
 * 根据坐标获取数据点
 * @param dataSet 数据集
 * @param x X坐标
 * @param y Y坐标
 * @returns 数据点对象，如果不存在则返回null
 */
export const getMatrixDataByCoordinate = (
  dataSet: MatrixDataSet,
  x: number,
  y: number
): MatrixDataPoint | null => {
  const key = coordinateKey(x, y);
  return dataSet.byCoordinate.get(key) ?? null;
};

/**
 * 通用数据查询函数 - 根据不同类型获取数据点
 * @param dataSet 数据集
 * @param type 查询类型
 * @param value 查询值
 * @returns 符合条件的数据点数组
 */
export const getMatrixDataBy = <T extends BasicColorType | DataLevel | GroupType>(
  dataSet: MatrixDataSet,
  type: 'color' | 'level' | 'group',
  value: T
): MatrixDataPoint[] => {
  switch (type) {
    case 'color':
      return dataSet.byColor.get(value as BasicColorType) ?? [];
    case 'level':
      return dataSet.byLevel.get(value as DataLevel) ?? [];
    case 'group':
      return dataSet.byGroup.get(value as GroupType) ?? [];
    default:
      return [];
  }
};





/**
 * 获取多个组的数据点
 * @param dataSet 数据集
 * @param groups 组类型数组
 * @returns 所有指定组的数据点数组
 */
export const getMatrixDataByGroups = (
  dataSet: MatrixDataSet,
  groups: GroupType[]
): MatrixDataPoint[] => {
  const result: MatrixDataPoint[] = [];
  for (const group of groups) {
    result.push(...getMatrixDataBy(dataSet, 'group', group));
  }
  return result;
};

/**
 * 根据条件过滤数据点
 * @param dataSet 数据集
 * @param predicate 过滤条件函数
 * @returns 符合条件的数据点数组
 */
export const filterMatrixData = (
  dataSet: MatrixDataSet,
  predicate: (point: MatrixDataPoint) => boolean
): MatrixDataPoint[] => {
  return dataSet.points.filter(predicate);
};

/**
 * 获取数据点的实际映射值
 * @param point 数据点
 * @returns 映射值（数字或组字符）
 */
export const getDataPointMappingValue = (point: MatrixDataPoint): string | number => {
  const colorValue = DEFAULT_COLOR_VALUES[point.color];

  if (colorValue.mappingValue === 'group') {
    // 黑色使用组字符
    return point.group;
  }

  // 其他颜色使用数字映射值
  return colorValue.mappingValue ?? point.level;
};

/**
 * 获取颜色的映射值（静态）
 * @param color 颜色类型
 * @returns 映射值配置
 */
export const getColorMappingValue = (color: BasicColorType): number | 'group' | undefined => {
  return DEFAULT_COLOR_VALUES[color].mappingValue;
};

/**
 * 获取数据点的坐标信息（包含网格坐标和显示坐标）
 * @param point 数据点
 * @returns 坐标信息对象
 */
export const getDataPointCoordinateInfo = (point: MatrixDataPoint) => {
  const gridCoord: [number, number] = [point.x, point.y];
  const displayCoord = toDisplayCoordinate(point.x, point.y);

  return {
    grid: {
      x: gridCoord[0],
      y: gridCoord[1],
      formatted: `(${gridCoord[0]},${gridCoord[1]})`
    },
    display: {
      x: displayCoord[0],
      y: displayCoord[1],
      formatted: `(${displayCoord[0]},${displayCoord[1]})`
    },
    isCenter: displayCoord[0] === 0 && displayCoord[1] === 0
  };
};

/**
 * 根据显示坐标查找数据点
 * @param dataSet 数据集
 * @param displayX 显示X坐标
 * @param displayY 显示Y坐标
 * @returns 数据点对象，如果不存在则返回null
 */
export const getMatrixDataByDisplayCoordinate = (
  dataSet: MatrixDataSet,
  displayX: number,
  displayY: number
): MatrixDataPoint | null => {
  const [gridX, gridY] = fromDisplayCoordinate(displayX, displayY);
  return getMatrixDataByCoordinate(dataSet, gridX, gridY);
};

// ===== 坐标系统验证函数 =====

/** 坐标系统验证结果 */
export interface CoordinateSystemValidation {
  /** 是否有效 */
  isValid: boolean;
  /** 验证信息 */
  messages: string[];
}

/**
 * 验证坐标系统的一致性
 * @returns 坐标系统验证结果
 */
export const validateCoordinateSystem = (): CoordinateSystemValidation => {
  const messages: string[] = [];

  // 验证中心点
  const centerGrid: [number, number] = [GRID_CENTER.X, GRID_CENTER.Y];
  const centerDisplay = toDisplayCoordinate(centerGrid[0], centerGrid[1]);

  if (centerDisplay[0] !== 0 || centerDisplay[1] !== 0) {
    messages.push(`中心点显示坐标应为(0,0)，实际为(${centerDisplay[0]},${centerDisplay[1]})`);
  }

  // 验证双向转换
  const backToGrid = fromDisplayCoordinate(centerDisplay[0], centerDisplay[1]);
  if (backToGrid[0] !== centerGrid[0] || backToGrid[1] !== centerGrid[1]) {
    messages.push(`坐标转换不一致：${centerGrid} -> ${centerDisplay} -> ${backToGrid}`);
  }

  const isValid = centerDisplay[0] === 0 && centerDisplay[1] === 0;
  if (isValid) {
    messages.push('坐标系统验证通过');
  }

  return {
    isValid,
    messages
  };
};

// ===== 数据验证和统计函数 =====

/** 数据验证结果 */
export interface ValidationResult {
  /** 是否通过验证 */
  isValid: boolean;
  /** 错误信息数组 */
  errors: string[];
  /** 警告信息数组 */
  warnings: string[];
}

/**
 * 验证数据集完整性
 * @param dataSet 要验证的数据集
 * @returns 验证结果，包含错误和警告信息
 */
export const validateMatrixDataSet = (dataSet: MatrixDataSet): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 验证坐标范围
  for (const point of dataSet.points) {
    if (!isValidCoordinate(point.x, point.y)) {
      errors.push(`数据点 ${point.id} 坐标 (${point.x}, ${point.y}) 超出网格范围`);
    }
  }

  // 检查重复坐标
  const coordinateMap = new Map<string, MatrixDataPoint[]>();
  for (const point of dataSet.points) {
    const key = coordinateKey(point.x, point.y);
    const existing = coordinateMap.get(key);
    if (existing) {
      existing.push(point);
    } else {
      coordinateMap.set(key, [point]);
    }
  }

  for (const [coordinate, pointsAtCoord] of coordinateMap) {
    if (pointsAtCoord.length > 1) {
      const ids = pointsAtCoord.map(p => p.id).join(', ');
      warnings.push(`坐标 ${coordinate} 有 ${pointsAtCoord.length} 个重叠数据点: ${ids}`);
    }
  }

  // 检查组数据完整性
  for (const group of ALL_GROUPS) {
    const groupData = getMatrixDataBy(dataSet, 'group', group);
    if (groupData.length === 0) {
      warnings.push(`组 ${group} 没有数据点`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/** 数据集统计信息 */
export interface MatrixDataStatistics {
  /** 总数据点数量 */
  totalPoints: number;
  /** 按颜色统计 */
  colorCounts: Record<BasicColorType, number>;
  /** 按级别统计 */
  levelCounts: Record<DataLevel, number>;
  /** 按组统计 */
  groupCounts: Record<GroupType, number>;
}

/**
 * 获取数据集基本统计信息
 * @param dataSet 数据集
 * @returns 基本统计信息
 */
export const getMatrixDataStatistics = (dataSet: MatrixDataSet): MatrixDataStatistics => {
  return {
    totalPoints: dataSet.metadata.totalPoints,
    colorCounts: dataSet.metadata.colorCounts,
    levelCounts: dataSet.metadata.levelCounts,
    groupCounts: dataSet.metadata.groupCounts
  };
};





// ===== 预定义数据集 =====

/** 获取A组数据集 */
export const getOptimizedGroupAData = () => createMatrixDataSet(['A']);

/** 获取完整的A-M组数据集 */
export const getCompleteMatrixData = () => createMatrixDataSet(['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M']);

// ===== 简化的缓存机制 =====

// 简单的缓存存储
const dataCache = new Map<string, MatrixDataSet>();

/** 获取缓存的A组数据集 */
export const getCachedGroupAData = () => {
  const key = 'A';
  return dataCache.get(key) || (() => {
    const dataSet = createMatrixDataSet(['A']);
    dataCache.set(key, dataSet);
    return dataSet;
  })();
};

/** 获取缓存的完整数据集 */
export const getCachedCompleteData = () => {
  const key = 'ALL';
  return dataCache.get(key) || (() => {
    const dataSet = createMatrixDataSet(ALL_GROUPS);
    dataCache.set(key, dataSet);
    return dataSet;
  })();
};

/** 清除数据集缓存 */
export const clearDataCache = () => dataCache.clear();

// ===== 默认导出 =====

// 导出主要的数据管理函数作为默认导出
export default {
  generateGroupData,
  createMatrixDataSet,
  getCachedGroupAData,
  getCachedCompleteData,
  clearDataCache
};
