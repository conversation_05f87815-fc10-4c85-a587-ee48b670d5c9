/**
 * Toast状态管理
 * 🎯 核心价值：全局Toast消息管理，支持多个消息同时显示
 * 📦 功能范围：添加、移除、清空Toast消息
 * 🔄 架构设计：基于Zustand的轻量级状态管理
 */

'use client';

import { create } from 'zustand';

// ===== 类型定义 =====

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export type ToastPosition = 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center' | 'center';

export interface ToastOptions {
  /** 显示时长（毫秒），0表示不自动消失 */
  duration?: number;
  /** 是否可手动关闭 */
  closable?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 显示位置 */
  position?: ToastPosition;
}

export interface ToastMessage {
  id: string;
  type: ToastType;
  message: string;
  duration: number;
  closable: boolean;
  className?: string;
  position: ToastPosition;
  createdAt: number;
}

interface ToastStore {
  /** Toast消息列表 */
  toasts: ToastMessage[];

  /** 添加Toast消息 */
  addToast: (message: string, type?: ToastType, options?: ToastOptions) => string;

  /** 移除Toast消息 */
  removeToast: (id: string) => void;

  /** 清空所有Toast消息 */
  clearToasts: () => void;

  /** 显示成功消息 */
  showSuccess: (message: string, options?: ToastOptions) => string;

  /** 显示错误消息 */
  showError: (message: string, options?: ToastOptions) => string;

  /** 显示警告消息 */
  showWarning: (message: string, options?: ToastOptions) => string;

  /** 显示信息消息 */
  showInfo: (message: string, options?: ToastOptions) => string;
}

// ===== 工具函数 =====

/** 生成唯一ID */
const generateId = (): string => {
  return `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/** 默认配置 */
const DEFAULT_DURATIONS: Record<ToastType, number> = {
  success: 3000,
  info: 3000,
  warning: 4000,
  error: 5000,
};

// ===== Toast Store =====

export const useToastStore = create<ToastStore>((set, get) => ({
  toasts: [],

  addToast: (message, type = 'info', options = {}) => {
    const id = generateId();
    const duration = options.duration ?? DEFAULT_DURATIONS[type];

    const newToast: ToastMessage = {
      id,
      type,
      message,
      duration,
      closable: options.closable ?? true,
      className: options.className,
      position: options.position ?? 'top-right',
      createdAt: Date.now(),
    };

    set((state) => ({
      toasts: [...state.toasts, newToast]
    }));

    // 自动移除（如果设置了duration且大于0）
    if (duration > 0) {
      setTimeout(() => {
        get().removeToast(id);
      }, duration);
    }

    return id;
  },

  removeToast: (id) => {
    set((state) => ({
      toasts: state.toasts.filter(toast => toast.id !== id)
    }));
  },

  clearToasts: () => {
    set({ toasts: [] });
  },

  showSuccess: (message, options) => {
    return get().addToast(message, 'success', options);
  },

  showError: (message, options) => {
    return get().addToast(message, 'error', options);
  },

  showWarning: (message, options) => {
    return get().addToast(message, 'warning', options);
  },

  showInfo: (message, options) => {
    return get().addToast(message, 'info', options);
  }
}));

// ===== 便捷API =====

/** 全局toast实例 */
export const toast = {
  /** 显示成功消息 */
  success: (message: string, options?: ToastOptions) => {
    return useToastStore.getState().showSuccess(message, options);
  },

  /** 显示错误消息 */
  error: (message: string, options?: ToastOptions) => {
    return useToastStore.getState().showError(message, options);
  },

  /** 显示警告消息 */
  warning: (message: string, options?: ToastOptions) => {
    return useToastStore.getState().showWarning(message, options);
  },

  /** 显示信息消息 */
  info: (message: string, options?: ToastOptions) => {
    return useToastStore.getState().showInfo(message, options);
  },

  /** 显示自定义消息 */
  show: (message: string, type?: ToastType, options?: ToastOptions) => {
    return useToastStore.getState().addToast(message, type, options);
  },

  /** 移除指定消息 */
  remove: (id: string) => {
    return useToastStore.getState().removeToast(id);
  },

  /** 清空所有消息 */
  clear: () => {
    return useToastStore.getState().clearToasts();
  }
};

// ===== 向后兼容的便捷函数 =====

/** @deprecated 使用 toast.success() 替代 */
export const showSuccessToast = (message: string, options?: ToastOptions) => {
  return toast.success(message, options);
};

/** @deprecated 使用 toast.error() 替代 */
export const showErrorToast = (message: string, options?: ToastOptions) => {
  return toast.error(message, options);
};

/** @deprecated 使用 toast.warning() 替代 */
export const showWarningToast = (message: string, options?: ToastOptions) => {
  return toast.warning(message, options);
};

/** @deprecated 使用 toast.info() 替代 */
export const showInfoToast = (message: string, options?: ToastOptions) => {
  return toast.info(message, options);
};

// ===== 导出类型 =====

export type { ToastStore };
