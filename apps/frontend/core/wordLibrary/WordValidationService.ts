/**
 * 词语验证服务
 * 🎯 核心价值：统一的词语验证逻辑，支持多种验证规则和策略
 * 📦 功能范围：格式验证、重复检测、跨词库验证、批量验证
 * 🔄 架构设计：静态服务类，提供纯函数式的验证方法
 */

import type {
  BasicColorType,
  DataLevel,
  WordEntry,
  WordLibrary,
  WordLibraryKey,
  WordValidationResult
} from '../matrix/MatrixTypes';

import { WORD_LENGTH_LIMITS } from './WordLibraryCore';

// ===== 验证规则接口 =====

interface ValidationRule {
  name: string;
  validate: (text: string) => ValidationResult;
  priority: number; // 优先级，数字越小优先级越高
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

interface DuplicateCheckResult {
  isDuplicate: boolean;
  duplicateLibraries: WordLibraryKey[];
  conflictType: 'same-library' | 'cross-library' | 'none';
}

interface BatchValidationResult {
  results: Map<string, WordValidationResult>;
  summary: {
    totalWords: number;
    validWords: number;
    invalidWords: number;
    duplicateWords: number;
  };
}

// ===== 预定义验证规则 =====

const VALIDATION_RULES: ValidationRule[] = [
  {
    name: 'length',
    priority: 1,
    validate: (text: string): ValidationResult => {
      const trimmed = text.trim();
      const errors: string[] = [];

      if (trimmed.length < WORD_LENGTH_LIMITS.MIN) {
        errors.push(`词语长度不能少于${WORD_LENGTH_LIMITS.MIN}个字符`);
      }

      if (trimmed.length > WORD_LENGTH_LIMITS.MAX) {
        errors.push(`词语长度不能超过${WORD_LENGTH_LIMITS.MAX}个字符`);
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    name: 'format',
    priority: 2,
    validate: (text: string): ValidationResult => {
      const trimmed = text.trim();
      const errors: string[] = [];
      const warnings: string[] = [];

      // 检查是否为空
      if (!trimmed) {
        errors.push('词语不能为空');
        return { isValid: false, errors };
      }

      // 检查特殊字符
      const hasSpecialChars = /[^\u4e00-\u9fa5a-zA-Z0-9]/.test(trimmed);
      if (hasSpecialChars) {
        warnings.push('词语包含特殊字符，建议使用中文、英文或数字');
      }

      // 检查是否全为数字
      if (/^\d+$/.test(trimmed)) {
        warnings.push('词语全为数字，建议添加文字说明');
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      };
    },
  },
  {
    name: 'content',
    priority: 3,
    validate: (text: string): ValidationResult => {
      const trimmed = text.trim();
      const errors: string[] = [];
      const warnings: string[] = [];

      // 检查重复字符
      const chars = trimmed.split('');
      const uniqueChars = new Set(chars);
      if (uniqueChars.size === 1 && chars.length > 1) {
        warnings.push('词语由相同字符组成，建议使用更有意义的词语');
      }

      // 检查常见无意义词语
      const meaninglessWords = ['测试', 'test', '111', '222', '333'];
      if (meaninglessWords.includes(trimmed.toLowerCase())) {
        warnings.push('建议使用更有意义的词语');
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      };
    },
  },
];

// ===== 词语验证服务 =====

export class WordValidationService {
  private static customRules: ValidationRule[] = [];

  /**
   * 添加自定义验证规则
   */
  static addCustomRule(rule: ValidationRule): void {
    this.customRules.push(rule);
    this.customRules.sort((a, b) => a.priority - b.priority);
  }

  /**
   * 移除自定义验证规则
   */
  static removeCustomRule(ruleName: string): boolean {
    const index = this.customRules.findIndex(rule => rule.name === ruleName);
    if (index !== -1) {
      this.customRules.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * 验证词语文本格式
   */
  static validateWordText(text: string): ValidationResult {
    const allRules = [...VALIDATION_RULES, ...this.customRules];
    allRules.sort((a, b) => a.priority - b.priority);

    const allErrors: string[] = [];
    const allWarnings: string[] = [];

    for (const rule of allRules) {
      const result = rule.validate(text);
      allErrors.push(...result.errors);
      if (result.warnings) {
        allWarnings.push(...result.warnings);
      }

      // 如果有错误且是高优先级规则，可以提前返回
      if (result.errors.length > 0 && rule.priority <= 2) {
        return {
          isValid: false,
          errors: result.errors,
          warnings: result.warnings,
        };
      }
    }

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings.length > 0 ? allWarnings : undefined,
    };
  }

  /**
   * 检查词语重复
   */
  static checkWordDuplicate(
    text: string,
    targetLibraryKey: WordLibraryKey,
    libraries: Map<WordLibraryKey, WordLibrary>
  ): DuplicateCheckResult {
    const trimmedText = text.trim();
    const duplicateLibraries: WordLibraryKey[] = [];

    for (const [libraryKey, library] of libraries.entries()) {
      const hasDuplicate = library.words.some(word => word.text === trimmedText);
      
      if (hasDuplicate) {
        duplicateLibraries.push(libraryKey);
      }
    }

    let conflictType: 'same-library' | 'cross-library' | 'none' = 'none';
    
    if (duplicateLibraries.length > 0) {
      if (duplicateLibraries.includes(targetLibraryKey)) {
        conflictType = 'same-library';
      } else {
        conflictType = 'cross-library';
      }
    }

    return {
      isDuplicate: duplicateLibraries.length > 0,
      duplicateLibraries,
      conflictType,
    };
  }

  /**
   * 完整的词语验证
   */
  static validateWord(
    text: string,
    libraryKey: WordLibraryKey,
    libraries: Map<WordLibraryKey, WordLibrary>
  ): WordValidationResult {
    // 格式验证
    const formatValidation = this.validateWordText(text);
    if (!formatValidation.isValid) {
      return {
        isValid: false,
        errors: formatValidation.errors,
        isDuplicate: false,
        duplicateLibraries: [],
      };
    }

    // 重复检测
    const duplicateCheck = this.checkWordDuplicate(text, libraryKey, libraries);

    // 如果在同一词库内重复，直接禁止
    if (duplicateCheck.conflictType === 'same-library') {
      return {
        isValid: false,
        errors: [`词语"${text.trim()}"已存在于当前词库中`],
        isDuplicate: true,
        duplicateLibraries: duplicateCheck.duplicateLibraries,
      };
    }

    // 如果在其他词库中重复，给出警告但允许添加
    const warnings = formatValidation.warnings || [];
    if (duplicateCheck.conflictType === 'cross-library') {
      warnings.push(`词语"${text.trim()}"在其他词库中已存在`);
    }

    return {
      isValid: true,
      errors: [],
      warnings: warnings.length > 0 ? warnings : undefined,
      isDuplicate: duplicateCheck.isDuplicate,
      duplicateLibraries: duplicateCheck.duplicateLibraries,
    };
  }

  /**
   * 批量验证词语
   */
  static validateWordsBatch(
    words: Array<{ text: string; libraryKey: WordLibraryKey }>,
    libraries: Map<WordLibraryKey, WordLibrary>
  ): BatchValidationResult {
    const results = new Map<string, WordValidationResult>();
    let validWords = 0;
    let invalidWords = 0;
    let duplicateWords = 0;

    for (const { text, libraryKey } of words) {
      const result = this.validateWord(text, libraryKey, libraries);
      results.set(text, result);

      if (result.isValid) {
        validWords++;
      } else {
        invalidWords++;
      }

      if (result.isDuplicate) {
        duplicateWords++;
      }
    }

    return {
      results,
      summary: {
        totalWords: words.length,
        validWords,
        invalidWords,
        duplicateWords,
      },
    };
  }

  /**
   * 验证输入前的预检查
   */
  static validateInputBeforeSubmit(
    text: string,
    libraryKey: WordLibraryKey,
    libraries: Map<WordLibraryKey, WordLibrary>
  ): {
    canSubmit: boolean;
    errors: string[];
    warnings: string[];
    suggestions: string[];
  } {
    const validation = this.validateWord(text, libraryKey, libraries);
    const suggestions: string[] = [];

    // 生成建议
    if (!validation.isValid) {
      const trimmed = text.trim();
      
      if (trimmed.length < WORD_LENGTH_LIMITS.MIN) {
        suggestions.push('尝试添加更多字符');
      }
      
      if (trimmed.length > WORD_LENGTH_LIMITS.MAX) {
        suggestions.push('尝试使用更简洁的词语');
      }
      
      if (validation.isDuplicate && validation.duplicateLibraries.includes(libraryKey)) {
        suggestions.push('尝试使用不同的词语或在词语后添加数字');
      }
    }

    return {
      canSubmit: validation.isValid,
      errors: validation.errors,
      warnings: validation.warnings || [],
      suggestions,
    };
  }

  /**
   * 获取词语建议
   */
  static getWordSuggestions(
    partialText: string,
    libraryKey: WordLibraryKey,
    libraries: Map<WordLibraryKey, WordLibrary>,
    maxSuggestions: number = 5
  ): string[] {
    const suggestions: string[] = [];
    const trimmed = partialText.trim().toLowerCase();

    if (!trimmed) return suggestions;

    // 从所有词库中查找相似词语
    for (const library of libraries.values()) {
      for (const word of library.words) {
        const wordText = word.text.toLowerCase();
        
        // 简单的相似度匹配
        if (wordText.includes(trimmed) || trimmed.includes(wordText)) {
          if (!suggestions.includes(word.text)) {
            suggestions.push(word.text);
          }
        }
      }
    }

    return suggestions.slice(0, maxSuggestions);
  }
}

// ===== 导出便捷函数 =====

/**
 * 快速验证词语
 */
export const validateWord = (
  text: string,
  libraryKey: WordLibraryKey,
  libraries: Map<WordLibraryKey, WordLibrary>
): WordValidationResult => {
  return WordValidationService.validateWord(text, libraryKey, libraries);
};

/**
 * 快速检查重复
 */
export const checkDuplicate = (
  text: string,
  libraryKey: WordLibraryKey,
  libraries: Map<WordLibraryKey, WordLibrary>
): boolean => {
  const result = WordValidationService.checkWordDuplicate(text, libraryKey, libraries);
  return result.conflictType === 'same-library';
};

// ===== 高级验证功能 =====

/**
 * 词语相似度计算服务
 */
export class WordSimilarityService {
  /**
   * 计算两个词语的相似度 (0-1)
   */
  static calculateSimilarity(word1: string, word2: string): number {
    if (word1 === word2) return 1;

    // 简单的编辑距离算法
    const len1 = word1.length;
    const len2 = word2.length;
    const matrix: number[][] = [];

    for (let i = 0; i <= len1; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= len2; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        if (word1[i - 1] === word2[j - 1]) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j - 1] + 1
          );
        }
      }
    }

    const maxLen = Math.max(len1, len2);
    return maxLen === 0 ? 1 : (maxLen - matrix[len1][len2]) / maxLen;
  }

  /**
   * 查找相似词语
   */
  static findSimilarWords(
    targetWord: string,
    wordList: string[],
    threshold: number = 0.6
  ): Array<{ word: string; similarity: number }> {
    return wordList
      .map(word => ({
        word,
        similarity: this.calculateSimilarity(targetWord, word)
      }))
      .filter(item => item.similarity >= threshold)
      .sort((a, b) => b.similarity - a.similarity);
  }
}

/**
 * 智能词语建议服务
 */
export class IntelligentSuggestionService {
  /**
   * 基于上下文的智能建议
   */
  static getContextualSuggestions(
    input: string,
    context: {
      color?: BasicColorType;
      level?: DataLevel;
      nearbyWords?: string[];
      userHistory?: string[];
    }
  ): WordSuggestion[] {
    const suggestions: WordSuggestion[] = [];

    // 基于颜色的建议
    if (context.color) {
      const colorSuggestions = this.getColorBasedSuggestions(context.color);
      suggestions.push(...colorSuggestions);
    }

    // 基于级别的建议
    if (context.level) {
      const levelSuggestions = this.getLevelBasedSuggestions(context.level);
      suggestions.push(...levelSuggestions);
    }

    // 基于用户历史的建议
    if (context.userHistory && context.userHistory.length > 0) {
      const historySuggestions = this.getHistoryBasedSuggestions(input, context.userHistory);
      suggestions.push(...historySuggestions);
    }

    // 去重并排序
    const uniqueSuggestions = this.deduplicateAndRank(suggestions);

    return uniqueSuggestions.slice(0, 10); // 返回前10个建议
  }

  private static getColorBasedSuggestions(color: BasicColorType): WordSuggestion[] {
    const colorWords: Partial<Record<BasicColorType, string[]>> = {
      red: ['火', '血', '玫瑰', '苹果', '太阳'],
      blue: ['海', '天', '冰', '水', '蓝莓'],
      green: ['草', '树', '叶', '森林', '翡翠'],
      yellow: ['金', '阳', '香蕉', '柠檬', '沙'],
      purple: ['紫', '葡萄', '薰衣草', '茄子'],
      orange: ['橙', '橘', '胡萝卜', '南瓜'],
      pink: ['粉', '桃', '樱花', '玫瑰'],
      cyan: ['青', '碧', '翠', '湖'],
      black: ['夜', '墨', '煤', '乌鸦'],
      white: ['雪', '云', '纸', '牛奶']
    };

    return (colorWords[color] || []).map(word => ({
      text: word,
      score: 0.8,
      reason: `与${color}颜色相关`
    }));
  }

  private static getLevelBasedSuggestions(level: DataLevel): WordSuggestion[] {
    const levelWords: Record<DataLevel, string[]> = {
      1: ['一', '单', '独', '始', '初'],
      2: ['二', '双', '对', '次', '再'],
      3: ['三', '多', '众', '终', '末']
    };

    return (levelWords[level] || []).map(word => ({
      text: word,
      score: 0.7,
      reason: `适合级别${level}`
    }));
  }

  private static getHistoryBasedSuggestions(
    input: string,
    userHistory: string[]
  ): WordSuggestion[] {
    if (!input) return [];

    return userHistory
      .filter(word => word.startsWith(input))
      .map(word => ({
        text: word,
        score: 0.9,
        reason: '基于使用历史'
      }));
  }

  private static deduplicateAndRank(suggestions: WordSuggestion[]): WordSuggestion[] {
    const seen = new Set<string>();
    const unique: WordSuggestion[] = [];

    suggestions.forEach(suggestion => {
      if (!seen.has(suggestion.text)) {
        seen.add(suggestion.text);
        unique.push(suggestion);
      }
    });

    return unique.sort((a, b) => b.score - a.score);
  }
}
