/**
 * 词库管理状态存储 - 简化版本
 * 🎯 核心价值：专注核心词库管理功能，集成验证服务
 * 📦 功能范围：词库状态、词语CRUD、数据持久化
 * 🔄 架构设计：基于Zustand的简化状态管理，使用独立服务
 */

import { enableMapSet } from 'immer';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import type {
  BasicColorType,
  DataLevel,
  WordEntry,
  WordLibrary,
  WordLibraryKey,
  WordLibraryState,
  WordValidationResult
} from '../matrix/MatrixTypes';

import {
  createInitialWordLibraryState,
  createWordEntry,
  buildGlobalWordIndex
} from './WordLibraryCore';

import { WordValidationService } from './WordValidationService';
import { 
  createStateUpdater, 
  createPropertyUpdater 
} from '../utils/StateUpdateUtils';

// 启用 Immer 的 MapSet 插件
enableMapSet();

// ===== 简化的Store接口 =====

interface SimplifiedWordLibraryStore extends WordLibraryState {
  // ===== 基础词库管理 =====
  
  /** 添加词语到指定词库 */
  addWord: (libraryKey: WordLibraryKey, text: string) => WordValidationResult;
  
  /** 从词库中删除词语 */
  removeWord: (libraryKey: WordLibraryKey, wordId: string) => boolean;
  
  /** 更新词语文本 */
  updateWord: (libraryKey: WordLibraryKey, wordId: string, newText: string) => WordValidationResult;
  
  /** 批量添加词语 */
  addWords: (libraryKey: WordLibraryKey, texts: string[]) => WordValidationResult[];
  
  /** 清空指定词库 */
  clearLibrary: (libraryKey: WordLibraryKey) => boolean;
  
  /** 获取词库 */
  getLibrary: (libraryKey: WordLibraryKey) => WordLibrary | undefined;
  
  /** 获取词语 */
  getWord: (libraryKey: WordLibraryKey, wordId: string) => WordEntry | undefined;
  
  /** 设置激活词库 */
  setActiveLibrary: (libraryKey: WordLibraryKey | null) => void;
  
  /** 切换词库折叠状态 */
  toggleLibraryCollapse: (libraryKey: WordLibraryKey) => void;
  
  // ===== 搜索功能 =====
  
  /** 搜索词语 */
  searchWords: (query: string) => Array<{ libraryKey: WordLibraryKey; word: WordEntry }>;
  
  /** 获取匹配的词库（根据颜色和级别） */
  getMatchingLibrary: (color: BasicColorType, level: DataLevel) => WordLibrary | undefined;
  
  // ===== 数据管理 =====
  
  /** 重置所有词库 */
  resetAllLibraries: () => void;
  
  /** 获取统计信息 */
  getStatistics: () => {
    totalLibraries: number;
    totalWords: number;
    libraryStats: Array<{ libraryKey: WordLibraryKey; wordCount: number }>;
  };
}

// ===== Store实现 =====

export const useWordLibraryStoreSimplified = create<SimplifiedWordLibraryStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      ...createInitialWordLibraryState(),

      // ===== 基础词库管理 =====

      addWord: (libraryKey: WordLibraryKey, text: string) => {
        const libraries = get().libraries;
        
        // 使用验证服务进行验证
        const validation = WordValidationService.validateWord(text, libraryKey, libraries);
        
        if (!validation.isValid) {
          return validation;
        }

        // 验证通过，添加词语
        const trimmedText = text.trim();
        let addedSuccessfully = false;

        set(createStateUpdater((state) => {
          const library = state.libraries.get(libraryKey);
          if (library) {
            const wordEntry = createWordEntry(trimmedText, library.color, library.level);
            library.words.push(wordEntry);
            library.lastUpdated = new Date();

            // 更新全局索引
            if (!state.globalWordIndex.has(trimmedText)) {
              state.globalWordIndex.set(trimmedText, new Set());
            }
            state.globalWordIndex.get(trimmedText)!.add(libraryKey);

            addedSuccessfully = true;
          }
        }));

        return addedSuccessfully ? validation : {
          isValid: false,
          errors: ['添加词语失败'],
          isDuplicate: false,
          duplicateLibraries: []
        };
      },

      removeWord: (libraryKey: WordLibraryKey, wordId: string) => {
        let removed = false;

        set(createStateUpdater((state) => {
          const library = state.libraries.get(libraryKey);
          if (library) {
            const wordIndex = library.words.findIndex(word => word.id === wordId);
            if (wordIndex !== -1) {
              const removedWord = library.words[wordIndex];
              library.words.splice(wordIndex, 1);
              library.lastUpdated = new Date();

              // 更新全局索引
              const wordLibraries = state.globalWordIndex.get(removedWord.text);
              if (wordLibraries) {
                wordLibraries.delete(libraryKey);
                if (wordLibraries.size === 0) {
                  state.globalWordIndex.delete(removedWord.text);
                }
              }

              removed = true;
            }
          }
        }));

        return removed;
      },

      updateWord: (libraryKey: WordLibraryKey, wordId: string, newText: string) => {
        const libraries = get().libraries;
        
        // 验证新文本
        const validation = WordValidationService.validateWord(newText, libraryKey, libraries);
        
        if (!validation.isValid) {
          return validation;
        }

        const trimmedText = newText.trim();
        let updated = false;

        set(createStateUpdater((state) => {
          const library = state.libraries.get(libraryKey);
          if (library) {
            const word = library.words.find(w => w.id === wordId);
            if (word) {
              const oldText = word.text;
              word.text = trimmedText;
              word.updatedAt = new Date();
              library.lastUpdated = new Date();

              // 更新全局索引
              const oldWordLibraries = state.globalWordIndex.get(oldText);
              if (oldWordLibraries) {
                oldWordLibraries.delete(libraryKey);
                if (oldWordLibraries.size === 0) {
                  state.globalWordIndex.delete(oldText);
                }
              }

              if (!state.globalWordIndex.has(trimmedText)) {
                state.globalWordIndex.set(trimmedText, new Set());
              }
              state.globalWordIndex.get(trimmedText)!.add(libraryKey);

              updated = true;
            }
          }
        }));

        return updated ? validation : {
          isValid: false,
          errors: ['更新词语失败'],
          isDuplicate: false,
          duplicateLibraries: []
        };
      },

      addWords: (libraryKey: WordLibraryKey, texts: string[]) => {
        const libraries = get().libraries;
        const results: WordValidationResult[] = [];

        // 批量验证
        const validationResults = WordValidationService.validateWordsBatch(
          texts.map(text => ({ text, libraryKey })),
          libraries
        );

        // 添加有效的词语
        const validTexts = texts.filter(text => {
          const result = validationResults.results.get(text);
          results.push(result!);
          return result?.isValid;
        });

        if (validTexts.length > 0) {
          set(createStateUpdater((state) => {
            const library = state.libraries.get(libraryKey);
            if (library) {
              validTexts.forEach(text => {
                const trimmedText = text.trim();
                const wordEntry = createWordEntry(trimmedText, library.color, library.level);
                library.words.push(wordEntry);

                // 更新全局索引
                if (!state.globalWordIndex.has(trimmedText)) {
                  state.globalWordIndex.set(trimmedText, new Set());
                }
                state.globalWordIndex.get(trimmedText)!.add(libraryKey);
              });
              library.lastUpdated = new Date();
            }
          }));
        }

        return results;
      },

      clearLibrary: (libraryKey: WordLibraryKey) => {
        let cleared = false;

        set(createStateUpdater((state) => {
          const library = state.libraries.get(libraryKey);
          if (library) {
            // 从全局索引中移除所有词语
            library.words.forEach(word => {
              const wordLibraries = state.globalWordIndex.get(word.text);
              if (wordLibraries) {
                wordLibraries.delete(libraryKey);
                if (wordLibraries.size === 0) {
                  state.globalWordIndex.delete(word.text);
                }
              }
            });

            library.words = [];
            library.lastUpdated = new Date();
            cleared = true;
          }
        }));

        return cleared;
      },

      getLibrary: (libraryKey: WordLibraryKey) => {
        return get().libraries.get(libraryKey);
      },

      getWord: (libraryKey: WordLibraryKey, wordId: string) => {
        const library = get().libraries.get(libraryKey);
        return library?.words.find(word => word.id === wordId);
      },

      setActiveLibrary: (libraryKey: WordLibraryKey | null) => {
        set(createPropertyUpdater('activeLibrary', libraryKey));
      },

      toggleLibraryCollapse: (libraryKey: WordLibraryKey) => {
        set(createStateUpdater((state) => {
          const library = state.libraries.get(libraryKey);
          if (library) {
            library.collapsed = !library.collapsed;
          }
        }));
      },

      // ===== 搜索功能 =====

      searchWords: (query: string) => {
        const results: Array<{ libraryKey: WordLibraryKey; word: WordEntry }> = [];
        const libraries = get().libraries;
        const lowerQuery = query.toLowerCase();

        for (const [libraryKey, library] of libraries.entries()) {
          for (const word of library.words) {
            if (word.text.toLowerCase().includes(lowerQuery)) {
              results.push({ libraryKey, word });
            }
          }
        }

        return results;
      },

      getMatchingLibrary: (color: BasicColorType, level: DataLevel) => {
        const libraries = get().libraries;
        for (const library of libraries.values()) {
          if (library.color === color && library.level === level) {
            return library;
          }
        }
        return undefined;
      },

      // ===== 数据管理 =====

      resetAllLibraries: () => {
        set(createStateUpdater((state) => {
          // 重新初始化状态
          const newState = createInitialWordLibraryState();
          Object.assign(state, newState);
        }));
      },

      getStatistics: () => {
        const libraries = get().libraries;
        const libraryStats: Array<{ libraryKey: WordLibraryKey; wordCount: number }> = [];
        let totalWords = 0;

        for (const [libraryKey, library] of libraries.entries()) {
          const wordCount = library.words.length;
          libraryStats.push({ libraryKey, wordCount });
          totalWords += wordCount;
        }

        return {
          totalLibraries: libraries.size,
          totalWords,
          libraryStats,
        };
      },
    }),
    {
      name: 'word-library-store-simplified',
      version: 1,
      partialize: (state) => ({
        libraries: Array.from(state.libraries.entries()),
        activeLibrary: state.activeLibrary,
        globalWordIndex: Array.from(state.globalWordIndex.entries()).map(([word, libraries]) => [
          word,
          Array.from(libraries)
        ]),
        lastSyncTime: state.lastSyncTime,
      }),
      onRehydrateStorage: () => (state) => {
        if (state?.libraries) {
          state.libraries = new Map(state.libraries as any);
        }
        if (state?.globalWordIndex) {
          state.globalWordIndex = new Map(
            (state.globalWordIndex as any).map(([word, libraries]: [string, string[]]) => [
              word,
              new Set(libraries)
            ])
          );
        }
        // 重建其他Map和Set
        if (!state?.duplicateWords) {
          state!.duplicateWords = new Map();
        }
        if (!state?.wordHighlightColors) {
          state!.wordHighlightColors = new Map();
        }
        if (!state?.usedHighlightColors) {
          state!.usedHighlightColors = new Set();
        }
      },
    }
  )
);
