/**
 * 矩阵模式处理服务
 * 🎯 核心价值：统一的模式处理逻辑，简化复杂的模式切换和内容生成
 * 📦 功能范围：模式管理、内容生成、样式计算、模式验证
 * 🔄 架构设计：策略模式 + 工厂模式，支持动态模式切换
 */

import type {
  BusinessMode,
  MainMode,
  ContentMode,
  MatrixConfig,
  CellData,
  CellRenderData
} from '../matrix/MatrixTypes';
import { createConditionalChain, createStrategySelector } from '../utils/ConditionalLogicUtils';

// ===== 类型定义 =====

interface ModeStrategy {
  /** 策略名称 */
  name: string;
  /** 生成内容 */
  generateContent: (cell: CellData, config: MatrixConfig) => string;
  /** 是否使用矩阵颜色 */
  useMatrixColor: boolean;
  /** 自定义样式 */
  customStyle?: React.CSSProperties;
  /** 验证函数 */
  validate?: (cell: CellData, config: MatrixConfig) => boolean;
}

interface ModeTransition {
  /** 源模式 */
  from: BusinessMode;
  /** 目标模式 */
  to: BusinessMode;
  /** 转换函数 */
  transform: (data: any) => any;
  /** 是否需要重新渲染 */
  requiresRerender: boolean;
}

interface ModeValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

// ===== 内容生成策略 =====

const contentGenerators = {
  coordinate: (cell: CellData): string => {
    return cell.displayCoordinate || `${String.fromCharCode(65 + cell.y)}${cell.x + 1}`;
  },

  color: (cell: CellData): string => {
    return ''; // 颜色模式不显示文本内容
  },

  level: (cell: CellData): string => {
    return cell.level?.toString() || '';
  },

  word: (cell: CellData): string => {
    return cell.word || '';
  },

  number: (cell: CellData): string => {
    return cell.value?.toString() || '';
  },

  'color-word': (cell: CellData): string => {
    return cell.word || '';
  },

  'number-word': (cell: CellData): string => {
    return cell.word || cell.value?.toString() || '';
  }
};

// ===== 模式策略定义 =====

const modeStrategies: Record<BusinessMode, ModeStrategy> = {
  coordinate: {
    name: '坐标模式',
    generateContent: contentGenerators.coordinate,
    useMatrixColor: false,
    customStyle: { fontSize: '10px' }
  },

  color: {
    name: '颜色模式',
    generateContent: contentGenerators.color,
    useMatrixColor: true
  },

  level: {
    name: '级别模式',
    generateContent: contentGenerators.level,
    useMatrixColor: false,
    customStyle: { fontWeight: 'bold' }
  },

  word: {
    name: '词语模式',
    generateContent: contentGenerators.word,
    useMatrixColor: false,
    validate: (cell) => Boolean(cell.word)
  },

  'color-word': {
    name: '颜色+词语模式',
    generateContent: contentGenerators['color-word'],
    useMatrixColor: true,
    validate: (cell) => Boolean(cell.word)
  },

  'number-word': {
    name: '数字+词语模式',
    generateContent: contentGenerators['number-word'],
    useMatrixColor: false,
    validate: (cell) => Boolean(cell.word || cell.value)
  }
};

// ===== 模式转换定义 =====

const modeTransitions: ModeTransition[] = [
  {
    from: 'coordinate',
    to: 'color',
    transform: (data) => ({ ...data, useMatrixColor: true }),
    requiresRerender: true
  },
  {
    from: 'color',
    to: 'word',
    transform: (data) => ({ ...data, useMatrixColor: false }),
    requiresRerender: true
  },
  {
    from: 'word',
    to: 'color-word',
    transform: (data) => ({ ...data, useMatrixColor: true }),
    requiresRerender: false
  }
];

// ===== 主服务类 =====

export class MatrixModeService {
  private static currentMode: BusinessMode = 'coordinate';
  private static modeHistory: BusinessMode[] = [];

  /**
   * 设置当前模式
   */
  static setMode(mode: BusinessMode): void {
    if (this.currentMode !== mode) {
      this.modeHistory.push(this.currentMode);
      this.currentMode = mode;
    }
  }

  /**
   * 获取当前模式
   */
  static getCurrentMode(): BusinessMode {
    return this.currentMode;
  }

  /**
   * 获取模式策略
   */
  static getModeStrategy(mode: BusinessMode): ModeStrategy {
    return modeStrategies[mode];
  }

  /**
   * 生成单元格渲染数据
   */
  static generateCellRenderData(
    cell: CellData,
    config: MatrixConfig
  ): CellRenderData {
    const mode = config.mode || this.currentMode;
    const strategy = this.getModeStrategy(mode);

    if (!strategy) {
      throw new Error(`未知的模式: ${mode}`);
    }

    const content = strategy.generateContent(cell, config);
    const useMatrixColor = strategy.useMatrixColor;

    return {
      content,
      className: `matrix-cell matrix-cell--${mode}`,
      isInteractive: this.isModeInteractive(mode),
      style: {
        ...strategy.customStyle,
        ...(useMatrixColor && cell.color ? this.getColorStyle(cell.color) : {})
      }
    };
  }

  /**
   * 批量生成渲染数据
   */
  static generateBatchRenderData(
    cells: CellData[],
    config: MatrixConfig
  ): Map<string, CellRenderData> {
    const renderDataMap = new Map<string, CellRenderData>();

    cells.forEach(cell => {
      const key = `${cell.x},${cell.y}`;
      const renderData = this.generateCellRenderData(cell, config);
      renderDataMap.set(key, renderData);
    });

    return renderDataMap;
  }

  /**
   * 验证模式配置
   */
  static validateModeConfig(config: MatrixConfig): ModeValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // 验证模式存在性
    if (!config.mode) {
      errors.push('缺少模式配置');
    } else if (!modeStrategies[config.mode]) {
      errors.push(`不支持的模式: ${config.mode}`);
    }

    // 验证模式组合
    if (config.mainMode && config.contentMode) {
      const combinedMode = `${config.mainMode}-${config.contentMode}` as BusinessMode;
      if (!modeStrategies[combinedMode] && combinedMode !== config.mode) {
        warnings.push(`模式组合可能不兼容: ${combinedMode}`);
        suggestions.push(`考虑使用预定义的组合模式`);
      }
    }

    // 验证布尔标志一致性
    if (config.isColorMode !== undefined && config.mainMode) {
      const expectedColorMode = config.mainMode === 'color';
      if (config.isColorMode !== expectedColorMode) {
        warnings.push('isColorMode标志与mainMode不一致');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * 执行模式转换
   */
  static transitionMode(
    from: BusinessMode,
    to: BusinessMode,
    data?: any
  ): { success: boolean; result?: any; requiresRerender: boolean } {
    const transition = modeTransitions.find(t => t.from === from && t.to === to);

    if (!transition) {
      return {
        success: false,
        requiresRerender: false
      };
    }

    try {
      const result = transition.transform(data);
      this.setMode(to);

      return {
        success: true,
        result,
        requiresRerender: transition.requiresRerender
      };
    } catch (error) {
      return {
        success: false,
        requiresRerender: false
      };
    }
  }

  /**
   * 获取模式兼容性
   */
  static getModeCompatibility(mode: BusinessMode): {
    compatibleModes: BusinessMode[];
    conflictingModes: BusinessMode[];
  } {
    const allModes = Object.keys(modeStrategies) as BusinessMode[];
    const compatibleModes: BusinessMode[] = [];
    const conflictingModes: BusinessMode[] = [];

    allModes.forEach(otherMode => {
      if (otherMode === mode) return;

      const hasTransition = modeTransitions.some(
        t => (t.from === mode && t.to === otherMode) || 
             (t.from === otherMode && t.to === mode)
      );

      if (hasTransition) {
        compatibleModes.push(otherMode);
      } else {
        conflictingModes.push(otherMode);
      }
    });

    return { compatibleModes, conflictingModes };
  }

  /**
   * 获取推荐模式
   */
  static getRecommendedModes(
    cellData: CellData[],
    currentMode: BusinessMode
  ): BusinessMode[] {
    const recommendations: BusinessMode[] = [];

    // 基于数据特征推荐
    const hasWords = cellData.some(cell => cell.word);
    const hasNumbers = cellData.some(cell => cell.value);
    const hasColors = cellData.some(cell => cell.color && cell.color !== 'white');

    if (hasWords && hasColors) {
      recommendations.push('color-word');
    }

    if (hasWords && hasNumbers) {
      recommendations.push('number-word');
    }

    if (hasColors && !hasWords) {
      recommendations.push('color');
    }

    if (hasWords && !hasColors) {
      recommendations.push('word');
    }

    // 移除当前模式
    return recommendations.filter(mode => mode !== currentMode);
  }

  // ===== 私有辅助方法 =====

  private static isModeInteractive(mode: BusinessMode): boolean {
    return ['word', 'color-word', 'number-word'].includes(mode);
  }

  private static getColorStyle(color: string): React.CSSProperties {
    // 简化的颜色映射
    const colorMap: Record<string, string> = {
      red: '#ff4444',
      blue: '#4444ff',
      green: '#44ff44',
      yellow: '#ffff44',
      purple: '#ff44ff',
      orange: '#ff8844',
      pink: '#ff88cc',
      cyan: '#44ffff',
      black: '#444444',
      white: '#ffffff',
      gray: '#888888',
      brown: '#8b4513'
    };

    return {
      backgroundColor: colorMap[color] || '#ffffff',
      color: ['black', 'blue', 'purple'].includes(color) ? '#ffffff' : '#000000'
    };
  }

  /**
   * 重置服务状态
   */
  static reset(): void {
    this.currentMode = 'coordinate';
    this.modeHistory = [];
  }

  /**
   * 获取模式历史
   */
  static getModeHistory(): BusinessMode[] {
    return [...this.modeHistory];
  }

  /**
   * 回退到上一个模式
   */
  static revertToPreviousMode(): boolean {
    if (this.modeHistory.length > 0) {
      this.currentMode = this.modeHistory.pop()!;
      return true;
    }
    return false;
  }
}

// ===== 便捷函数 =====

/**
 * 创建标准矩阵配置
 */
export const createMatrixConfig = (
  mainMode: MainMode,
  contentMode: ContentMode
): MatrixConfig => {
  const mode = `${mainMode}-${contentMode}` as BusinessMode;
  
  return {
    mode,
    mainMode,
    contentMode,
    businessMode: mode,
    isColorMode: mainMode === 'color',
    isWordMode: contentMode === 'word',
    isNumberMode: contentMode === 'number',
    isCoordinateMode: contentMode === 'coordinate'
  };
};

/**
 * 快速模式切换
 */
export const switchMode = (
  currentConfig: MatrixConfig,
  newMode: BusinessMode
): MatrixConfig => {
  return {
    ...currentConfig,
    mode: newMode,
    businessMode: newMode
  };
};

// ===== 导出类型 =====

export type {
  ModeStrategy,
  ModeTransition,
  ModeValidationResult,
};
