/**
 * 矩阵数据服务
 * 🎯 核心价值：统一的矩阵数据处理服务，封装复杂的业务逻辑
 * 📦 功能范围：数据查询、数据转换、数据验证、数据统计
 * 🔄 架构设计：服务层模式，提供纯函数式的数据处理方法
 */

import type {
  CellData,
  CellRenderData,
  MatrixConfig,
  MatrixData,
  MatrixDataSet,
  MatrixDataPoint,
  Coordinate,
  BasicColorType,
  DataLevel
} from '../matrix/MatrixTypes';
import { MATRIX_SIZE, coordinateKey } from '../matrix/MatrixTypes';
import { matrixCache } from '../matrix/MatrixCacheService';

// ===== 类型定义 =====

interface DataQueryOptions {
  /** 是否包含空单元格 */
  includeEmpty?: boolean;
  /** 过滤条件 */
  filter?: {
    colors?: BasicColorType[];
    levels?: DataLevel[];
    groups?: string[];
    isActive?: boolean;
  };
  /** 排序选项 */
  sort?: {
    by: 'x' | 'y' | 'level' | 'color' | 'group';
    order: 'asc' | 'desc';
  };
  /** 分页选项 */
  pagination?: {
    page: number;
    size: number;
  };
}

interface DataStatistics {
  /** 总单元格数 */
  totalCells: number;
  /** 活跃单元格数 */
  activeCells: number;
  /** 按颜色分组的统计 */
  byColor: Record<BasicColorType, number>;
  /** 按级别分组的统计 */
  byLevel: Record<DataLevel, number>;
  /** 按组分组的统计 */
  byGroup: Record<string, number>;
  /** 数据覆盖率 */
  coverageRate: number;
}

interface DataValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 错误信息 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
  /** 修复建议 */
  suggestions: string[];
}

interface DataTransformOptions {
  /** 目标格式 */
  format: 'matrix' | 'list' | 'tree' | 'graph';
  /** 是否包含元数据 */
  includeMetadata?: boolean;
  /** 自定义转换函数 */
  customTransform?: (data: any) => any;
}

// ===== 矩阵数据服务类 =====

export class MatrixDataService {
  private static cache = new Map<string, any>();

  // ===== 数据查询方法 =====

  /**
   * 根据坐标获取单元格数据
   */
  static getCellData(
    matrixData: MatrixData,
    x: number,
    y: number
  ): CellData | undefined {
    const key = coordinateKey(x, y);
    return matrixData.cells.get(key);
  }

  /**
   * 批量获取单元格数据
   */
  static getCellsData(
    matrixData: MatrixData,
    coordinates: Coordinate[]
  ): Map<string, CellData> {
    const result = new Map<string, CellData>();
    
    coordinates.forEach(({ x, y }) => {
      const key = coordinateKey(x, y);
      const cellData = matrixData.cells.get(key);
      if (cellData) {
        result.set(key, cellData);
      }
    });
    
    return result;
  }

  /**
   * 查询满足条件的单元格
   */
  static queryCells(
    matrixData: MatrixData,
    options: DataQueryOptions = {}
  ): CellData[] {
    const {
      includeEmpty = false,
      filter,
      sort,
      pagination
    } = options;

    let cells: CellData[] = [];

    // 收集所有单元格
    if (includeEmpty) {
      // 包含空单元格，生成完整网格
      for (let y = 0; y < MATRIX_SIZE; y++) {
        for (let x = 0; x < MATRIX_SIZE; x++) {
          const key = coordinateKey(x, y);
          const cellData = matrixData.cells.get(key);
          if (cellData) {
            cells.push(cellData);
          } else {
            // 创建空单元格数据
            cells.push({
              x,
              y,
              color: 'white',
              level: 1,
              group: '',
              displayCoordinate: `${String.fromCharCode(65 + y)}${x + 1}`,
              isActive: false,
              isSelected: false,
              isHovered: false,
              isFocused: false,
            });
          }
        }
      }
    } else {
      // 只包含有数据的单元格
      cells = Array.from(matrixData.cells.values());
    }

    // 应用过滤条件
    if (filter) {
      cells = cells.filter(cell => {
        if (filter.colors && !filter.colors.includes(cell.color)) return false;
        if (filter.levels && !filter.levels.includes(cell.level)) return false;
        if (filter.groups && !filter.groups.includes(cell.group)) return false;
        if (filter.isActive !== undefined && cell.isActive !== filter.isActive) return false;
        return true;
      });
    }

    // 应用排序
    if (sort) {
      cells.sort((a, b) => {
        let aValue: any = a[sort.by];
        let bValue: any = b[sort.by];
        
        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }
        
        const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        return sort.order === 'desc' ? -comparison : comparison;
      });
    }

    // 应用分页
    if (pagination) {
      const start = (pagination.page - 1) * pagination.size;
      const end = start + pagination.size;
      cells = cells.slice(start, end);
    }

    return cells;
  }

  /**
   * 获取相邻单元格
   */
  static getAdjacentCells(
    matrixData: MatrixData,
    x: number,
    y: number,
    includeDiagonal: boolean = false
  ): CellData[] {
    const adjacent: CellData[] = [];
    const directions = includeDiagonal 
      ? [[-1,-1], [-1,0], [-1,1], [0,-1], [0,1], [1,-1], [1,0], [1,1]]
      : [[-1,0], [0,-1], [0,1], [1,0]];

    directions.forEach(([dx, dy]) => {
      const newX = x + dx;
      const newY = y + dy;
      
      if (newX >= 0 && newX < MATRIX_SIZE && newY >= 0 && newY < MATRIX_SIZE) {
        const cellData = this.getCellData(matrixData, newX, newY);
        if (cellData) {
          adjacent.push(cellData);
        }
      }
    });

    return adjacent;
  }

  // ===== 数据统计方法 =====

  /**
   * 计算数据统计信息
   */
  static calculateStatistics(matrixData: MatrixData): DataStatistics {
    const cacheKey = `stats-${matrixData.lastUpdate}`;
    const cached = this.cache.get(cacheKey);
    if (cached) return cached;

    const stats: DataStatistics = {
      totalCells: MATRIX_SIZE * MATRIX_SIZE,
      activeCells: 0,
      byColor: {} as Record<BasicColorType, number>,
      byLevel: {} as Record<DataLevel, number>,
      byGroup: {},
      coverageRate: 0,
    };

    // 初始化计数器
    const colors: BasicColorType[] = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'cyan', 'black', 'white', 'gray', 'brown'];
    colors.forEach(color => stats.byColor[color] = 0);
    
    const levels: DataLevel[] = [1, 2, 3];
    levels.forEach(level => stats.byLevel[level] = 0);

    // 统计数据
    matrixData.cells.forEach(cell => {
      if (cell.isActive) {
        stats.activeCells++;
      }
      
      stats.byColor[cell.color]++;
      stats.byLevel[cell.level]++;
      
      if (cell.group) {
        stats.byGroup[cell.group] = (stats.byGroup[cell.group] || 0) + 1;
      }
    });

    // 计算覆盖率
    stats.coverageRate = stats.activeCells / stats.totalCells;

    // 缓存结果
    this.cache.set(cacheKey, stats);
    
    return stats;
  }

  /**
   * 获取数据分布信息
   */
  static getDataDistribution(matrixData: MatrixData): {
    density: number[][];
    hotspots: Coordinate[];
    emptyRegions: Coordinate[];
  } {
    const density: number[][] = Array(MATRIX_SIZE).fill(null).map(() => Array(MATRIX_SIZE).fill(0));
    const hotspots: Coordinate[] = [];
    const emptyRegions: Coordinate[] = [];

    // 计算密度
    for (let y = 0; y < MATRIX_SIZE; y++) {
      for (let x = 0; x < MATRIX_SIZE; x++) {
        const cellData = this.getCellData(matrixData, x, y);
        if (cellData?.isActive) {
          density[y][x] = 1;
          
          // 检查是否为热点（周围有多个活跃单元格）
          const adjacent = this.getAdjacentCells(matrixData, x, y, true);
          const activeAdjacent = adjacent.filter(cell => cell.isActive).length;
          
          if (activeAdjacent >= 5) {
            hotspots.push({ x, y });
          }
        } else {
          emptyRegions.push({ x, y });
        }
      }
    }

    return { density, hotspots, emptyRegions };
  }

  // ===== 数据验证方法 =====

  /**
   * 验证矩阵数据完整性
   */
  static validateData(matrixData: MatrixData): DataValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // 检查数据结构
    if (!matrixData.cells) {
      errors.push('缺少单元格数据');
    }

    if (!matrixData.lastUpdate) {
      warnings.push('缺少最后更新时间');
    }

    // 检查单元格数据
    matrixData.cells.forEach((cell, key) => {
      // 检查坐标一致性
      const expectedKey = coordinateKey(cell.x, cell.y);
      if (key !== expectedKey) {
        errors.push(`单元格坐标不一致: ${key} vs ${expectedKey}`);
      }

      // 检查坐标范围
      if (cell.x < 0 || cell.x >= MATRIX_SIZE || cell.y < 0 || cell.y >= MATRIX_SIZE) {
        errors.push(`单元格坐标超出范围: (${cell.x}, ${cell.y})`);
      }

      // 检查必需属性
      if (!cell.color) {
        errors.push(`单元格 (${cell.x}, ${cell.y}) 缺少颜色属性`);
      }

      if (!cell.level || cell.level < 1 || cell.level > 3) {
        errors.push(`单元格 (${cell.x}, ${cell.y}) 级别无效: ${cell.level}`);
      }
    });

    // 检查数据覆盖率
    const stats = this.calculateStatistics(matrixData);
    if (stats.coverageRate < 0.1) {
      warnings.push('数据覆盖率过低，可能影响用户体验');
      suggestions.push('考虑增加更多数据点');
    }

    // 检查数据分布
    const distribution = this.getDataDistribution(matrixData);
    if (distribution.hotspots.length === 0) {
      suggestions.push('考虑创建一些数据热点区域');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
    };
  }

  // ===== 数据转换方法 =====

  /**
   * 转换数据格式
   */
  static transformData(
    matrixData: MatrixData,
    options: DataTransformOptions
  ): any {
    const { format, includeMetadata = false, customTransform } = options;

    let result: any;

    switch (format) {
      case 'list':
        result = Array.from(matrixData.cells.values());
        break;

      case 'tree':
        result = this.convertToTree(matrixData);
        break;

      case 'graph':
        result = this.convertToGraph(matrixData);
        break;

      case 'matrix':
      default:
        result = this.convertToMatrix(matrixData);
        break;
    }

    // 添加元数据
    if (includeMetadata) {
      const stats = this.calculateStatistics(matrixData);
      result = {
        data: result,
        metadata: {
          statistics: stats,
          lastUpdate: matrixData.lastUpdate,
          version: '1.0',
        },
      };
    }

    // 应用自定义转换
    if (customTransform) {
      result = customTransform(result);
    }

    return result;
  }

  // ===== 私有辅助方法 =====

  private static convertToMatrix(matrixData: MatrixData): CellData[][] {
    const matrix: CellData[][] = Array(MATRIX_SIZE).fill(null).map(() => Array(MATRIX_SIZE).fill(null));
    
    matrixData.cells.forEach(cell => {
      matrix[cell.y][cell.x] = cell;
    });
    
    return matrix;
  }

  private static convertToTree(matrixData: MatrixData): any {
    const tree: any = {};
    
    matrixData.cells.forEach(cell => {
      if (!tree[cell.group]) {
        tree[cell.group] = {};
      }
      if (!tree[cell.group][cell.color]) {
        tree[cell.group][cell.color] = {};
      }
      if (!tree[cell.group][cell.color][cell.level]) {
        tree[cell.group][cell.color][cell.level] = [];
      }
      
      tree[cell.group][cell.color][cell.level].push(cell);
    });
    
    return tree;
  }

  private static convertToGraph(matrixData: MatrixData): any {
    const nodes: any[] = [];
    const edges: any[] = [];
    
    matrixData.cells.forEach(cell => {
      nodes.push({
        id: coordinateKey(cell.x, cell.y),
        data: cell,
      });
      
      // 添加与相邻单元格的边
      const adjacent = this.getAdjacentCells(matrixData, cell.x, cell.y);
      adjacent.forEach(adjCell => {
        edges.push({
          source: coordinateKey(cell.x, cell.y),
          target: coordinateKey(adjCell.x, adjCell.y),
          weight: cell.level + adjCell.level,
        });
      });
    });
    
    return { nodes, edges };
  }

  /**
   * 清理缓存
   */
  static clearCache(): void {
    this.cache.clear();
  }
}

// ===== 导出类型 =====

export type {
  DataQueryOptions,
  DataStatistics,
  DataValidationResult,
  DataTransformOptions,
};
