/**
 * 矩阵数据处理服务
 * 🎯 核心价值：统一的矩阵数据处理逻辑，封装复杂的数据转换和计算
 * 📦 功能范围：数据生成、坐标转换、偏移计算、数据验证
 * 🔄 架构设计：服务层模式，提供纯函数式的数据处理方法
 */

import type {
  BasicColorType,
  DataLevel,
  MatrixDataPoint,
  MatrixDataSet,
  Coordinate
} from '../matrix/MatrixTypes';
import { AVAILABLE_LEVELS, GROUP_OFFSETS } from '../data/GroupAData';

// ===== 类型定义 =====

interface DataGenerationOptions {
  /** 目标组 */
  group: string;
  /** 包含的颜色 */
  colors?: BasicColorType[];
  /** 包含的级别 */
  levels?: DataLevel[];
  /** 是否应用偏移 */
  applyOffset?: boolean;
  /** 自定义偏移 */
  customOffset?: [number, number];
}

interface CoordinateTransformOptions {
  /** 源坐标系 */
  sourceSystem: 'matrix' | 'display' | 'offset';
  /** 目标坐标系 */
  targetSystem: 'matrix' | 'display' | 'offset';
  /** 组偏移 */
  groupOffset?: [number, number];
}

interface DataValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  statistics: {
    totalPoints: number;
    pointsByColor: Record<BasicColorType, number>;
    pointsByLevel: Record<DataLevel, number>;
    coverage: number;
  };
}

// ===== 坐标转换工具 =====

export class CoordinateTransformService {
  /**
   * 矩阵坐标转显示坐标
   */
  static matrixToDisplay(x: number, y: number): [string, number] {
    const displayX = String.fromCharCode(65 + y); // A, B, C...
    const displayY = x + 1; // 1, 2, 3...
    return [displayX, displayY];
  }

  /**
   * 显示坐标转矩阵坐标
   */
  static displayToMatrix(displayX: string, displayY: number): [number, number] {
    const x = displayY - 1;
    const y = displayX.charCodeAt(0) - 65;
    return [x, y];
  }

  /**
   * 应用组偏移
   */
  static applyGroupOffset(
    x: number,
    y: number,
    offset: [number, number]
  ): [number, number] {
    return [x + offset[0], y + offset[1]];
  }

  /**
   * 移除组偏移
   */
  static removeGroupOffset(
    x: number,
    y: number,
    offset: [number, number]
  ): [number, number] {
    return [x - offset[0], y - offset[1]];
  }

  /**
   * 通用坐标转换
   */
  static transform(
    x: number,
    y: number,
    options: CoordinateTransformOptions
  ): [number, number] {
    let resultX = x;
    let resultY = y;

    // 处理源坐标系
    if (options.sourceSystem === 'display') {
      // 假设输入是显示坐标的数值形式
      [resultX, resultY] = this.displayToMatrix(resultX, resultY);
    } else if (options.sourceSystem === 'offset' && options.groupOffset) {
      [resultX, resultY] = this.removeGroupOffset(resultX, resultY, options.groupOffset);
    }

    // 处理目标坐标系
    if (options.targetSystem === 'offset' && options.groupOffset) {
      [resultX, resultY] = this.applyGroupOffset(resultX, resultY, options.groupOffset);
    }

    return [resultX, resultY];
  }
}

// ===== 数据生成服务 =====

export class MatrixDataGenerationService {
  /**
   * 生成指定组的数据点
   */
  static generateGroupData(
    group: string,
    options: DataGenerationOptions = {}
  ): MatrixDataPoint[] {
    const {
      colors = Object.keys(AVAILABLE_LEVELS) as BasicColorType[],
      levels = [1, 2, 3] as DataLevel[],
      applyOffset = true,
      customOffset
    } = options;

    const dataPoints: MatrixDataPoint[] = [];
    const groupOffset = customOffset || GROUP_OFFSETS[group]?.defaultOffset || [0, 0];

    colors.forEach(color => {
      const availableLevels = AVAILABLE_LEVELS[color] || [];
      const validLevels = levels.filter(level => availableLevels.includes(level));

      validLevels.forEach(level => {
        // 获取该颜色在该级别的特定偏移
        const specificOffset = GROUP_OFFSETS[group]?.level1Offsets?.[color] || groupOffset;
        
        // 基础坐标（可以根据颜色和级别计算）
        const baseX = this.calculateBaseX(color, level);
        const baseY = this.calculateBaseY(color, level);

        // 应用偏移
        const [finalX, finalY] = applyOffset 
          ? CoordinateTransformService.applyGroupOffset(baseX, baseY, specificOffset)
          : [baseX, baseY];

        // 生成显示坐标
        const [displayX, displayY] = CoordinateTransformService.matrixToDisplay(finalX, finalY);

        dataPoints.push({
          x: finalX,
          y: finalY,
          color,
          level,
          group,
          displayCoordinate: `${displayX}${displayY}`
        });
      });
    });

    return dataPoints;
  }

  /**
   * 批量生成多个组的数据
   */
  static generateMultiGroupData(
    groups: string[],
    options: DataGenerationOptions = {}
  ): MatrixDataSet {
    const allDataPoints: MatrixDataPoint[] = [];
    
    groups.forEach(group => {
      const groupData = this.generateGroupData(group, { ...options, group });
      allDataPoints.push(...groupData);
    });

    return {
      metadata: {
        totalPoints: allDataPoints.length,
        version: '1.0.0'
      },
      data: allDataPoints,
      byCoordinate: new Map(
        allDataPoints.map(point => [`${point.x},${point.y}`, point])
      )
    };
  }

  /**
   * 根据颜色计算基础X坐标
   */
  private static calculateBaseX(color: BasicColorType, level: DataLevel): number {
    const colorIndex = Object.keys(AVAILABLE_LEVELS).indexOf(color);
    return colorIndex * 4 + (level - 1);
  }

  /**
   * 根据颜色计算基础Y坐标
   */
  private static calculateBaseY(color: BasicColorType, level: DataLevel): number {
    const colorIndex = Object.keys(AVAILABLE_LEVELS).indexOf(color);
    return Math.floor(colorIndex / 3) * 4 + (level - 1);
  }
}

// ===== 数据验证服务 =====

export class MatrixDataValidationService {
  /**
   * 验证矩阵数据集
   */
  static validateDataSet(dataSet: MatrixDataSet): DataValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const statistics = this.calculateStatistics(dataSet);

    // 验证数据完整性
    if (!dataSet.data || dataSet.data.length === 0) {
      errors.push('数据集为空');
    }

    if (!dataSet.metadata) {
      errors.push('缺少元数据');
    }

    // 验证坐标唯一性
    const coordinateSet = new Set<string>();
    const duplicates: string[] = [];

    dataSet.data.forEach(point => {
      const key = `${point.x},${point.y}`;
      if (coordinateSet.has(key)) {
        duplicates.push(key);
      } else {
        coordinateSet.add(key);
      }
    });

    if (duplicates.length > 0) {
      errors.push(`发现重复坐标: ${duplicates.join(', ')}`);
    }

    // 验证数据范围
    dataSet.data.forEach(point => {
      if (point.x < 0 || point.x >= 33 || point.y < 0 || point.y >= 33) {
        errors.push(`坐标超出范围: (${point.x}, ${point.y})`);
      }

      if (!AVAILABLE_LEVELS[point.color]?.includes(point.level)) {
        errors.push(`无效的颜色级别组合: ${point.color}-${point.level}`);
      }
    });

    // 检查数据分布
    if (statistics.coverage < 0.1) {
      warnings.push('数据覆盖率过低');
    }

    const colorDistribution = Object.values(statistics.pointsByColor);
    const maxPoints = Math.max(...colorDistribution);
    const minPoints = Math.min(...colorDistribution);
    
    if (maxPoints > minPoints * 3) {
      warnings.push('颜色分布不均匀');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      statistics
    };
  }

  /**
   * 计算数据统计信息
   */
  private static calculateStatistics(dataSet: MatrixDataSet) {
    const pointsByColor: Record<BasicColorType, number> = {} as any;
    const pointsByLevel: Record<DataLevel, number> = {} as any;

    // 初始化计数器
    Object.keys(AVAILABLE_LEVELS).forEach(color => {
      pointsByColor[color as BasicColorType] = 0;
    });
    [1, 2, 3].forEach(level => {
      pointsByLevel[level as DataLevel] = 0;
    });

    // 统计数据
    dataSet.data.forEach(point => {
      pointsByColor[point.color]++;
      pointsByLevel[point.level]++;
    });

    const totalPoints = dataSet.data.length;
    const maxPossiblePoints = 33 * 33; // 矩阵大小
    const coverage = totalPoints / maxPossiblePoints;

    return {
      totalPoints,
      pointsByColor,
      pointsByLevel,
      coverage
    };
  }
}

// ===== 数据查询服务 =====

export class MatrixDataQueryService {
  /**
   * 按区域查询数据点
   */
  static queryByRegion(
    dataSet: MatrixDataSet,
    region: { startX: number; startY: number; endX: number; endY: number }
  ): MatrixDataPoint[] {
    return dataSet.data.filter(point => 
      point.x >= region.startX && point.x <= region.endX &&
      point.y >= region.startY && point.y <= region.endY
    );
  }

  /**
   * 按颜色查询数据点
   */
  static queryByColor(
    dataSet: MatrixDataSet,
    colors: BasicColorType[]
  ): MatrixDataPoint[] {
    return dataSet.data.filter(point => colors.includes(point.color));
  }

  /**
   * 按级别查询数据点
   */
  static queryByLevel(
    dataSet: MatrixDataSet,
    levels: DataLevel[]
  ): MatrixDataPoint[] {
    return dataSet.data.filter(point => levels.includes(point.level));
  }

  /**
   * 按组查询数据点
   */
  static queryByGroup(
    dataSet: MatrixDataSet,
    groups: string[]
  ): MatrixDataPoint[] {
    return dataSet.data.filter(point => groups.includes(point.group));
  }

  /**
   * 复合查询
   */
  static complexQuery(
    dataSet: MatrixDataSet,
    filters: {
      colors?: BasicColorType[];
      levels?: DataLevel[];
      groups?: string[];
      region?: { startX: number; startY: number; endX: number; endY: number };
    }
  ): MatrixDataPoint[] {
    let result = dataSet.data;

    if (filters.colors) {
      result = result.filter(point => filters.colors!.includes(point.color));
    }

    if (filters.levels) {
      result = result.filter(point => filters.levels!.includes(point.level));
    }

    if (filters.groups) {
      result = result.filter(point => filters.groups!.includes(point.group));
    }

    if (filters.region) {
      const { startX, startY, endX, endY } = filters.region;
      result = result.filter(point => 
        point.x >= startX && point.x <= endX &&
        point.y >= startY && point.y <= endY
      );
    }

    return result;
  }
}

// ===== 导出类型 =====

export type {
  DataGenerationOptions,
  CoordinateTransformOptions,
  DataValidationResult,
};
