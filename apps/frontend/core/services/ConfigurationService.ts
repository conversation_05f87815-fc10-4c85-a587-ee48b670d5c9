/**
 * 配置管理服务
 * 🎯 核心价值：统一的配置管理，支持动态配置、环境配置、用户偏好
 * 📦 功能范围：配置加载、配置验证、配置持久化、配置监听
 * 🔄 架构设计：观察者模式 + 策略模式，支持热更新和配置继承
 */

import type { MatrixConfig, BusinessMode, MainMode, ContentMode } from '../matrix/MatrixTypes';

// ===== 类型定义 =====

interface ConfigurationSchema {
  /** 配置键 */
  key: string;
  /** 配置类型 */
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  /** 默认值 */
  defaultValue: any;
  /** 是否必需 */
  required?: boolean;
  /** 验证函数 */
  validator?: (value: any) => boolean;
  /** 描述 */
  description?: string;
}

interface ConfigurationProfile {
  /** 配置文件名称 */
  name: string;
  /** 配置数据 */
  config: Record<string, any>;
  /** 继承的配置文件 */
  extends?: string;
  /** 环境限制 */
  environment?: string[];
  /** 版本 */
  version: string;
}

interface ConfigurationListener {
  /** 监听的配置键 */
  key: string;
  /** 回调函数 */
  callback: (newValue: any, oldValue: any) => void;
  /** 是否立即执行 */
  immediate?: boolean;
}

interface ConfigurationValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 错误信息 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
}

// ===== 预定义配置模式 =====

const MATRIX_CONFIG_SCHEMA: ConfigurationSchema[] = [
  {
    key: 'mainMode',
    type: 'string',
    defaultValue: 'color',
    required: true,
    validator: (value) => ['color', 'number'].includes(value),
    description: '主要显示模式',
  },
  {
    key: 'contentMode',
    type: 'string',
    defaultValue: 'word',
    required: true,
    validator: (value) => ['word', 'number', 'coordinate'].includes(value),
    description: '内容显示模式',
  },
  {
    key: 'enableAnimation',
    type: 'boolean',
    defaultValue: true,
    description: '是否启用动画效果',
  },
  {
    key: 'cellSize',
    type: 'number',
    defaultValue: 40,
    validator: (value) => value >= 20 && value <= 100,
    description: '单元格大小（像素）',
  },
  {
    key: 'theme',
    type: 'object',
    defaultValue: {
      primaryColor: '#007bff',
      secondaryColor: '#6c757d',
      backgroundColor: '#ffffff',
    },
    description: '主题配置',
  },
];

const DEFAULT_PROFILES: ConfigurationProfile[] = [
  {
    name: 'default',
    version: '1.0.0',
    config: {
      mainMode: 'color',
      contentMode: 'word',
      enableAnimation: true,
      cellSize: 40,
      theme: {
        primaryColor: '#007bff',
        secondaryColor: '#6c757d',
        backgroundColor: '#ffffff',
      },
    },
  },
  {
    name: 'performance',
    version: '1.0.0',
    extends: 'default',
    config: {
      enableAnimation: false,
      enableCaching: true,
      batchUpdates: true,
    },
  },
  {
    name: 'accessibility',
    version: '1.0.0',
    extends: 'default',
    config: {
      highContrast: true,
      largeText: true,
      keyboardNavigation: true,
      screenReaderSupport: true,
    },
  },
];

// ===== 配置管理服务 =====

export class ConfigurationService {
  private static instance: ConfigurationService;
  private profiles = new Map<string, ConfigurationProfile>();
  private currentProfile = 'default';
  private config = new Map<string, any>();
  private listeners = new Map<string, ConfigurationListener[]>();
  private schema = new Map<string, ConfigurationSchema>();

  private constructor() {
    this.initializeSchema();
    this.loadDefaultProfiles();
    this.loadFromStorage();
  }

  static getInstance(): ConfigurationService {
    if (!this.instance) {
      this.instance = new ConfigurationService();
    }
    return this.instance;
  }

  // ===== 配置文件管理 =====

  /**
   * 加载配置文件
   */
  loadProfile(name: string): boolean {
    const profile = this.profiles.get(name);
    if (!profile) {
      console.error(`配置文件不存在: ${name}`);
      return false;
    }

    try {
      // 解析继承关系
      const resolvedConfig = this.resolveProfileInheritance(profile);
      
      // 验证配置
      const validation = this.validateConfiguration(resolvedConfig);
      if (!validation.isValid) {
        console.error('配置验证失败:', validation.errors);
        return false;
      }

      // 应用配置
      const oldConfig = new Map(this.config);
      this.config.clear();
      
      Object.entries(resolvedConfig).forEach(([key, value]) => {
        this.config.set(key, value);
      });

      this.currentProfile = name;

      // 触发监听器
      this.notifyListeners(oldConfig);

      // 持久化
      this.saveToStorage();

      return true;
    } catch (error) {
      console.error('加载配置文件失败:', error);
      return false;
    }
  }

  /**
   * 保存配置文件
   */
  saveProfile(name: string, config: Record<string, any>): boolean {
    try {
      const profile: ConfigurationProfile = {
        name,
        version: '1.0.0',
        config,
      };

      this.profiles.set(name, profile);
      this.saveToStorage();
      
      return true;
    } catch (error) {
      console.error('保存配置文件失败:', error);
      return false;
    }
  }

  /**
   * 删除配置文件
   */
  deleteProfile(name: string): boolean {
    if (name === 'default') {
      console.error('不能删除默认配置文件');
      return false;
    }

    if (this.currentProfile === name) {
      console.error('不能删除当前使用的配置文件');
      return false;
    }

    return this.profiles.delete(name);
  }

  /**
   * 获取所有配置文件
   */
  getProfiles(): ConfigurationProfile[] {
    return Array.from(this.profiles.values());
  }

  // ===== 配置操作 =====

  /**
   * 获取配置值
   */
  get<T = any>(key: string, defaultValue?: T): T {
    if (this.config.has(key)) {
      return this.config.get(key) as T;
    }

    // 从schema获取默认值
    const schemaItem = this.schema.get(key);
    if (schemaItem) {
      return schemaItem.defaultValue as T;
    }

    return defaultValue as T;
  }

  /**
   * 设置配置值
   */
  set(key: string, value: any): boolean {
    try {
      // 验证值
      const schemaItem = this.schema.get(key);
      if (schemaItem && schemaItem.validator && !schemaItem.validator(value)) {
        console.error(`配置值验证失败: ${key} = ${value}`);
        return false;
      }

      const oldValue = this.config.get(key);
      this.config.set(key, value);

      // 触发监听器
      this.notifyKeyListeners(key, value, oldValue);

      // 持久化
      this.saveToStorage();

      return true;
    } catch (error) {
      console.error('设置配置失败:', error);
      return false;
    }
  }

  /**
   * 批量设置配置
   */
  setMany(configs: Record<string, any>): boolean {
    try {
      const oldConfigs = new Map<string, any>();
      
      // 验证所有配置
      for (const [key, value] of Object.entries(configs)) {
        const schemaItem = this.schema.get(key);
        if (schemaItem && schemaItem.validator && !schemaItem.validator(value)) {
          console.error(`配置值验证失败: ${key} = ${value}`);
          return false;
        }
        oldConfigs.set(key, this.config.get(key));
      }

      // 应用所有配置
      Object.entries(configs).forEach(([key, value]) => {
        this.config.set(key, value);
      });

      // 触发监听器
      Object.entries(configs).forEach(([key, value]) => {
        this.notifyKeyListeners(key, value, oldConfigs.get(key));
      });

      // 持久化
      this.saveToStorage();

      return true;
    } catch (error) {
      console.error('批量设置配置失败:', error);
      return false;
    }
  }

  /**
   * 重置配置
   */
  reset(keys?: string[]): void {
    if (keys) {
      // 重置指定配置
      keys.forEach(key => {
        const schemaItem = this.schema.get(key);
        if (schemaItem) {
          this.set(key, schemaItem.defaultValue);
        }
      });
    } else {
      // 重置所有配置
      this.loadProfile('default');
    }
  }

  // ===== 监听器管理 =====

  /**
   * 添加配置监听器
   */
  addListener(listener: ConfigurationListener): void {
    const { key } = listener;
    
    if (!this.listeners.has(key)) {
      this.listeners.set(key, []);
    }
    
    this.listeners.get(key)!.push(listener);

    // 立即执行
    if (listener.immediate) {
      const currentValue = this.get(key);
      listener.callback(currentValue, undefined);
    }
  }

  /**
   * 移除配置监听器
   */
  removeListener(key: string, callback: Function): boolean {
    const keyListeners = this.listeners.get(key);
    if (!keyListeners) return false;

    const index = keyListeners.findIndex(l => l.callback === callback);
    if (index !== -1) {
      keyListeners.splice(index, 1);
      return true;
    }

    return false;
  }

  // ===== 矩阵专用方法 =====

  /**
   * 获取矩阵配置
   */
  getMatrixConfig(): MatrixConfig {
    const mainMode = this.get<MainMode>('mainMode', 'color');
    const contentMode = this.get<ContentMode>('contentMode', 'word');
    
    return {
      mode: `${mainMode}-${contentMode}` as BusinessMode,
      mainMode,
      contentMode,
      businessMode: `${mainMode}-${contentMode}` as BusinessMode,
      isColorMode: mainMode === 'color',
      isWordMode: contentMode === 'word',
      isNumberMode: contentMode === 'number',
      isCoordinateMode: contentMode === 'coordinate',
    };
  }

  /**
   * 更新矩阵配置
   */
  updateMatrixConfig(config: Partial<MatrixConfig>): boolean {
    const updates: Record<string, any> = {};
    
    if (config.mainMode) {
      updates.mainMode = config.mainMode;
    }
    
    if (config.contentMode) {
      updates.contentMode = config.contentMode;
    }

    return this.setMany(updates);
  }

  // ===== 私有方法 =====

  private initializeSchema(): void {
    MATRIX_CONFIG_SCHEMA.forEach(schema => {
      this.schema.set(schema.key, schema);
    });
  }

  private loadDefaultProfiles(): void {
    DEFAULT_PROFILES.forEach(profile => {
      this.profiles.set(profile.name, profile);
    });
  }

  private resolveProfileInheritance(profile: ConfigurationProfile): Record<string, any> {
    const resolved: Record<string, any> = {};

    // 递归解析继承
    if (profile.extends) {
      const parentProfile = this.profiles.get(profile.extends);
      if (parentProfile) {
        const parentConfig = this.resolveProfileInheritance(parentProfile);
        Object.assign(resolved, parentConfig);
      }
    }

    // 应用当前配置
    Object.assign(resolved, profile.config);

    return resolved;
  }

  private validateConfiguration(config: Record<string, any>): ConfigurationValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查必需配置
    this.schema.forEach((schema, key) => {
      if (schema.required && !(key in config)) {
        errors.push(`缺少必需配置: ${key}`);
      }
    });

    // 验证配置值
    Object.entries(config).forEach(([key, value]) => {
      const schema = this.schema.get(key);
      if (schema && schema.validator && !schema.validator(value)) {
        errors.push(`配置值无效: ${key} = ${value}`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  private notifyListeners(oldConfig: Map<string, any>): void {
    this.config.forEach((value, key) => {
      const oldValue = oldConfig.get(key);
      if (value !== oldValue) {
        this.notifyKeyListeners(key, value, oldValue);
      }
    });
  }

  private notifyKeyListeners(key: string, newValue: any, oldValue: any): void {
    const keyListeners = this.listeners.get(key);
    if (keyListeners) {
      keyListeners.forEach(listener => {
        try {
          listener.callback(newValue, oldValue);
        } catch (error) {
          console.error(`配置监听器执行失败: ${key}`, error);
        }
      });
    }
  }

  private saveToStorage(): void {
    try {
      const data = {
        currentProfile: this.currentProfile,
        config: Object.fromEntries(this.config),
        profiles: Object.fromEntries(this.profiles),
      };
      
      localStorage.setItem('matrix-configuration', JSON.stringify(data));
    } catch (error) {
      console.error('保存配置到本地存储失败:', error);
    }
  }

  private loadFromStorage(): void {
    try {
      const data = localStorage.getItem('matrix-configuration');
      if (data) {
        const parsed = JSON.parse(data);
        
        if (parsed.profiles) {
          Object.entries(parsed.profiles).forEach(([name, profile]) => {
            this.profiles.set(name, profile as ConfigurationProfile);
          });
        }
        
        if (parsed.currentProfile) {
          this.loadProfile(parsed.currentProfile);
        }
      }
    } catch (error) {
      console.error('从本地存储加载配置失败:', error);
      this.loadProfile('default');
    }
  }
}

// ===== 导出便捷函数 =====

export const configService = ConfigurationService.getInstance();

export const getMatrixConfig = (): MatrixConfig => {
  return configService.getMatrixConfig();
};

export const updateMatrixConfig = (config: Partial<MatrixConfig>): boolean => {
  return configService.updateMatrixConfig(config);
};

// ===== 导出类型 =====

export type {
  ConfigurationSchema,
  ConfigurationProfile,
  ConfigurationListener,
  ConfigurationValidationResult,
};
