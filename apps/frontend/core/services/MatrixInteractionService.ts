/**
 * 矩阵交互服务
 * 🎯 核心价值：统一的矩阵交互逻辑处理，封装复杂的用户交互行为
 * 📦 功能范围：点击处理、选择管理、拖拽操作、键盘导航
 * 🔄 架构设计：事件驱动架构，支持插件式扩展和中间件
 */

import type {
  CellData,
  MatrixConfig,
  MatrixData,
  Coordinate,
  InteractionEvent,
  InteractionEventType
} from '../matrix/MatrixTypes';
import { coordinateKey } from '../matrix/MatrixTypes';
import { createConditionalChain, CellStateChecker, ModeChecker } from '../utils/ConditionalLogicUtils';

// ===== 类型定义 =====

interface InteractionContext {
  /** 交互事件 */
  event: InteractionEvent;
  /** 目标单元格 */
  targetCell: CellData;
  /** 矩阵配置 */
  config: MatrixConfig;
  /** 矩阵数据 */
  matrixData: MatrixData;
  /** 当前选择状态 */
  currentSelection: Set<string>;
}

interface InteractionResult {
  /** 是否处理成功 */
  success: boolean;
  /** 更新的单元格 */
  updatedCells: Map<string, Partial<CellData>>;
  /** 新的选择状态 */
  newSelection: Set<string>;
  /** 触发的事件 */
  triggeredEvents: string[];
  /** 错误信息 */
  error?: string;
}

interface InteractionHandler {
  /** 处理器名称 */
  name: string;
  /** 优先级 */
  priority: number;
  /** 是否可以处理该交互 */
  canHandle: (context: InteractionContext) => boolean;
  /** 处理交互 */
  handle: (context: InteractionContext) => InteractionResult;
}

interface SelectionOptions {
  /** 选择模式 */
  mode: 'single' | 'multiple' | 'range';
  /** 是否允许取消选择 */
  allowDeselect: boolean;
  /** 最大选择数量 */
  maxSelection?: number;
  /** 选择过滤器 */
  filter?: (cell: CellData) => boolean;
}

interface DragOperation {
  /** 拖拽类型 */
  type: 'move' | 'copy' | 'select';
  /** 起始坐标 */
  startCoordinate: Coordinate;
  /** 当前坐标 */
  currentCoordinate: Coordinate;
  /** 拖拽的数据 */
  dragData?: any;
  /** 是否正在拖拽 */
  isDragging: boolean;
}

// ===== 交互处理器 =====

/**
 * 单击处理器
 */
const clickHandler: InteractionHandler = {
  name: 'click',
  priority: 100,
  canHandle: (context) => context.event.type === 'click',
  handle: (context) => {
    const { targetCell, config, currentSelection } = context;
    const cellKey = coordinateKey(targetCell.x, targetCell.y);
    const updatedCells = new Map<string, Partial<CellData>>();
    let newSelection = new Set(currentSelection);
    const triggeredEvents: string[] = [];

    // 处理选择逻辑
    if (context.event.modifiers?.ctrl || context.event.modifiers?.meta) {
      // Ctrl/Cmd + 点击：多选模式
      if (currentSelection.has(cellKey)) {
        newSelection.delete(cellKey);
        updatedCells.set(cellKey, { isSelected: false });
        triggeredEvents.push('cell-deselected');
      } else {
        newSelection.add(cellKey);
        updatedCells.set(cellKey, { isSelected: true });
        triggeredEvents.push('cell-selected');
      }
    } else {
      // 普通点击：单选模式
      // 清除之前的选择
      currentSelection.forEach(key => {
        updatedCells.set(key, { isSelected: false });
      });
      
      // 选择当前单元格
      newSelection = new Set([cellKey]);
      updatedCells.set(cellKey, { isSelected: true });
      triggeredEvents.push('selection-changed');
    }

    return {
      success: true,
      updatedCells,
      newSelection,
      triggeredEvents,
    };
  },
};

/**
 * 双击处理器
 */
const doubleClickHandler: InteractionHandler = {
  name: 'doubleClick',
  priority: 90,
  canHandle: (context) => context.event.type === 'doubleClick',
  handle: (context) => {
    const { targetCell, config } = context;
    const cellKey = coordinateKey(targetCell.x, targetCell.y);
    const updatedCells = new Map<string, Partial<CellData>>();
    const triggeredEvents: string[] = [];

    // 双击进入编辑模式（如果是词语模式）
    if (ModeChecker.isWordMode(config)) {
      triggeredEvents.push('enter-edit-mode');
    }

    // 双击激活单元格
    if (!targetCell.isActive) {
      updatedCells.set(cellKey, { isActive: true });
      triggeredEvents.push('cell-activated');
    }

    return {
      success: true,
      updatedCells,
      newSelection: new Set([cellKey]),
      triggeredEvents,
    };
  },
};

/**
 * 悬停处理器
 */
const hoverHandler: InteractionHandler = {
  name: 'hover',
  priority: 80,
  canHandle: (context) => context.event.type === 'mouseEnter' || context.event.type === 'mouseLeave',
  handle: (context) => {
    const { targetCell, event } = context;
    const cellKey = coordinateKey(targetCell.x, targetCell.y);
    const updatedCells = new Map<string, Partial<CellData>>();
    const triggeredEvents: string[] = [];

    if (event.type === 'mouseEnter') {
      updatedCells.set(cellKey, { isHovered: true });
      triggeredEvents.push('cell-hovered');
    } else {
      updatedCells.set(cellKey, { isHovered: false });
      triggeredEvents.push('cell-unhovered');
    }

    return {
      success: true,
      updatedCells,
      newSelection: new Set(),
      triggeredEvents,
    };
  },
};

/**
 * 焦点处理器
 */
const focusHandler: InteractionHandler = {
  name: 'focus',
  priority: 70,
  canHandle: (context) => context.event.type === 'focus' || context.event.type === 'blur',
  handle: (context) => {
    const { targetCell, event, matrixData } = context;
    const cellKey = coordinateKey(targetCell.x, targetCell.y);
    const updatedCells = new Map<string, Partial<CellData>>();
    const triggeredEvents: string[] = [];

    if (event.type === 'focus') {
      // 清除之前的焦点
      matrixData.cells.forEach((cell, key) => {
        if (cell.isFocused) {
          updatedCells.set(key, { isFocused: false });
        }
      });

      // 设置新焦点
      updatedCells.set(cellKey, { isFocused: true });
      triggeredEvents.push('cell-focused');
    } else {
      updatedCells.set(cellKey, { isFocused: false });
      triggeredEvents.push('cell-blurred');
    }

    return {
      success: true,
      updatedCells,
      newSelection: new Set(),
      triggeredEvents,
    };
  },
};

// ===== 主服务类 =====

export class MatrixInteractionService {
  private static handlers: InteractionHandler[] = [
    clickHandler,
    doubleClickHandler,
    hoverHandler,
    focusHandler,
  ];

  private static dragOperation: DragOperation | null = null;

  /**
   * 处理交互事件
   */
  static handleInteraction(context: InteractionContext): InteractionResult {
    // 按优先级排序处理器
    const sortedHandlers = [...this.handlers].sort((a, b) => b.priority - a.priority);

    // 找到第一个可以处理的处理器
    for (const handler of sortedHandlers) {
      if (handler.canHandle(context)) {
        try {
          return handler.handle(context);
        } catch (error) {
          return {
            success: false,
            updatedCells: new Map(),
            newSelection: new Set(),
            triggeredEvents: [],
            error: `处理器 ${handler.name} 执行失败: ${error}`,
          };
        }
      }
    }

    // 没有找到合适的处理器
    return {
      success: false,
      updatedCells: new Map(),
      newSelection: new Set(),
      triggeredEvents: [],
      error: '没有找到合适的交互处理器',
    };
  }

  /**
   * 注册自定义处理器
   */
  static registerHandler(handler: InteractionHandler): void {
    this.handlers.push(handler);
  }

  /**
   * 移除处理器
   */
  static removeHandler(name: string): boolean {
    const index = this.handlers.findIndex(h => h.name === name);
    if (index !== -1) {
      this.handlers.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * 处理选择操作
   */
  static handleSelection(
    coordinates: Coordinate[],
    currentSelection: Set<string>,
    options: SelectionOptions
  ): Set<string> {
    const { mode, allowDeselect, maxSelection, filter } = options;
    let newSelection = new Set(currentSelection);

    switch (mode) {
      case 'single':
        if (coordinates.length > 0) {
          const coord = coordinates[0];
          const key = coordinateKey(coord.x, coord.y);
          
          if (allowDeselect && currentSelection.has(key)) {
            newSelection = new Set();
          } else {
            newSelection = new Set([key]);
          }
        }
        break;

      case 'multiple':
        coordinates.forEach(coord => {
          const key = coordinateKey(coord.x, coord.y);
          
          if (currentSelection.has(key)) {
            if (allowDeselect) {
              newSelection.delete(key);
            }
          } else {
            if (!maxSelection || newSelection.size < maxSelection) {
              newSelection.add(key);
            }
          }
        });
        break;

      case 'range':
        if (coordinates.length >= 2) {
          const start = coordinates[0];
          const end = coordinates[coordinates.length - 1];
          
          const minX = Math.min(start.x, end.x);
          const maxX = Math.max(start.x, end.x);
          const minY = Math.min(start.y, end.y);
          const maxY = Math.max(start.y, end.y);
          
          newSelection = new Set();
          for (let y = minY; y <= maxY; y++) {
            for (let x = minX; x <= maxX; x++) {
              const key = coordinateKey(x, y);
              newSelection.add(key);
            }
          }
        }
        break;
    }

    return newSelection;
  }

  /**
   * 开始拖拽操作
   */
  static startDrag(
    startCoordinate: Coordinate,
    type: DragOperation['type'] = 'select',
    dragData?: any
  ): void {
    this.dragOperation = {
      type,
      startCoordinate,
      currentCoordinate: startCoordinate,
      dragData,
      isDragging: true,
    };
  }

  /**
   * 更新拖拽位置
   */
  static updateDrag(currentCoordinate: Coordinate): DragOperation | null {
    if (this.dragOperation) {
      this.dragOperation.currentCoordinate = currentCoordinate;
    }
    return this.dragOperation;
  }

  /**
   * 结束拖拽操作
   */
  static endDrag(): DragOperation | null {
    const operation = this.dragOperation;
    this.dragOperation = null;
    return operation;
  }

  /**
   * 获取当前拖拽状态
   */
  static getDragOperation(): DragOperation | null {
    return this.dragOperation;
  }

  /**
   * 处理键盘导航
   */
  static handleKeyboardNavigation(
    currentFocus: Coordinate | null,
    key: string,
    matrixData: MatrixData
  ): Coordinate | null {
    if (!currentFocus) return null;

    let newX = currentFocus.x;
    let newY = currentFocus.y;

    switch (key) {
      case 'ArrowUp':
        newY = Math.max(0, newY - 1);
        break;
      case 'ArrowDown':
        newY = Math.min(32, newY + 1); // MATRIX_SIZE - 1
        break;
      case 'ArrowLeft':
        newX = Math.max(0, newX - 1);
        break;
      case 'ArrowRight':
        newX = Math.min(32, newX + 1); // MATRIX_SIZE - 1
        break;
      case 'Home':
        newX = 0;
        break;
      case 'End':
        newX = 32; // MATRIX_SIZE - 1
        break;
      case 'PageUp':
        newY = Math.max(0, newY - 10);
        break;
      case 'PageDown':
        newY = Math.min(32, newY + 10); // MATRIX_SIZE - 1
        break;
      default:
        return currentFocus;
    }

    return { x: newX, y: newY };
  }

  /**
   * 批量处理交互事件
   */
  static handleBatchInteractions(
    contexts: InteractionContext[]
  ): InteractionResult[] {
    return contexts.map(context => this.handleInteraction(context));
  }

  /**
   * 验证交互权限
   */
  static validateInteraction(
    context: InteractionContext,
    permissions?: string[]
  ): boolean {
    // 基础验证
    if (!context.targetCell || !context.event) {
      return false;
    }

    // 权限验证（如果提供）
    if (permissions && permissions.length > 0) {
      // 这里可以添加具体的权限检查逻辑
      return true;
    }

    return true;
  }
}

// ===== 导出类型 =====

export type {
  InteractionContext,
  InteractionResult,
  InteractionHandler,
  SelectionOptions,
  DragOperation,
};
