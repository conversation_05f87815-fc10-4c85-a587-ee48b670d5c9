/**
 * 直接填词输入服务
 * 🎯 核心价值：绕过复杂的交互逻辑，直接处理双击激活填词模式
 * 📦 功能范围：双击检测、状态激活、滑动触发
 * 🔄 架构设计：简单直接的事件处理，避免复杂的中间层
 */

import type { BasicColorType, DataLevel, WordLibraryKey } from '@/core/matrix/MatrixTypes';
import { createWordLibraryKey } from '@/core/matrix/MatrixTypes';

// ===== 类型定义 =====

interface CellInfo {
  x: number;
  y: number;
  color: BasicColorType;
  level: DataLevel;
}

interface DoubleClickResult {
  success: boolean;
  libraryKey?: WordLibraryKey;
  error?: string;
}

// ===== 直接填词输入服务 =====

export class DirectWordInputService {
  private static instance: DirectWordInputService | null = null;
  private isInitialized = false;
  private lastDoubleClickTime = 0;
  private readonly DOUBLE_CLICK_DELAY = 300; // 防抖延迟

  private constructor() {}

  /**
   * 获取服务实例
   */
  static getInstance(): DirectWordInputService {
    if (!this.instance) {
      this.instance = new DirectWordInputService();
    }
    return this.instance;
  }

  /**
   * 初始化服务
   */
  initialize(): void {
    if (this.isInitialized) {
      return;
    }

    console.log('[DirectWordInputService] 初始化服务');
    this.setupGlobalDoubleClickHandler();
    this.isInitialized = true;
  }

  /**
   * 设置全局双击处理器
   */
  private setupGlobalDoubleClickHandler(): void {
    // 使用事件委托监听整个文档的双击事件
    document.addEventListener('dblclick', (event) => {
      this.handleGlobalDoubleClick(event);
    }, true); // 使用捕获阶段，确保优先处理
  }

  /**
   * 处理全局双击事件
   */
  private handleGlobalDoubleClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    
    // 检查是否是矩阵单元格
    const cellElement = target.closest('[data-x][data-y]') as HTMLElement;
    if (!cellElement) {
      return;
    }

    // 防抖处理
    const now = Date.now();
    if (now - this.lastDoubleClickTime < this.DOUBLE_CLICK_DELAY) {
      return;
    }
    this.lastDoubleClickTime = now;

    // 获取单元格信息
    const cellInfo = this.extractCellInfo(cellElement);
    if (!cellInfo) {
      console.warn('[DirectWordInputService] 无法获取单元格信息');
      return;
    }

    console.log('[DirectWordInputService] 检测到双击:', cellInfo);

    // 检查是否为颜色+词语模式
    if (!this.isColorWordMode()) {
      console.log('[DirectWordInputService] 非颜色+词语模式，跳过处理');
      return;
    }

    // 阻止事件继续传播，避免其他处理器干扰
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();

    // 处理双击激活
    this.handleDoubleClickActivation(cellInfo);
  }

  /**
   * 提取单元格信息
   */
  private extractCellInfo(cellElement: HTMLElement): CellInfo | null {
    const x = parseInt(cellElement.getAttribute('data-x') || '0');
    const y = parseInt(cellElement.getAttribute('data-y') || '0');
    const color = cellElement.getAttribute('data-color') as BasicColorType;
    const level = parseInt(cellElement.getAttribute('data-level') || '1') as DataLevel;

    if (isNaN(x) || isNaN(y) || !color || isNaN(level)) {
      return null;
    }

    return { x, y, color, level };
  }

  /**
   * 检查是否为颜色+词语模式
   */
  private isColorWordMode(): boolean {
    try {
      // 动态导入避免循环依赖
      const { useMatrixStore } = require('@/core/matrix/MatrixStore');
      const matrixStore = useMatrixStore.getState();
      const config = matrixStore.config;
      
      return config.mainMode === 'color' && config.contentMode === 'word';
    } catch (error) {
      console.error('[DirectWordInputService] 检查模式失败:', error);
      return false;
    }
  }

  /**
   * 处理双击激活
   */
  private async handleDoubleClickActivation(cellInfo: CellInfo): Promise<DoubleClickResult> {
    try {
      console.log('[DirectWordInputService] 开始激活填词模式:', cellInfo);

      const libraryKey = createWordLibraryKey(cellInfo.color, cellInfo.level);

      // 检查词库是否存在
      const libraryExists = await this.checkLibraryExists(libraryKey);
      if (!libraryExists) {
        console.warn('[DirectWordInputService] 词库不存在:', libraryKey);
        this.showToast('请先填入词语', 'warning');
      }

      // 获取已绑定的词语
      const boundWordId = this.getBoundWordId(cellInfo.x, cellInfo.y);

      // 激活填词模式
      await this.activateWordInput(cellInfo, boundWordId);

      // 触发滑动
      this.triggerLibraryScroll(libraryKey);

      console.log('[DirectWordInputService] 填词模式激活成功');
      return { success: true, libraryKey };

    } catch (error) {
      console.error('[DirectWordInputService] 激活失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 检查词库是否存在且有词语
   */
  private async checkLibraryExists(libraryKey: WordLibraryKey): Promise<boolean> {
    try {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const wordLibraryStore = useWordLibraryStore.getState();
      const library = wordLibraryStore.getLibrary(libraryKey);
      
      return library && library.words.length > 0;
    } catch (error) {
      console.error('[DirectWordInputService] 检查词库失败:', error);
      return false;
    }
  }

  /**
   * 获取单元格绑定的词语ID
   */
  private getBoundWordId(x: number, y: number): string | null {
    try {
      const { useMatrixStore } = require('@/core/matrix/MatrixStore');
      const matrixStore = useMatrixStore.getState();
      return matrixStore.getCellWord(x, y);
    } catch (error) {
      console.error('[DirectWordInputService] 获取绑定词语失败:', error);
      return null;
    }
  }

  /**
   * 激活填词模式
   */
  private async activateWordInput(cellInfo: CellInfo, boundWordId?: string | null): Promise<void> {
    try {
      const { useWordInputStore } = require('@/core/wordLibrary/WordLibraryStore');
      const wordInputStore = useWordInputStore.getState();
      
      await wordInputStore.activateWordInput(
        cellInfo.x,
        cellInfo.y,
        cellInfo.color,
        cellInfo.level,
        boundWordId || undefined
      );

      console.log('[DirectWordInputService] 填词状态已激活');
    } catch (error) {
      console.error('[DirectWordInputService] 激活填词状态失败:', error);
      throw error;
    }
  }

  /**
   * 触发词库滑动
   */
  private triggerLibraryScroll(libraryKey: WordLibraryKey): void {
    // 延迟执行，确保DOM已更新
    setTimeout(() => {
      try {
        const container = document.querySelector('.word-library-manager .overflow-y-auto') as HTMLElement;
        const target = document.querySelector(`[data-word-library="${libraryKey}"]`) as HTMLElement;

        if (!container || !target) {
          console.warn('[DirectWordInputService] 滑动失败: 未找到容器或目标元素');
          return;
        }

        // 计算滑动位置
        const containerRect = container.getBoundingClientRect();
        const targetRect = target.getBoundingClientRect();

        const current = container.scrollTop;
        const targetCenter = targetRect.top + targetRect.height / 2;
        const containerCenter = containerRect.top + containerRect.height / 2;
        const targetPosition = current + (targetCenter - containerCenter);
        const delta = targetPosition - current;

        console.log('[DirectWordInputService] 滑动计算:', {
          libraryKey,
          current,
          target: targetPosition,
          delta,
          action: delta === 0 ? '不移动' : delta < 0 ? '上移' : '下移'
        });

        // 执行滑动
        if (Math.abs(delta) > 5) {
          container.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
          });
          console.log(`[DirectWordInputService] 滑动执行: ${delta < 0 ? '上移' : '下移'} ${Math.abs(delta)}px`);
        } else {
          console.log('[DirectWordInputService] 无需滑动: 已在正确位置');
        }

      } catch (error) {
        console.error('[DirectWordInputService] 滑动执行失败:', error);
      }
    }, 100);
  }

  /**
   * 显示提示消息
   */
  private showToast(message: string, type: 'success' | 'warning' | 'error' = 'success'): void {
    try {
      // 动态导入toast避免循环依赖
      const { toast } = require('react-hot-toast');
      toast[type](message, {
        position: 'top-center',
        duration: 2000
      });
    } catch (error) {
      console.warn('[DirectWordInputService] 显示提示失败:', error);
    }
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.isInitialized = false;
    console.log('[DirectWordInputService] 服务已销毁');
  }
}

// ===== 便捷方法 =====

/**
 * 获取直接填词输入服务实例
 */
export const getDirectWordInputService = () => DirectWordInputService.getInstance();

/**
 * 初始化直接填词输入服务
 */
export const initializeDirectWordInput = () => {
  const service = getDirectWordInputService();
  service.initialize();
  return service;
};

// ===== 导出类型 =====

export type {
  CellInfo,
  DoubleClickResult
};
