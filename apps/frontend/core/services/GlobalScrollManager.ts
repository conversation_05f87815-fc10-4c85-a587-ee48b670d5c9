/**
 * 全局滚动管理器
 * 🎯 核心价值：完全独立于React状态系统的滚动位置管理
 * 📦 功能范围：滚动位置保存、恢复、防抖处理
 * 🔄 架构设计：基于DOM的直接操作，避免React重新渲染影响
 */

// ===== 类型定义 =====

interface ScrollState {
  position: number;
  timestamp: number;
  isScrolling: boolean;
}

interface ScrollManagerOptions {
  debounceDelay?: number;
  debug?: boolean;
}

// ===== 全局滚动管理器 =====

export class GlobalScrollManager {
  private static instance: GlobalScrollManager | null = null;
  private scrollStates = new Map<string, ScrollState>();
  private scrollTimeouts = new Map<string, NodeJS.Timeout>();
  private observers = new Map<string, MutationObserver>();

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): GlobalScrollManager {
    if (!this.instance) {
      this.instance = new GlobalScrollManager();
    }
    return this.instance;
  }

  /**
   * 注册滚动容器
   */
  registerContainer(
    containerId: string, 
    selector: string, 
    options: ScrollManagerOptions = {}
  ): void {
    const { debounceDelay = 100, debug = false } = options;

    // 查找容器
    const container = document.querySelector(selector) as HTMLElement;
    if (!container) {
      if (debug) console.warn(`[GlobalScrollManager] 未找到容器: ${selector}`);
      return;
    }

    if (debug) console.log(`[GlobalScrollManager] 注册容器: ${containerId}`);

    // 初始化状态
    this.scrollStates.set(containerId, {
      position: container.scrollTop,
      timestamp: Date.now(),
      isScrolling: false
    });

    // 监听滚动事件
    const handleScroll = () => {
      // 清除之前的定时器
      const existingTimeout = this.scrollTimeouts.get(containerId);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }

      // 标记正在滚动
      const currentState = this.scrollStates.get(containerId);
      if (currentState) {
        currentState.isScrolling = true;
      }

      // 防抖保存位置
      const timeout = setTimeout(() => {
        this.savePosition(containerId, container.scrollTop);
        const state = this.scrollStates.get(containerId);
        if (state) {
          state.isScrolling = false;
        }
      }, debounceDelay);

      this.scrollTimeouts.set(containerId, timeout);
    };

    container.addEventListener('scroll', handleScroll, { passive: true });

    // 监听DOM变化，自动恢复位置
    const observer = new MutationObserver(() => {
      // 延迟恢复位置，确保DOM更新完成
      setTimeout(() => {
        this.restorePosition(containerId, selector);
      }, 50);
    });

    observer.observe(container.parentElement || container, {
      childList: true,
      subtree: true
    });

    this.observers.set(containerId, observer);
  }

  /**
   * 保存滚动位置
   */
  savePosition(containerId: string, position: number): void {
    const state = this.scrollStates.get(containerId);
    if (state) {
      state.position = position;
      state.timestamp = Date.now();
    } else {
      this.scrollStates.set(containerId, {
        position,
        timestamp: Date.now(),
        isScrolling: false
      });
    }
  }

  /**
   * 恢复滚动位置
   */
  restorePosition(containerId: string, selector?: string): boolean {
    const state = this.scrollStates.get(containerId);
    if (!state || state.position <= 0) {
      return false;
    }

    // 检查时间差，避免恢复过旧的位置
    const timeDiff = Date.now() - state.timestamp;
    if (timeDiff > 10000) { // 10秒内的位置才恢复
      return false;
    }

    // 查找容器
    const container = selector 
      ? document.querySelector(selector) as HTMLElement
      : this.findContainerById(containerId);

    if (!container) {
      return false;
    }

    // 恢复位置
    container.scrollTop = state.position;
    return true;
  }

  /**
   * 滚动到指定位置
   */
  scrollTo(
    containerId: string, 
    position: number, 
    smooth = true, 
    selector?: string
  ): boolean {
    const container = selector 
      ? document.querySelector(selector) as HTMLElement
      : this.findContainerById(containerId);

    if (!container) {
      return false;
    }

    if (smooth) {
      container.scrollTo({
        top: position,
        behavior: 'smooth'
      });
    } else {
      container.scrollTop = position;
    }

    // 更新保存的位置
    this.savePosition(containerId, position);
    return true;
  }

  /**
   * 滚动到指定元素
   */
  scrollToElement(
    containerId: string,
    targetElement: HTMLElement,
    block: ScrollLogicalPosition = 'center',
    smooth = true,
    selector?: string
  ): boolean {
    const container = selector 
      ? document.querySelector(selector) as HTMLElement
      : this.findContainerById(containerId);

    if (!container || !targetElement) {
      return false;
    }

    // 计算目标位置
    const containerRect = container.getBoundingClientRect();
    const targetRect = targetElement.getBoundingClientRect();

    const current = container.scrollTop;
    let targetPosition: number;

    switch (block) {
      case 'start':
        targetPosition = current + (targetRect.top - containerRect.top);
        break;
      case 'end':
        targetPosition = current + (targetRect.bottom - containerRect.bottom);
        break;
      case 'center':
      default:
        const targetCenter = targetRect.top + targetRect.height / 2;
        const containerCenter = containerRect.top + containerRect.height / 2;
        targetPosition = current + (targetCenter - containerCenter);
        break;
    }

    // 确保位置在有效范围内
    const maxScroll = container.scrollHeight - container.clientHeight;
    targetPosition = Math.max(0, Math.min(targetPosition, maxScroll));

    return this.scrollTo(containerId, targetPosition, smooth);
  }

  /**
   * 获取当前滚动位置
   */
  getCurrentPosition(containerId: string, selector?: string): number {
    const container = selector 
      ? document.querySelector(selector) as HTMLElement
      : this.findContainerById(containerId);

    return container ? container.scrollTop : 0;
  }

  /**
   * 注销容器
   */
  unregisterContainer(containerId: string): void {
    // 清理定时器
    const timeout = this.scrollTimeouts.get(containerId);
    if (timeout) {
      clearTimeout(timeout);
      this.scrollTimeouts.delete(containerId);
    }

    // 清理观察器
    const observer = this.observers.get(containerId);
    if (observer) {
      observer.disconnect();
      this.observers.delete(containerId);
    }

    // 清理状态
    this.scrollStates.delete(containerId);
  }

  /**
   * 根据ID查找容器（私有方法）
   */
  private findContainerById(containerId: string): HTMLElement | null {
    // 这里可以根据需要实现查找逻辑
    // 目前简单返回null，依赖selector参数
    return null;
  }

  /**
   * 清理所有状态
   */
  cleanup(): void {
    // 清理所有定时器
    this.scrollTimeouts.forEach(timeout => clearTimeout(timeout));
    this.scrollTimeouts.clear();

    // 清理所有观察器
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();

    // 清理状态
    this.scrollStates.clear();
  }
}

// ===== 便捷方法 =====

/**
 * 获取全局滚动管理器实例
 */
export const getGlobalScrollManager = () => GlobalScrollManager.getInstance();

/**
 * 词库滚动管理器
 * 专门用于词库面板的滚动管理
 */
export class WordLibraryScrollManager {
  private static readonly CONTAINER_ID = 'word-library-container';
  private static readonly SELECTOR = '.word-library-manager .overflow-y-auto';
  private static manager = getGlobalScrollManager();

  /**
   * 初始化词库滚动管理
   */
  static initialize(): void {
    this.manager.registerContainer(this.CONTAINER_ID, this.SELECTOR, {
      debounceDelay: 150,
      debug: false
    });
  }

  /**
   * 保存当前位置
   */
  static savePosition(): void {
    const container = document.querySelector(this.SELECTOR) as HTMLElement;
    if (container) {
      this.manager.savePosition(this.CONTAINER_ID, container.scrollTop);
    }
  }

  /**
   * 恢复位置
   */
  static restorePosition(): boolean {
    return this.manager.restorePosition(this.CONTAINER_ID, this.SELECTOR);
  }

  /**
   * 滚动到词库
   */
  static scrollToLibrary(libraryKey: string): boolean {
    const targetElement = document.querySelector(`[data-word-library="${libraryKey}"]`) as HTMLElement;
    if (!targetElement) {
      return false;
    }

    return this.manager.scrollToElement(
      this.CONTAINER_ID,
      targetElement,
      'center',
      true,
      this.SELECTOR
    );
  }

  /**
   * 清理
   */
  static cleanup(): void {
    this.manager.unregisterContainer(this.CONTAINER_ID);
  }
}

// ===== 导出类型 =====

export type {
  ScrollState,
  ScrollManagerOptions
};
