/**
 * 词库滑动服务
 * 🎯 核心价值：统一管理所有需要触发词库面板滑动的逻辑
 * 📦 功能范围：提供统一的滑动触发接口，支持任何组件调用
 * 🔄 架构设计：基于位置计算的滑动控制，简化滑动逻辑
 */

import type { WordLibraryKey } from '@/core/matrix/MatrixTypes';

// ===== 类型定义 =====

interface ScrollTriggerOptions {
  /** 滑动行为 */
  behavior?: ScrollBehavior;
  /** 对齐方式 */
  block?: ScrollLogicalPosition;
  /** 是否强制滑动（忽略位置检查） */
  force?: boolean;
  /** 是否启用调试日志 */
  debug?: boolean;
}

interface ScrollResult {
  /** 是否成功执行滑动 */
  success: boolean;
  /** 位置差值：0=不移动，负数=上移，正数=下移 */
  delta: number;
  /** 错误信息（如果有） */
  error?: string;
}

// ===== 滑动服务类 =====

export class WordLibraryScrollService {
  private static instance: WordLibraryScrollService | null = null;
  private currentActiveLibrary: WordLibraryKey | null = null;
  private scrollContainer: HTMLElement | null = null;

  private constructor() { }

  /**
   * 获取服务实例（单例模式）
   */
  static getInstance(): WordLibraryScrollService {
    if (!this.instance) {
      this.instance = new WordLibraryScrollService();
    }
    return this.instance;
  }

  /**
   * 初始化滚动容器
   * @param container 滚动容器元素
   */
  initializeContainer(container: HTMLElement): void {
    this.scrollContainer = container;
  }

  /**
   * 获取滚动容器
   */
  private getScrollContainer(): HTMLElement | null {
    if (this.scrollContainer) {
      return this.scrollContainer;
    }

    // 尝试多种选择器查找词库滚动容器
    const selectors = [
      '.word-library-manager .overflow-y-auto',
      '.word-library-manager [style*="overflow-y"]',
      '.word-library-manager .scrollable',
      '.overflow-y-auto',
      '[class*="word-library"] [class*="overflow"]'
    ];

    for (const selector of selectors) {
      const container = document.querySelector(selector) as HTMLElement;
      if (container) {
        this.scrollContainer = container;
        return container;
      }
    }

    return null;
  }

  /**
   * 查找目标词库元素
   * @param libraryKey 词库键
   */
  private findLibraryElement(libraryKey: WordLibraryKey): HTMLElement | null {
    const element = document.querySelector(`[data-word-library="${libraryKey}"]`) as HTMLElement;
    return element;
  }

  /**
   * 计算滑动位置差值
   * @param targetElement 目标元素
   * @param container 滚动容器
   * @param block 对齐方式
   */
  private calculateScrollDelta(
    targetElement: HTMLElement,
    container: HTMLElement,
    block: ScrollLogicalPosition = 'center'
  ): { current: number; target: number; delta: number } {
    // 获取当前滑动位置
    const current = container.scrollTop;

    // 获取目标元素位置信息
    const targetRect = targetElement.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();

    // 计算目标位置（相对于容器顶部）
    let target: number;

    switch (block) {
      case 'start':
        target = current + (targetRect.top - containerRect.top);
        break;
      case 'end':
        target = current + (targetRect.bottom - containerRect.bottom);
        break;
      case 'center':
      default:
        const targetCenter = targetRect.top + targetRect.height / 2;
        const containerCenter = containerRect.top + containerRect.height / 2;
        target = current + (targetCenter - containerCenter);
        break;
    }

    // 确保目标位置在有效范围内
    const maxScroll = container.scrollHeight - container.clientHeight;
    target = Math.max(0, Math.min(target, maxScroll));

    // 计算位置差值
    const delta = target - current;

    return { current, target, delta };
  }

  /**
   * 触发词库滑动
   * @param libraryKey 目标词库键
   * @param options 滑动选项
   */
  triggerScroll(libraryKey: WordLibraryKey, options: ScrollTriggerOptions = {}): ScrollResult {
    const {
      behavior = 'smooth',
      block = 'center',
      force = false,
      debug = false
    } = options;

    if (debug) {
      console.log('[WordLibraryScrollService] 开始滑动处理:', {
        libraryKey,
        currentActiveLibrary: this.currentActiveLibrary,
        force,
        options
      });
    }

    try {
      // 检查是否需要滑动
      if (!force && this.currentActiveLibrary === libraryKey) {
        if (debug) console.log('[WordLibraryScrollService] 跳过滑动：词库已激活');
        return { success: false, delta: 0 };
      }

      // 获取滚动容器
      const container = this.getScrollContainer();
      if (!container) {
        const error = '未找到滚动容器';
        if (debug) {
          console.warn('[WordLibraryScrollService]', error);
          console.log('[WordLibraryScrollService] 尝试查找的选择器:', '.word-library-manager .overflow-y-auto');
          console.log('[WordLibraryScrollService] 当前DOM中的词库管理器:', document.querySelectorAll('.word-library-manager'));
        }
        return { success: false, delta: 0, error };
      }

      if (debug) {
        console.log('[WordLibraryScrollService] 找到滚动容器:', container);
      }

      // 查找目标词库元素
      const targetElement = this.findLibraryElement(libraryKey);
      if (!targetElement) {
        const error = `未找到词库元素: ${libraryKey}`;
        if (debug) {
          console.warn('[WordLibraryScrollService]', error);
          console.log('[WordLibraryScrollService] 尝试查找的选择器:', `[data-word-library="${libraryKey}"]`);
          console.log('[WordLibraryScrollService] 当前DOM中的词库元素:', document.querySelectorAll('[data-word-library]'));
        }
        return { success: false, delta: 0, error };
      }

      if (debug) {
        console.log('[WordLibraryScrollService] 找到目标元素:', targetElement);
      }

      // 计算滑动位置
      const position = this.calculateScrollDelta(targetElement, container, block);

      if (debug) {
        console.log('[WordLibraryScrollService] 位置计算:', {
          libraryKey,
          current: position.current,
          target: position.target,
          delta: position.delta,
          action: position.delta === 0 ? '不移动' : position.delta < 0 ? '上移' : '下移'
        });
      }

      // 根据位置差值决定是否滑动
      if (position.delta === 0 && !force) {
        if (debug) console.log('[WordLibraryScrollService] 无需滑动：目标已在正确位置');
        this.currentActiveLibrary = libraryKey;
        return { success: false, delta: 0 };
      }

      // 执行滑动
      container.scrollTo({
        top: position.target,
        behavior
      });

      // 更新当前激活的词库
      this.currentActiveLibrary = libraryKey;

      if (debug) {
        const action = position.delta < 0 ? '上移' : '下移';
        console.log(`[WordLibraryScrollService] 执行${action}滑动:`, {
          from: position.current,
          to: position.target,
          delta: position.delta
        });
      }

      return { success: true, delta: position.delta };

    } catch (error) {
      const errorMessage = `滑动执行失败: ${error}`;
      if (debug) console.error('[WordLibraryScrollService]', errorMessage);
      return { success: false, delta: 0, error: errorMessage };
    }
  }

  /**
   * 重置滑动状态
   */
  resetScrollState(): void {
    this.currentActiveLibrary = null;
  }

  /**
   * 获取当前激活的词库
   */
  getCurrentActiveLibrary(): WordLibraryKey | null {
    return this.currentActiveLibrary;
  }

  /**
   * 设置当前激活的词库（不触发滑动）
   */
  setCurrentActiveLibrary(libraryKey: WordLibraryKey | null): void {
    this.currentActiveLibrary = libraryKey;
  }
}

// ===== 便捷方法 =====

/**
 * 获取词库滑动服务实例
 */
export const getWordLibraryScrollService = () => WordLibraryScrollService.getInstance();

/**
 * 触发词库滑动的便捷方法
 * @param libraryKey 目标词库键
 * @param options 滑动选项
 */
export const triggerWordLibraryScroll = (
  libraryKey: WordLibraryKey,
  options?: ScrollTriggerOptions
): ScrollResult => {
  return getWordLibraryScrollService().triggerScroll(libraryKey, options);
};

/**
 * 重置词库滑动状态的便捷方法
 */
export const resetWordLibraryScrollState = (): void => {
  getWordLibraryScrollService().resetScrollState();
};

// ===== 导出类型 =====

export type {
  ScrollResult, ScrollTriggerOptions
};

