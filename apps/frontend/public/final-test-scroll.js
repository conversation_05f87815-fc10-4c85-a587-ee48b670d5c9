/**
 * 最终滑动功能测试脚本
 * 验证修复后的双击激活填词模式滑动功能
 */

(function() {
  'use strict';
  
  console.log('%c🎯 最终滑动功能测试', 'color: #10b981; font-size: 18px; font-weight: bold');
  
  const finalTest = {
    // 检查修复状态
    checkFixStatus() {
      console.log('\n🔍 检查修复状态...');
      
      // 检查DOM结构
      const wordLibraryManager = document.querySelector('.word-library-manager');
      const scrollContainer = document.querySelector('.word-library-manager .overflow-y-auto');
      const wordLibraryItems = document.querySelectorAll('[data-word-library]');
      
      console.log('DOM检查结果:');
      console.log(`  词库管理器: ${wordLibraryManager ? '✅' : '❌'}`);
      console.log(`  滚动容器: ${scrollContainer ? '✅' : '❌'}`);
      console.log(`  词库项目: ${wordLibraryItems.length} 个`);
      
      // 检查滑动服务
      let serviceAvailable = false;
      try {
        if (window.triggerWordLibraryScroll) {
          serviceAvailable = true;
        }
      } catch (e) {
        // 服务不可用
      }
      
      console.log(`  滑动服务: ${serviceAvailable ? '✅' : '❌'}`);
      
      return {
        domReady: !!wordLibraryManager && !!scrollContainer && wordLibraryItems.length > 0,
        serviceReady: serviceAvailable
      };
    },
    
    // 模拟完整的双击流程
    simulateFullDoubleClickFlow(x = 0, y = 0) {
      console.log(`\n🖱️ 模拟完整双击流程: (${x}, ${y})`);
      
      // 1. 查找目标单元格
      const cell = document.querySelector(`[data-x="${x}"][data-y="${y}"]`);
      if (!cell) {
        console.error(`❌ 未找到单元格 (${x}, ${y})`);
        return false;
      }
      
      console.log('✅ 找到目标单元格');
      
      // 2. 获取单元格信息
      const color = cell.getAttribute('data-color') || 'red';
      const level = cell.getAttribute('data-level') || '1';
      const libraryKey = `${color}-${level}`;
      
      console.log(`📊 单元格信息: ${color} ${level}级 -> ${libraryKey}`);
      
      // 3. 检查对应的词库元素是否存在
      const libraryElement = document.querySelector(`[data-word-library="${libraryKey}"]`);
      if (!libraryElement) {
        console.error(`❌ 未找到对应的词库元素: ${libraryKey}`);
        return false;
      }
      
      console.log('✅ 找到对应的词库元素');
      
      // 4. 记录初始状态
      const container = document.querySelector('.word-library-manager .overflow-y-auto');
      const initialScrollTop = container ? container.scrollTop : 0;
      
      console.log(`📍 初始滚动位置: ${initialScrollTop}`);
      
      // 5. 触发双击事件
      const doubleClickEvent = new MouseEvent('dblclick', {
        bubbles: true,
        cancelable: true,
        view: window
      });
      
      cell.dispatchEvent(doubleClickEvent);
      console.log('✅ 双击事件已触发');
      
      // 6. 等待并检查结果
      setTimeout(() => {
        this.checkDoubleClickResult(libraryKey, initialScrollTop);
      }, 1000);
      
      return true;
    },
    
    // 检查双击结果
    checkDoubleClickResult(libraryKey, initialScrollTop) {
      console.log(`\n🔍 检查双击结果: ${libraryKey}`);
      
      const container = document.querySelector('.word-library-manager .overflow-y-auto');
      const libraryElement = document.querySelector(`[data-word-library="${libraryKey}"]`);
      
      if (!container || !libraryElement) {
        console.error('❌ 检查失败: 缺少必要元素');
        return;
      }
      
      // 检查滚动位置变化
      const finalScrollTop = container.scrollTop;
      const scrollChanged = Math.abs(finalScrollTop - initialScrollTop) > 5;
      
      console.log(`📍 最终滚动位置: ${finalScrollTop} (变化: ${scrollChanged ? '是' : '否'})`);
      
      // 检查词库激活状态
      const isActive = libraryElement.classList.contains('word-library-active') ||
                      libraryElement.classList.contains('active') ||
                      libraryElement.querySelector('.word-library-active');
      
      console.log(`🎯 词库激活状态: ${isActive ? '已激活' : '未激活'}`);
      
      // 检查元素可见性
      const containerRect = container.getBoundingClientRect();
      const elementRect = libraryElement.getBoundingClientRect();
      
      const isVisible = elementRect.top >= containerRect.top && 
                       elementRect.bottom <= containerRect.bottom;
      
      const isInCenter = Math.abs(
        (elementRect.top + elementRect.height / 2) - 
        (containerRect.top + containerRect.height / 2)
      ) < 50;
      
      console.log(`👁️ 元素可见性: ${isVisible ? '可见' : '不可见'}`);
      console.log(`🎯 居中对齐: ${isInCenter ? '已居中' : '未居中'}`);
      
      // 综合评估
      const success = (scrollChanged || isVisible || isInCenter) && isActive;
      
      console.log(`\n📊 测试结果: ${success ? '✅ 成功' : '❌ 失败'}`);
      
      if (!success) {
        console.log('问题分析:');
        if (!scrollChanged && !isVisible && !isInCenter) {
          console.log('  - 滑动未执行或元素不在正确位置');
        }
        if (!isActive) {
          console.log('  - 词库未激活');
        }
      }
      
      return success;
    },
    
    // 运行批量测试
    runBatchTest() {
      console.log('\n🚀 开始批量测试...');
      
      const testCases = [
        { x: 0, y: 0, expected: 'red-1' },
        { x: 1, y: 1, expected: 'blue-2' },
        { x: 2, y: 2, expected: 'green-3' },
        { x: 3, y: 3, expected: 'yellow-4' }
      ];
      
      let currentIndex = 0;
      
      const runNextTest = () => {
        if (currentIndex >= testCases.length) {
          console.log('\n🎉 批量测试完成');
          return;
        }
        
        const testCase = testCases[currentIndex];
        console.log(`\n--- 测试 ${currentIndex + 1}/${testCases.length}: (${testCase.x}, ${testCase.y}) ---`);
        
        this.simulateFullDoubleClickFlow(testCase.x, testCase.y);
        
        currentIndex++;
        setTimeout(runNextTest, 3000); // 3秒间隔
      };
      
      runNextTest();
    },
    
    // 手动触发滑动测试
    manualScrollTest(libraryKey = 'red-1') {
      console.log(`\n🔧 手动滑动测试: ${libraryKey}`);
      
      const container = document.querySelector('.word-library-manager .overflow-y-auto');
      const target = document.querySelector(`[data-word-library="${libraryKey}"]`);
      
      if (!container || !target) {
        console.error('❌ 测试失败: 未找到必要元素');
        return false;
      }
      
      // 计算滑动位置
      const containerRect = container.getBoundingClientRect();
      const targetRect = target.getBoundingClientRect();
      
      const current = container.scrollTop;
      const targetCenter = targetRect.top + targetRect.height / 2;
      const containerCenter = containerRect.top + containerRect.height / 2;
      const targetPosition = current + (targetCenter - containerCenter);
      const delta = targetPosition - current;
      
      console.log('位置计算:', {
        current,
        target: targetPosition,
        delta,
        action: delta === 0 ? '不移动' : delta < 0 ? '上移' : '下移'
      });
      
      if (Math.abs(delta) < 5) {
        console.log('ℹ️ 目标已在正确位置');
        return true;
      }
      
      // 执行滑动
      container.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });
      
      console.log(`✅ 滑动已执行: ${delta < 0 ? '上移' : '下移'} ${Math.abs(delta)}px`);
      
      // 高亮目标
      target.style.border = '3px solid #ef4444';
      target.style.backgroundColor = 'rgba(239, 68, 68, 0.1)';
      
      setTimeout(() => {
        target.style.border = '';
        target.style.backgroundColor = '';
      }, 2000);
      
      return true;
    }
  };
  
  // 暴露到全局
  window.finalTestScroll = finalTest;
  
  // 自动检查状态
  setTimeout(() => {
    const status = finalTest.checkFixStatus();
    if (status.domReady) {
      console.log('\n🎯 系统就绪，可以开始测试');
      console.log('使用方法:');
      console.log('  finalTestScroll.simulateFullDoubleClickFlow(0, 0) - 模拟双击');
      console.log('  finalTestScroll.runBatchTest() - 批量测试');
      console.log('  finalTestScroll.manualScrollTest("red-1") - 手动滑动测试');
    } else {
      console.log('\n⚠️ 系统未就绪，请检查页面状态');
    }
  }, 1000);
  
})();
