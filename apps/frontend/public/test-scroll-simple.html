<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单滑动测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
        }
        
        .word-library-manager {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            height: 300px;
            overflow: hidden;
        }
        
        .overflow-y-auto {
            height: 100%;
            overflow-y: auto;
            padding: 10px;
        }
        
        .library-item {
            padding: 10px;
            margin-bottom: 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: #f9fafb;
            transition: all 0.2s ease;
        }
        
        .library-item.word-library-active {
            border-width: 3px;
            border-color: #4b5563;
            background: rgba(75, 85, 99, 0.05);
            box-shadow: 0 0 0 1px #4b5563;
        }
        
        .library-title {
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .library-words {
            font-size: 12px;
            color: #6b7280;
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: #2563eb;
            color: white;
        }
        
        .btn-primary:hover {
            background: #1d4ed8;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .log-area {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .status {
            background: #f3f4f6;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>词库滑动功能简单测试</h1>
        
        <div class="test-section">
            <h3>测试控制</h3>
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="testScroll('red-1')">测试红色1级</button>
                <button class="btn btn-primary" onclick="testScroll('blue-2')">测试蓝色2级</button>
                <button class="btn btn-primary" onclick="testScroll('green-3')">测试绿色3级</button>
                <button class="btn btn-primary" onclick="testScroll('yellow-4')">测试黄色4级</button>
                <button class="btn btn-secondary" onclick="clearActive()">清除激活</button>
                <button class="btn btn-secondary" onclick="clearLog()">清空日志</button>
            </div>
            
            <div class="status" id="status">
                状态: 等待测试
            </div>
        </div>
        
        <div class="test-section">
            <h3>词库管理面板</h3>
            <div class="word-library-manager">
                <div class="overflow-y-auto" id="scrollContainer">
                    <!-- 词库项目 -->
                    <div class="library-item" data-word-library="red-1">
                        <div class="library-title" style="color: #dc2626;">红色1级词库</div>
                        <div class="library-words">苹果, 草莓, 樱桃, 玫瑰</div>
                    </div>
                    
                    <div class="library-item" data-word-library="red-2">
                        <div class="library-title" style="color: #dc2626;">红色2级词库</div>
                        <div class="library-words">血液, 火焰, 热情, 危险</div>
                    </div>
                    
                    <div class="library-item" data-word-library="blue-1">
                        <div class="library-title" style="color: #2563eb;">蓝色1级词库</div>
                        <div class="library-words">天空, 海洋, 蓝莓, 宁静</div>
                    </div>
                    
                    <div class="library-item" data-word-library="blue-2">
                        <div class="library-title" style="color: #2563eb;">蓝色2级词库</div>
                        <div class="library-words">深海, 蓝宝石, 忧郁, 智慧</div>
                    </div>
                    
                    <div class="library-item" data-word-library="green-1">
                        <div class="library-title" style="color: #16a34a;">绿色1级词库</div>
                        <div class="library-words">草地, 树叶, 翡翠, 生命</div>
                    </div>
                    
                    <div class="library-item" data-word-library="green-2">
                        <div class="library-title" style="color: #16a34a;">绿色2级词库</div>
                        <div class="library-words">森林, 生机, 希望, 自然</div>
                    </div>
                    
                    <div class="library-item" data-word-library="green-3">
                        <div class="library-title" style="color: #16a34a;">绿色3级词库</div>
                        <div class="library-words">环保, 和谐, 平衡, 成长</div>
                    </div>
                    
                    <div class="library-item" data-word-library="yellow-1">
                        <div class="library-title" style="color: #d97706;">黄色1级词库</div>
                        <div class="library-words">太阳, 香蕉, 柠檬, 金子</div>
                    </div>
                    
                    <div class="library-item" data-word-library="yellow-2">
                        <div class="library-title" style="color: #d97706;">黄色2级词库</div>
                        <div class="library-words">麦田, 温暖, 光明, 活力</div>
                    </div>
                    
                    <div class="library-item" data-word-library="yellow-3">
                        <div class="library-title" style="color: #d97706;">黄色3级词库</div>
                        <div class="library-words">智慧, 创新, 启发, 突破</div>
                    </div>
                    
                    <div class="library-item" data-word-library="yellow-4">
                        <div class="library-title" style="color: #d97706;">黄色4级词库</div>
                        <div class="library-words">辉煌, 荣耀, 成功, 卓越</div>
                    </div>
                    
                    <div class="library-item" data-word-library="purple-1">
                        <div class="library-title" style="color: #9333ea;">紫色1级词库</div>
                        <div class="library-words">薰衣草, 葡萄, 紫罗兰, 神秘</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>测试日志</h3>
            <div class="log-area" id="logArea"></div>
        </div>
    </div>

    <script>
        let currentActive = null;
        
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logArea.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function updateStatus(message) {
            document.getElementById('status').textContent = `状态: ${message}`;
        }
        
        function testScroll(libraryKey) {
            log(`开始测试滑动到: ${libraryKey}`);
            updateStatus(`测试滑动到 ${libraryKey}`);
            
            const container = document.getElementById('scrollContainer');
            const target = document.querySelector(`[data-word-library="${libraryKey}"]`);
            
            if (!container || !target) {
                log('测试失败: 未找到容器或目标元素', 'error');
                updateStatus('测试失败');
                return;
            }
            
            // 清除之前的激活状态
            clearActive();
            
            // 计算滑动位置
            const containerRect = container.getBoundingClientRect();
            const targetRect = target.getBoundingClientRect();
            
            const current = container.scrollTop;
            const targetCenter = targetRect.top + targetRect.height / 2;
            const containerCenter = containerRect.top + containerRect.height / 2;
            const targetPosition = current + (targetCenter - containerCenter);
            const delta = targetPosition - current;
            
            log(`位置计算: current=${current}, target=${targetPosition}, delta=${delta}`);
            
            if (Math.abs(delta) < 5) {
                log('目标已在正确位置，无需滑动');
                activateLibrary(libraryKey);
                updateStatus('测试完成 - 无需滑动');
                return;
            }
            
            // 执行滑动
            const action = delta < 0 ? '上移' : '下移';
            log(`执行${action}滑动: ${Math.abs(delta)}px`);
            
            container.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
            
            // 激活目标词库
            setTimeout(() => {
                activateLibrary(libraryKey);
                
                // 验证滑动结果
                setTimeout(() => {
                    const finalPosition = container.scrollTop;
                    const success = Math.abs(finalPosition - targetPosition) < 20;
                    
                    log(`滑动结果: 最终位置=${finalPosition}, 成功=${success}`, success ? 'success' : 'error');
                    updateStatus(success ? '测试成功' : '测试失败');
                }, 500);
            }, 100);
        }
        
        function activateLibrary(libraryKey) {
            // 清除之前的激活状态
            document.querySelectorAll('.library-item').forEach(item => {
                item.classList.remove('word-library-active');
            });
            
            // 激活目标词库
            const target = document.querySelector(`[data-word-library="${libraryKey}"]`);
            if (target) {
                target.classList.add('word-library-active');
                currentActive = libraryKey;
                log(`词库已激活: ${libraryKey}`, 'success');
            }
        }
        
        function clearActive() {
            document.querySelectorAll('.library-item').forEach(item => {
                item.classList.remove('word-library-active');
            });
            currentActive = null;
            log('已清除所有激活状态');
            updateStatus('已清除激活状态');
        }
        
        function clearLog() {
            document.getElementById('logArea').textContent = '';
            log('测试日志已清空');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('简单滑动测试页面已加载');
            log('点击上方按钮测试不同词库的滑动功能');
            updateStatus('准备就绪');
        });
    </script>
</body>
</html>
