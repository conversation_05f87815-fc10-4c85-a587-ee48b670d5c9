/**
 * 深度滚动修复测试脚本
 * 测试彻底重构后的滚动位置保持功能
 */

(function() {
  'use strict';
  
  console.log('%c🔥 深度滚动修复测试', 'color: #dc2626; font-size: 20px; font-weight: bold');
  console.log('测试彻底重构后的滚动位置保持功能');
  
  const testDeepScrollFix = {
    // 测试状态
    testState: {
      testResults: [],
      renderCount: 0,
      scrollEvents: []
    },
    
    // 监听组件重新渲染
    monitorReRenders() {
      console.log('\n👀 开始监听组件重新渲染...');
      
      // 监听DOM变化
      const wordLibraryManager = document.querySelector('.word-library-manager');
      if (!wordLibraryManager) {
        console.error('❌ 未找到词库管理器');
        return null;
      }
      
      let renderCount = 0;
      
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            renderCount++;
            this.testState.renderCount = renderCount;
            console.log(`🔄 检测到重新渲染 #${renderCount}:`, {
              target: mutation.target,
              addedNodes: mutation.addedNodes.length,
              timestamp: Date.now()
            });
          }
        });
      });
      
      observer.observe(wordLibraryManager, {
        childList: true,
        subtree: true
      });
      
      console.log('✅ 重新渲染监听已启动');
      
      return () => {
        observer.disconnect();
        console.log(`⏹️ 重新渲染监听已停止 (共检测到 ${renderCount} 次重新渲染)`);
      };
    },
    
    // 监听滚动事件
    monitorScrollEvents() {
      console.log('\n📜 开始监听滚动事件...');
      
      const scrollContainer = document.querySelector('.word-library-manager .overflow-y-auto');
      if (!scrollContainer) {
        console.error('❌ 未找到滚动容器');
        return null;
      }
      
      let scrollEventCount = 0;
      
      const handleScroll = (event) => {
        scrollEventCount++;
        const scrollData = {
          count: scrollEventCount,
          scrollTop: event.target.scrollTop,
          timestamp: Date.now()
        };
        
        this.testState.scrollEvents.push(scrollData);
        console.log(`📜 滚动事件 #${scrollEventCount}: scrollTop = ${scrollData.scrollTop}`);
      };
      
      scrollContainer.addEventListener('scroll', handleScroll);
      
      console.log('✅ 滚动事件监听已启动');
      
      return () => {
        scrollContainer.removeEventListener('scroll', handleScroll);
        console.log(`⏹️ 滚动事件监听已停止 (共监听到 ${scrollEventCount} 次滚动)`);
      };
    },
    
    // 测试填词模式下的单击行为
    testWordInputModeClicks() {
      console.log('\n📝 测试填词模式下的单击行为...');
      
      // 首先激活填词模式
      const targetCell = document.querySelector('[data-x="1"][data-y="1"]');
      if (!targetCell) {
        console.error('❌ 未找到测试单元格');
        return false;
      }
      
      // 设置测试滚动位置
      const scrollContainer = document.querySelector('.word-library-manager .overflow-y-auto');
      if (!scrollContainer) {
        console.error('❌ 未找到滚动容器');
        return false;
      }
      
      const testScrollPosition = 200;
      scrollContainer.scrollTop = testScrollPosition;
      console.log(`📍 设置测试滚动位置: ${testScrollPosition}px`);
      
      // 双击激活填词模式
      const doubleClickEvent = new MouseEvent('dblclick', {
        bubbles: true,
        cancelable: true,
        view: window
      });
      
      targetCell.dispatchEvent(doubleClickEvent);
      console.log('✅ 双击激活填词模式');
      
      // 等待填词模式激活后测试单击
      setTimeout(() => {
        console.log('\n🖱️ 开始测试填词模式下的单击...');
        
        // 记录激活后的滚动位置
        const afterActivation = scrollContainer.scrollTop;
        console.log(`填词模式激活后滚动位置: ${afterActivation}px`);
        
        // 测试多次单击
        const clickTests = [
          { x: 1, y: 1, description: '点击同一单元格' },
          { x: 2, y: 2, description: '点击不同单元格' },
          { x: 0, y: 0, description: '点击另一个单元格' },
          { x: 3, y: 3, description: '点击第四个单元格' }
        ];
        
        let clickIndex = 0;
        
        const performNextClick = () => {
          if (clickIndex >= clickTests.length) {
            setTimeout(() => this.analyzeClickResults(), 1000);
            return;
          }
          
          const test = clickTests[clickIndex];
          console.log(`\n--- 点击测试 ${clickIndex + 1}/${clickTests.length}: ${test.description} (${test.x}, ${test.y}) ---`);
          
          const beforeClick = scrollContainer.scrollTop;
          console.log(`点击前位置: ${beforeClick}px`);
          
          // 执行单击
          const cell = document.querySelector(`[data-x="${test.x}"][data-y="${test.y}"]`);
          if (cell) {
            const clickEvent = new MouseEvent('click', {
              bubbles: true,
              cancelable: true,
              view: window
            });
            
            cell.dispatchEvent(clickEvent);
            console.log('✅ 单击事件已触发');
            
            // 检查点击后的位置
            setTimeout(() => {
              const afterClick = scrollContainer.scrollTop;
              const delta = Math.abs(afterClick - beforeClick);
              
              console.log(`点击后位置: ${afterClick}px (变化: ${delta}px)`);
              
              const testResult = {
                test: test.description,
                position: { x: test.x, y: test.y },
                beforeClick,
                afterClick,
                delta,
                isStable: delta < 10, // 小于10px认为是稳定的
                timestamp: Date.now()
              };
              
              this.testState.testResults.push(testResult);
              
              if (testResult.isStable) {
                console.log('✅ 滚动位置稳定');
              } else {
                console.log(`❌ 滚动位置不稳定，变化了 ${delta}px`);
              }
              
              clickIndex++;
              setTimeout(performNextClick, 500);
            }, 200);
          } else {
            console.error(`❌ 未找到单元格 (${test.x}, ${test.y})`);
            clickIndex++;
            setTimeout(performNextClick, 500);
          }
        };
        
        performNextClick();
      }, 1000);
      
      return true;
    },
    
    // 分析点击测试结果
    analyzeClickResults() {
      console.log('\n📊 点击测试结果分析');
      console.log('='.repeat(60));
      
      const results = this.testState.testResults;
      if (results.length === 0) {
        console.log('📝 暂无测试数据');
        return;
      }
      
      const stableCount = results.filter(r => r.isStable).length;
      const unstableCount = results.length - stableCount;
      
      console.log(`总测试次数: ${results.length}`);
      console.log(`滚动稳定: ${stableCount} 次`);
      console.log(`滚动不稳定: ${unstableCount} 次`);
      console.log(`稳定率: ${((stableCount / results.length) * 100).toFixed(1)}%`);
      console.log(`组件重新渲染次数: ${this.testState.renderCount}`);
      console.log(`滚动事件次数: ${this.testState.scrollEvents.length}`);
      
      if (unstableCount === 0) {
        console.log('🎉 所有测试通过！滚动位置完全稳定');
      } else {
        console.log('⚠️ 仍有滚动不稳定问题');
        
        console.log('\n不稳定的测试:');
        results.filter(r => !r.isStable).forEach((result, index) => {
          console.log(`${index + 1}. ${result.test}: ${result.beforeClick} → ${result.afterClick} (Δ${result.delta}px)`);
        });
      }
      
      // 分析滚动事件模式
      if (this.testState.scrollEvents.length > 0) {
        console.log('\n滚动事件分析:');
        const scrollPositions = this.testState.scrollEvents.map(e => e.scrollTop);
        const uniquePositions = [...new Set(scrollPositions)];
        console.log(`不同的滚动位置: ${uniquePositions.length} 个`);
        console.log(`位置范围: ${Math.min(...scrollPositions)} - ${Math.max(...scrollPositions)}px`);
      }
    },
    
    // 测试全局滚动管理器
    testGlobalScrollManager() {
      console.log('\n🌐 测试全局滚动管理器...');
      
      // 检查是否已初始化
      try {
        const { WordLibraryScrollManager } = require('@/core/services/GlobalScrollManager');
        
        // 测试保存和恢复位置
        const scrollContainer = document.querySelector('.word-library-manager .overflow-y-auto');
        if (scrollContainer) {
          const testPosition = 150;
          scrollContainer.scrollTop = testPosition;
          
          console.log(`设置测试位置: ${testPosition}px`);
          
          // 保存位置
          WordLibraryScrollManager.savePosition();
          console.log('✅ 位置已保存');
          
          // 改变位置
          scrollContainer.scrollTop = 0;
          console.log('位置已重置为 0px');
          
          // 恢复位置
          setTimeout(() => {
            const restored = WordLibraryScrollManager.restorePosition();
            const currentPosition = scrollContainer.scrollTop;
            
            console.log(`恢复结果: ${restored ? '成功' : '失败'}`);
            console.log(`当前位置: ${currentPosition}px`);
            
            if (Math.abs(currentPosition - testPosition) < 5) {
              console.log('✅ 全局滚动管理器工作正常');
            } else {
              console.log('❌ 全局滚动管理器工作异常');
            }
          }, 100);
        }
      } catch (error) {
        console.error('❌ 全局滚动管理器测试失败:', error);
      }
    },
    
    // 运行完整测试套件
    runFullTest() {
      console.log('\n🚀 开始完整测试套件...');
      
      // 重置测试状态
      this.testState = {
        testResults: [],
        renderCount: 0,
        scrollEvents: []
      };
      
      // 启动监听
      const stopRenderMonitor = this.monitorReRenders();
      const stopScrollMonitor = this.monitorScrollEvents();
      
      // 测试全局滚动管理器
      this.testGlobalScrollManager();
      
      // 延迟开始主要测试
      setTimeout(() => {
        this.testWordInputModeClicks();
        
        // 10秒后停止监听并显示最终结果
        setTimeout(() => {
          if (stopRenderMonitor) stopRenderMonitor();
          if (stopScrollMonitor) stopScrollMonitor();
          
          console.log('\n🏁 完整测试套件结束');
          this.showFinalSummary();
        }, 10000);
      }, 2000);
    },
    
    // 显示最终总结
    showFinalSummary() {
      console.log('\n📋 最终测试总结');
      console.log('='.repeat(60));
      
      const { testResults, renderCount, scrollEvents } = this.testState;
      
      console.log(`📊 测试统计:`);
      console.log(`  - 点击测试次数: ${testResults.length}`);
      console.log(`  - 组件重新渲染次数: ${renderCount}`);
      console.log(`  - 滚动事件次数: ${scrollEvents.length}`);
      
      if (testResults.length > 0) {
        const stableCount = testResults.filter(r => r.isStable).length;
        const stabilityRate = ((stableCount / testResults.length) * 100).toFixed(1);
        console.log(`  - 滚动稳定率: ${stabilityRate}%`);
        
        if (stabilityRate === '100.0') {
          console.log('🎉 修复成功！滚动位置完全稳定');
        } else {
          console.log('⚠️ 修复不完全，仍需进一步优化');
        }
      }
      
      console.log('\n💡 建议:');
      if (renderCount > testResults.length) {
        console.log('  - 组件重新渲染次数过多，需要进一步优化状态管理');
      }
      if (scrollEvents.length > testResults.length * 2) {
        console.log('  - 滚动事件过于频繁，可能需要增加防抖');
      }
      if (testResults.some(r => !r.isStable)) {
        console.log('  - 仍有滚动不稳定问题，需要检查状态依赖');
      }
    }
  };
  
  // 暴露到全局
  window.testDeepScrollFix = testDeepScrollFix;
  
  // 显示使用说明
  console.log('\n📖 使用说明:');
  console.log('testDeepScrollFix.runFullTest() - 运行完整测试套件');
  console.log('testDeepScrollFix.testWordInputModeClicks() - 测试填词模式点击');
  console.log('testDeepScrollFix.testGlobalScrollManager() - 测试全局滚动管理器');
  console.log('testDeepScrollFix.monitorReRenders() - 监听组件重新渲染');
  
  console.log('\n🚀 快速开始: testDeepScrollFix.runFullTest()');
  
})();
