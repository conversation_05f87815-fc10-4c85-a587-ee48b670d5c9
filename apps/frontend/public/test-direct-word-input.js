/**
 * 直接填词输入功能测试脚本
 * 测试新的DirectWordInputService是否正常工作
 */

(function() {
  'use strict';
  
  console.log('%c🎯 直接填词输入功能测试', 'color: #10b981; font-size: 18px; font-weight: bold');
  
  const testDirectWordInput = {
    // 检查服务状态
    checkServiceStatus() {
      console.log('\n🔍 检查DirectWordInputService状态...');
      
      // 检查是否已初始化
      const hasGlobalHandler = document.addEventListener.toString().includes('dblclick');
      console.log(`全局双击监听器: ${hasGlobalHandler ? '✅ 已设置' : '❌ 未设置'}`);
      
      // 检查DOM结构
      const matrixCells = document.querySelectorAll('[data-x][data-y]');
      console.log(`矩阵单元格数量: ${matrixCells.length}`);
      
      if (matrixCells.length > 0) {
        const firstCell = matrixCells[0];
        const hasColorData = firstCell.hasAttribute('data-color');
        const hasLevelData = firstCell.hasAttribute('data-level');
        
        console.log(`单元格data-color属性: ${hasColorData ? '✅ 存在' : '❌ 缺失'}`);
        console.log(`单元格data-level属性: ${hasLevelData ? '✅ 存在' : '❌ 缺失'}`);
        
        if (hasColorData && hasLevelData) {
          console.log(`示例单元格数据:`, {
            x: firstCell.getAttribute('data-x'),
            y: firstCell.getAttribute('data-y'),
            color: firstCell.getAttribute('data-color'),
            level: firstCell.getAttribute('data-level')
          });
        }
      }
      
      // 检查词库面板
      const wordLibraryManager = document.querySelector('.word-library-manager');
      const scrollContainer = document.querySelector('.word-library-manager .overflow-y-auto');
      const wordLibraryItems = document.querySelectorAll('[data-word-library]');
      
      console.log(`词库管理器: ${wordLibraryManager ? '✅ 存在' : '❌ 缺失'}`);
      console.log(`滚动容器: ${scrollContainer ? '✅ 存在' : '❌ 缺失'}`);
      console.log(`词库项目数量: ${wordLibraryItems.length}`);
      
      return {
        cellsReady: matrixCells.length > 0,
        dataAttributesReady: matrixCells.length > 0 && 
                            matrixCells[0].hasAttribute('data-color') && 
                            matrixCells[0].hasAttribute('data-level'),
        libraryPanelReady: !!wordLibraryManager && !!scrollContainer && wordLibraryItems.length > 0
      };
    },
    
    // 模拟双击事件
    simulateDoubleClick(x = 0, y = 0) {
      console.log(`\n🖱️ 模拟双击单元格 (${x}, ${y})`);
      
      const cell = document.querySelector(`[data-x="${x}"][data-y="${y}"]`);
      if (!cell) {
        console.error(`❌ 未找到单元格 (${x}, ${y})`);
        return false;
      }
      
      console.log('✅ 找到目标单元格:', {
        x: cell.getAttribute('data-x'),
        y: cell.getAttribute('data-y'),
        color: cell.getAttribute('data-color'),
        level: cell.getAttribute('data-level')
      });
      
      // 创建双击事件
      const doubleClickEvent = new MouseEvent('dblclick', {
        bubbles: true,
        cancelable: true,
        view: window,
        detail: 2
      });
      
      // 记录事件触发前的状态
      const beforeState = this.captureState();
      console.log('触发前状态:', beforeState);
      
      // 触发双击事件
      cell.dispatchEvent(doubleClickEvent);
      console.log('✅ 双击事件已触发');
      
      // 延迟检查结果
      setTimeout(() => {
        const afterState = this.captureState();
        console.log('触发后状态:', afterState);
        
        const success = this.compareStates(beforeState, afterState);
        console.log(`📊 双击测试结果: ${success ? '✅ 成功' : '❌ 失败'}`);
      }, 1000);
      
      return true;
    },
    
    // 捕获当前状态
    captureState() {
      try {
        // 获取填词状态
        const wordInputActive = document.querySelector('.word-input-active') !== null;
        const activeLibrary = document.querySelector('.word-library-active, [data-word-library].active');
        const activeLibraryKey = activeLibrary ? activeLibrary.getAttribute('data-word-library') : null;
        
        // 获取滚动位置
        const scrollContainer = document.querySelector('.word-library-manager .overflow-y-auto');
        const scrollTop = scrollContainer ? scrollContainer.scrollTop : 0;
        
        return {
          wordInputActive,
          activeLibraryKey,
          scrollTop,
          timestamp: Date.now()
        };
      } catch (error) {
        console.error('捕获状态失败:', error);
        return { error: String(error) };
      }
    },
    
    // 比较状态变化
    compareStates(before, after) {
      if (before.error || after.error) {
        return false;
      }
      
      const changes = {
        wordInputActivated: !before.wordInputActive && after.wordInputActive,
        libraryActivated: before.activeLibraryKey !== after.activeLibraryKey,
        scrollChanged: Math.abs(after.scrollTop - before.scrollTop) > 5
      };
      
      console.log('状态变化:', changes);
      
      // 成功的标准：填词模式激活 或 词库激活 或 滚动发生
      return changes.wordInputActivated || changes.libraryActivated || changes.scrollChanged;
    },
    
    // 批量测试
    runBatchTest() {
      console.log('\n🚀 开始批量测试...');
      
      const status = this.checkServiceStatus();
      if (!status.cellsReady || !status.dataAttributesReady) {
        console.error('❌ 系统未就绪，无法进行测试');
        return;
      }
      
      const testCases = [
        { x: 0, y: 0 },
        { x: 1, y: 1 },
        { x: 2, y: 2 },
        { x: 3, y: 3 }
      ];
      
      let currentIndex = 0;
      
      const runNextTest = () => {
        if (currentIndex >= testCases.length) {
          console.log('\n🎉 批量测试完成');
          return;
        }
        
        const testCase = testCases[currentIndex];
        console.log(`\n--- 测试 ${currentIndex + 1}/${testCases.length}: (${testCase.x}, ${testCase.y}) ---`);
        
        this.simulateDoubleClick(testCase.x, testCase.y);
        
        currentIndex++;
        setTimeout(runNextTest, 3000);
      };
      
      runNextTest();
    },
    
    // 监听双击事件
    monitorDoubleClicks() {
      console.log('\n👀 开始监听双击事件...');
      
      let eventCount = 0;
      
      const handler = (event) => {
        eventCount++;
        const target = event.target;
        const x = target.getAttribute('data-x');
        const y = target.getAttribute('data-y');
        
        if (x !== null && y !== null) {
          console.log(`🖱️ 检测到双击 #${eventCount}: (${x}, ${y})`, {
            target,
            color: target.getAttribute('data-color'),
            level: target.getAttribute('data-level'),
            timestamp: Date.now()
          });
        }
      };
      
      document.addEventListener('dblclick', handler, true);
      
      console.log('✅ 双击监听已启动');
      
      // 返回停止监听的函数
      return () => {
        document.removeEventListener('dblclick', handler, true);
        console.log(`⏹️ 双击监听已停止 (共检测到 ${eventCount} 次双击)`);
      };
    },
    
    // 手动触发滑动测试
    testScrollOnly(libraryKey = 'red-1') {
      console.log(`\n📜 测试滑动功能: ${libraryKey}`);
      
      const container = document.querySelector('.word-library-manager .overflow-y-auto');
      const target = document.querySelector(`[data-word-library="${libraryKey}"]`);
      
      if (!container || !target) {
        console.error('❌ 滑动测试失败: 未找到必要元素');
        return false;
      }
      
      // 计算滑动位置
      const containerRect = container.getBoundingClientRect();
      const targetRect = target.getBoundingClientRect();
      
      const current = container.scrollTop;
      const targetCenter = targetRect.top + targetRect.height / 2;
      const containerCenter = containerRect.top + containerRect.height / 2;
      const targetPosition = current + (targetCenter - containerCenter);
      const delta = targetPosition - current;
      
      console.log('滑动计算:', {
        current,
        target: targetPosition,
        delta,
        action: delta === 0 ? '不移动' : delta < 0 ? '上移' : '下移'
      });
      
      if (Math.abs(delta) < 5) {
        console.log('ℹ️ 目标已在正确位置');
        return true;
      }
      
      // 执行滑动
      container.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });
      
      console.log(`✅ 滑动已执行: ${delta < 0 ? '上移' : '下移'} ${Math.abs(delta)}px`);
      
      // 高亮目标
      target.style.border = '3px solid #ef4444';
      target.style.backgroundColor = 'rgba(239, 68, 68, 0.1)';
      
      setTimeout(() => {
        target.style.border = '';
        target.style.backgroundColor = '';
      }, 2000);
      
      return true;
    }
  };
  
  // 暴露到全局
  window.testDirectWordInput = testDirectWordInput;
  
  // 自动检查状态
  setTimeout(() => {
    const status = testDirectWordInput.checkServiceStatus();
    
    if (status.cellsReady && status.dataAttributesReady && status.libraryPanelReady) {
      console.log('\n🎯 系统完全就绪，可以开始测试');
      console.log('使用方法:');
      console.log('  testDirectWordInput.simulateDoubleClick(0, 0) - 模拟双击');
      console.log('  testDirectWordInput.runBatchTest() - 批量测试');
      console.log('  testDirectWordInput.monitorDoubleClicks() - 监听双击');
      console.log('  testDirectWordInput.testScrollOnly("red-1") - 测试滑动');
    } else {
      console.log('\n⚠️ 系统未完全就绪:');
      if (!status.cellsReady) console.log('  - 矩阵单元格未就绪');
      if (!status.dataAttributesReady) console.log('  - 单元格数据属性缺失');
      if (!status.libraryPanelReady) console.log('  - 词库面板未就绪');
    }
  }, 1000);
  
})();
