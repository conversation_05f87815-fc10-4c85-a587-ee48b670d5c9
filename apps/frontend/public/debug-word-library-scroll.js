/**
 * 词库滑动功能调试脚本
 * 在浏览器控制台中运行此脚本来诊断和修复滑动问题
 */

(function() {
  'use strict';
  
  console.log('%c🔧 词库滑动功能调试工具', 'color: #2563eb; font-size: 16px; font-weight: bold');
  
  // 调试工具对象
  const debugTool = {
    // 检查DOM结构
    checkDOM() {
      console.log('\n📋 检查DOM结构...');
      
      const results = {
        wordLibraryManager: document.querySelector('.word-library-manager'),
        scrollContainer: document.querySelector('.word-library-manager .overflow-y-auto'),
        wordLibraryItems: document.querySelectorAll('[data-word-library]'),
        matrixCells: document.querySelectorAll('[data-x][data-y]')
      };
      
      console.log('词库管理器:', results.wordLibraryManager ? '✅ 找到' : '❌ 未找到');
      console.log('滚动容器:', results.scrollContainer ? '✅ 找到' : '❌ 未找到');
      console.log('词库项目:', `${results.wordLibraryItems.length} 个`);
      console.log('矩阵单元格:', `${results.matrixCells.length} 个`);
      
      if (results.wordLibraryItems.length > 0) {
        console.log('词库列表:');
        results.wordLibraryItems.forEach((item, index) => {
          const key = item.getAttribute('data-word-library');
          console.log(`  ${index + 1}. ${key}`);
        });
      }
      
      return results;
    },
    
    // 手动触发滑动
    triggerScroll(libraryKey = 'red-1') {
      console.log(`\n🎯 手动触发滑动: ${libraryKey}`);
      
      const container = document.querySelector('.word-library-manager .overflow-y-auto');
      const target = document.querySelector(`[data-word-library="${libraryKey}"]`);
      
      if (!container) {
        console.error('❌ 未找到滚动容器');
        return false;
      }
      
      if (!target) {
        console.error(`❌ 未找到目标词库: ${libraryKey}`);
        return false;
      }
      
      console.log('✅ 找到滚动容器和目标元素');
      
      // 计算滑动位置
      const containerRect = container.getBoundingClientRect();
      const targetRect = target.getBoundingClientRect();
      
      const current = container.scrollTop;
      const targetCenter = targetRect.top + targetRect.height / 2;
      const containerCenter = containerRect.top + containerRect.height / 2;
      const targetPosition = current + (targetCenter - containerCenter);
      const delta = targetPosition - current;
      
      console.log('位置计算:', {
        current,
        target: targetPosition,
        delta,
        action: delta === 0 ? '不移动' : delta < 0 ? '上移' : '下移'
      });
      
      if (Math.abs(delta) < 5) {
        console.log('ℹ️ 目标已在正确位置，无需滑动');
        return true;
      }
      
      // 执行滑动
      container.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });
      
      console.log(`✅ 滑动已执行: ${delta < 0 ? '上移' : '下移'} ${Math.abs(delta)}px`);
      
      // 高亮目标元素
      target.style.border = '3px solid #ef4444';
      target.style.backgroundColor = 'rgba(239, 68, 68, 0.1)';
      
      setTimeout(() => {
        target.style.border = '';
        target.style.backgroundColor = '';
      }, 2000);
      
      return true;
    },
    
    // 模拟双击事件
    simulateDoubleClick(x = 0, y = 0) {
      console.log(`\n🖱️ 模拟双击单元格 (${x}, ${y})`);
      
      const cell = document.querySelector(`[data-x="${x}"][data-y="${y}"]`);
      if (!cell) {
        console.error(`❌ 未找到单元格 (${x}, ${y})`);
        return false;
      }
      
      console.log('✅ 找到目标单元格');
      
      // 获取单元格颜色和等级
      const color = cell.getAttribute('data-color') || 'red';
      const level = cell.getAttribute('data-level') || '1';
      const libraryKey = `${color}-${level}`;
      
      console.log(`目标词库: ${libraryKey}`);
      
      // 触发双击事件
      const event = new MouseEvent('dblclick', {
        bubbles: true,
        cancelable: true,
        view: window
      });
      
      cell.dispatchEvent(event);
      console.log('✅ 双击事件已触发');
      
      // 等待一段时间后检查滑动
      setTimeout(() => {
        console.log('🔍 检查滑动结果...');
        const target = document.querySelector(`[data-word-library="${libraryKey}"]`);
        if (target) {
          const isActive = target.classList.contains('word-library-active') ||
                          target.classList.contains('active');
          console.log(`词库激活状态: ${isActive ? '✅ 已激活' : '⚠️ 未激活'}`);
        }
      }, 500);
      
      return true;
    },
    
    // 监听状态变化
    watchStateChanges() {
      console.log('\n👀 开始监听状态变化...');
      
      // 监听DOM变化
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && 
              mutation.attributeName === 'class') {
            const target = mutation.target;
            if (target.hasAttribute('data-word-library')) {
              const libraryKey = target.getAttribute('data-word-library');
              const isActive = target.classList.contains('word-library-active') ||
                              target.classList.contains('active');
              console.log(`📊 词库状态变化: ${libraryKey} -> ${isActive ? '激活' : '未激活'}`);
            }
          }
        });
      });
      
      // 观察词库项目的类名变化
      document.querySelectorAll('[data-word-library]').forEach(item => {
        observer.observe(item, { attributes: true, attributeFilter: ['class'] });
      });
      
      console.log('✅ 状态监听已启动');
      
      // 返回停止监听的函数
      return () => {
        observer.disconnect();
        console.log('⏹️ 状态监听已停止');
      };
    },
    
    // 运行完整诊断
    runDiagnosis() {
      console.log('\n🔍 开始完整诊断...');
      
      // 1. 检查DOM
      const domResults = this.checkDOM();
      
      // 2. 测试手动滑动
      if (domResults.scrollContainer && domResults.wordLibraryItems.length > 0) {
        console.log('\n测试手动滑动...');
        this.triggerScroll('red-1');
        
        setTimeout(() => {
          this.triggerScroll('blue-2');
        }, 1000);
        
        setTimeout(() => {
          this.triggerScroll('green-3');
        }, 2000);
      }
      
      // 3. 测试双击模拟
      if (domResults.matrixCells.length > 0) {
        setTimeout(() => {
          console.log('\n测试双击模拟...');
          this.simulateDoubleClick(0, 0);
          
          setTimeout(() => {
            this.simulateDoubleClick(1, 1);
          }, 1000);
        }, 3000);
      }
      
      console.log('\n✅ 诊断完成，请观察上述输出结果');
    },
    
    // 修复常见问题
    fixCommonIssues() {
      console.log('\n🔧 尝试修复常见问题...');
      
      // 修复1: 确保词库项目有正确的data属性
      const items = document.querySelectorAll('.word-library-item');
      items.forEach((item, index) => {
        if (!item.hasAttribute('data-word-library')) {
          // 尝试从内容推断词库键
          const title = item.querySelector('.library-title, h3, h4');
          if (title) {
            const text = title.textContent || '';
            const match = text.match(/(红色|蓝色|绿色|黄色|紫色|橙色|粉色|青色)(\d+)级/);
            if (match) {
              const colorMap = {
                '红色': 'red', '蓝色': 'blue', '绿色': 'green', '黄色': 'yellow',
                '紫色': 'purple', '橙色': 'orange', '粉色': 'pink', '青色': 'cyan'
              };
              const color = colorMap[match[1]];
              const level = match[2];
              const libraryKey = `${color}-${level}`;
              item.setAttribute('data-word-library', libraryKey);
              console.log(`✅ 修复词库项目: ${libraryKey}`);
            }
          }
        }
      });
      
      // 修复2: 确保滚动容器有正确的类名
      const manager = document.querySelector('.word-library-manager');
      if (manager) {
        const scrollable = manager.querySelector('.overflow-y-auto');
        if (!scrollable) {
          const candidate = manager.querySelector('[style*="overflow"], .scroll, .scrollable');
          if (candidate) {
            candidate.classList.add('overflow-y-auto');
            console.log('✅ 修复滚动容器类名');
          }
        }
      }
      
      console.log('🔧 修复尝试完成');
    }
  };
  
  // 将调试工具暴露到全局
  window.debugWordLibraryScroll = debugTool;
  
  // 显示使用说明
  console.log('\n📖 使用说明:');
  console.log('debugWordLibraryScroll.checkDOM() - 检查DOM结构');
  console.log('debugWordLibraryScroll.triggerScroll("red-1") - 手动触发滑动');
  console.log('debugWordLibraryScroll.simulateDoubleClick(0, 0) - 模拟双击');
  console.log('debugWordLibraryScroll.watchStateChanges() - 监听状态变化');
  console.log('debugWordLibraryScroll.runDiagnosis() - 运行完整诊断');
  console.log('debugWordLibraryScroll.fixCommonIssues() - 修复常见问题');
  
  console.log('\n🚀 快速开始: debugWordLibraryScroll.runDiagnosis()');
  
})();
