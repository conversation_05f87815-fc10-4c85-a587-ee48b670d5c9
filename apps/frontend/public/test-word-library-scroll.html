<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词库滑动功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: #2563eb;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .test-content {
            display: flex;
            height: 600px;
        }
        
        .matrix-section {
            flex: 1;
            padding: 20px;
            border-right: 1px solid #e5e7eb;
        }
        
        .controls-section {
            width: 320px;
            padding: 20px;
            background: #f9fafb;
        }
        
        .test-matrix {
            display: grid;
            grid-template-columns: repeat(8, 40px);
            gap: 2px;
            margin: 20px 0;
        }
        
        .test-cell {
            width: 40px;
            height: 40px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            transition: all 0.2s ease;
        }
        
        .test-cell:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .test-cell.active {
            border-width: 3px;
            border-color: #4b5563;
            box-shadow: 0 0 0 1px #4b5563;
            z-index: 10;
        }
        
        /* 颜色样式 */
        .color-red { background-color: #fecaca; color: #dc2626; }
        .color-blue { background-color: #bfdbfe; color: #2563eb; }
        .color-green { background-color: #bbf7d0; color: #16a34a; }
        .color-yellow { background-color: #fef3c7; color: #d97706; }
        .color-purple { background-color: #e9d5ff; color: #9333ea; }
        .color-orange { background-color: #fed7aa; color: #ea580c; }
        .color-pink { background-color: #fce7f3; color: #ec4899; }
        .color-cyan { background-color: #a5f3fc; color: #0891b2; }
        
        .word-library-manager {
            height: 400px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            overflow: hidden;
        }
        
        .word-library-header {
            background: #f3f4f6;
            padding: 12px;
            border-bottom: 1px solid #d1d5db;
            font-weight: 600;
        }
        
        .word-library-list {
            height: calc(100% - 45px);
            overflow-y: auto;
            padding: 8px;
        }
        
        .word-library-item {
            margin-bottom: 8px;
            padding: 8px;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        
        .word-library-item.active {
            border-width: 3px;
            border-color: #4b5563;
            box-shadow: 0 0 0 1px #4b5563;
            background-color: rgba(75, 85, 99, 0.05);
        }
        
        .library-title {
            font-weight: 600;
            margin-bottom: 4px;
            font-size: 14px;
        }
        
        .library-words {
            font-size: 12px;
            color: #6b7280;
        }
        
        .test-controls {
            margin-bottom: 20px;
        }
        
        .test-button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 8px;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #1d4ed8;
        }
        
        .test-button.secondary {
            background: #6b7280;
        }
        
        .test-button.secondary:hover {
            background: #4b5563;
        }
        
        .status-panel {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 12px;
            margin-top: 20px;
        }
        
        .status-title {
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .status-item {
            font-size: 12px;
            margin-bottom: 4px;
            font-family: monospace;
        }
        
        .log-panel {
            background: #1f2937;
            color: #f9fafb;
            border-radius: 4px;
            padding: 12px;
            margin-top: 12px;
            height: 120px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 11px;
        }
        
        .log-entry {
            margin-bottom: 2px;
            word-break: break-all;
        }
        
        .log-entry.error {
            color: #fca5a5;
        }
        
        .log-entry.success {
            color: #86efac;
        }
        
        .log-entry.info {
            color: #93c5fd;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>词库滑动功能测试</h1>
            <p>测试双击激活填词模式时词库面板的滑动功能</p>
        </div>
        
        <div class="test-content">
            <div class="matrix-section">
                <h3>测试矩阵</h3>
                <p>双击任意单元格激活填词模式，观察右侧词库面板是否滑动到对应词库</p>
                
                <div class="test-matrix" id="testMatrix">
                    <!-- 动态生成测试单元格 -->
                </div>
                
                <div class="test-controls">
                    <button class="test-button" onclick="simulateDoubleClick('red', 1)">激活红色1级</button>
                    <button class="test-button" onclick="simulateDoubleClick('blue', 2)">激活蓝色2级</button>
                    <button class="test-button" onclick="simulateDoubleClick('green', 3)">激活绿色3级</button>
                    <button class="test-button" onclick="simulateDoubleClick('yellow', 4)">激活黄色4级</button>
                    <button class="test-button secondary" onclick="deactivateWordInput()">退出填词模式</button>
                    <button class="test-button secondary" onclick="clearLogs()">清空日志</button>
                </div>
                
                <div class="status-panel">
                    <div class="status-title">当前状态</div>
                    <div class="status-item">填词模式: <span id="wordInputStatus">未激活</span></div>
                    <div class="status-item">匹配词库: <span id="matchedLibrary">无</span></div>
                    <div class="status-item">选中单元格: <span id="selectedCell">无</span></div>
                    <div class="status-item">当前激活词库: <span id="currentActiveLibrary">无</span></div>
                </div>
            </div>
            
            <div class="controls-section">
                <div class="word-library-manager">
                    <div class="word-library-header">
                        词库管理面板
                    </div>
                    <div class="word-library-list" id="wordLibraryList">
                        <!-- 动态生成词库列表 -->
                    </div>
                </div>
                
                <div class="log-panel" id="logPanel">
                    <!-- 日志输出 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟词库数据
        const mockLibraries = [
            { key: 'red-1', color: 'red', level: 1, name: '红色1级', words: ['苹果', '草莓', '樱桃'] },
            { key: 'red-2', color: 'red', level: 2, name: '红色2级', words: ['玫瑰', '血液', '火焰'] },
            { key: 'blue-1', color: 'blue', level: 1, name: '蓝色1级', words: ['天空', '海洋', '蓝莓'] },
            { key: 'blue-2', color: 'blue', level: 2, name: '蓝色2级', words: ['深海', '蓝宝石', '忧郁'] },
            { key: 'green-1', color: 'green', level: 1, name: '绿色1级', words: ['草地', '树叶', '翡翠'] },
            { key: 'green-2', color: 'green', level: 2, name: '绿色2级', words: ['森林', '生机', '希望'] },
            { key: 'green-3', color: 'green', level: 3, name: '绿色3级', words: ['环保', '自然', '和谐'] },
            { key: 'yellow-1', color: 'yellow', level: 1, name: '黄色1级', words: ['太阳', '香蕉', '柠檬'] },
            { key: 'yellow-2', color: 'yellow', level: 2, name: '黄色2级', words: ['金子', '麦田', '温暖'] },
            { key: 'yellow-3', color: 'yellow', level: 3, name: '黄色3级', words: ['光明', '智慧', '活力'] },
            { key: 'yellow-4', color: 'yellow', level: 4, name: '黄色4级', words: ['辉煌', '荣耀', '成功'] },
            { key: 'purple-1', color: 'purple', level: 1, name: '紫色1级', words: ['薰衣草', '葡萄', '紫罗兰'] },
            { key: 'orange-1', color: 'orange', level: 1, name: '橙色1级', words: ['橙子', '胡萝卜', '夕阳'] },
            { key: 'pink-1', color: 'pink', level: 1, name: '粉色1级', words: ['樱花', '粉红', '温柔'] },
            { key: 'cyan-1', color: 'cyan', level: 1, name: '青色1级', words: ['青春', '清新', '宁静'] },
        ];

        // 当前状态
        let currentState = {
            isWordInputActive: false,
            matchedLibrary: null,
            selectedCell: null,
            currentActiveLibrary: null
        };

        // 初始化页面
        function initializePage() {
            generateTestMatrix();
            generateWordLibraryList();
            updateStatusDisplay();
            log('页面初始化完成', 'info');
        }

        // 生成测试矩阵
        function generateTestMatrix() {
            const matrix = document.getElementById('testMatrix');
            const colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'cyan'];
            
            for (let i = 0; i < 64; i++) {
                const cell = document.createElement('div');
                const color = colors[i % colors.length];
                const level = Math.floor(i / 8) % 4 + 1;
                
                cell.className = `test-cell color-${color}`;
                cell.textContent = `${color.charAt(0).toUpperCase()}${level}`;
                cell.dataset.color = color;
                cell.dataset.level = level;
                cell.dataset.x = i % 8;
                cell.dataset.y = Math.floor(i / 8);
                
                cell.addEventListener('dblclick', () => {
                    simulateDoubleClick(color, level);
                });
                
                matrix.appendChild(cell);
            }
        }

        // 生成词库列表
        function generateWordLibraryList() {
            const list = document.getElementById('wordLibraryList');
            
            mockLibraries.forEach(library => {
                const item = document.createElement('div');
                item.className = 'word-library-item';
                item.dataset.wordLibrary = library.key;
                
                item.innerHTML = `
                    <div class="library-title color-${library.color}">${library.name}</div>
                    <div class="library-words">${library.words.join(', ')}</div>
                `;
                
                list.appendChild(item);
            });
        }

        // 模拟双击激活
        function simulateDoubleClick(color, level) {
            const libraryKey = `${color}-${level}`;
            
            log(`模拟双击激活: ${color} ${level}级 (${libraryKey})`, 'info');
            
            // 更新状态
            currentState.isWordInputActive = true;
            currentState.matchedLibrary = libraryKey;
            currentState.selectedCell = { x: 0, y: 0 };
            
            // 更新UI
            updateCellActiveState();
            updateLibraryActiveState(libraryKey);
            updateStatusDisplay();
            
            // 模拟滑动
            simulateScrollToLibrary(libraryKey);
            
            log(`填词模式已激活，匹配词库: ${libraryKey}`, 'success');
        }

        // 模拟滑动到词库
        function simulateScrollToLibrary(libraryKey) {
            const container = document.getElementById('wordLibraryList');
            const targetElement = document.querySelector(`[data-word-library="${libraryKey}"]`);
            
            if (!container || !targetElement) {
                log(`滑动失败: 未找到容器或目标元素 ${libraryKey}`, 'error');
                return;
            }
            
            // 计算位置
            const containerRect = container.getBoundingClientRect();
            const targetRect = targetElement.getBoundingClientRect();
            
            const current = container.scrollTop;
            const targetCenter = targetRect.top + targetRect.height / 2;
            const containerCenter = containerRect.top + containerRect.height / 2;
            const target = current + (targetCenter - containerCenter);
            const delta = target - current;
            
            log(`位置计算: current=${current}, target=${target}, delta=${delta}`, 'info');
            
            if (Math.abs(delta) < 5) {
                log('无需滑动: 目标已在正确位置', 'info');
                currentState.currentActiveLibrary = libraryKey;
                updateStatusDisplay();
                return;
            }
            
            // 执行滑动
            const action = delta < 0 ? '上移' : '下移';
            log(`执行${action}滑动: ${Math.abs(delta)}px`, 'success');
            
            container.scrollTo({
                top: target,
                behavior: 'smooth'
            });
            
            currentState.currentActiveLibrary = libraryKey;
            updateStatusDisplay();
        }

        // 退出填词模式
        function deactivateWordInput() {
            currentState.isWordInputActive = false;
            currentState.matchedLibrary = null;
            currentState.selectedCell = null;
            
            updateCellActiveState();
            updateLibraryActiveState(null);
            updateStatusDisplay();
            
            log('填词模式已退出', 'info');
        }

        // 更新单元格激活状态
        function updateCellActiveState() {
            document.querySelectorAll('.test-cell').forEach(cell => {
                cell.classList.remove('active');
            });
            
            if (currentState.isWordInputActive && currentState.selectedCell) {
                const activeCell = document.querySelector(
                    `[data-x="${currentState.selectedCell.x}"][data-y="${currentState.selectedCell.y}"]`
                );
                if (activeCell) {
                    activeCell.classList.add('active');
                }
            }
        }

        // 更新词库激活状态
        function updateLibraryActiveState(libraryKey) {
            document.querySelectorAll('.word-library-item').forEach(item => {
                item.classList.remove('active');
            });
            
            if (libraryKey) {
                const activeLibrary = document.querySelector(`[data-word-library="${libraryKey}"]`);
                if (activeLibrary) {
                    activeLibrary.classList.add('active');
                }
            }
        }

        // 更新状态显示
        function updateStatusDisplay() {
            document.getElementById('wordInputStatus').textContent = 
                currentState.isWordInputActive ? '已激活' : '未激活';
            document.getElementById('matchedLibrary').textContent = 
                currentState.matchedLibrary || '无';
            document.getElementById('selectedCell').textContent = 
                currentState.selectedCell ? `(${currentState.selectedCell.x}, ${currentState.selectedCell.y})` : '无';
            document.getElementById('currentActiveLibrary').textContent = 
                currentState.currentActiveLibrary || '无';
        }

        // 日志功能
        function log(message, type = 'info') {
            const logPanel = document.getElementById('logPanel');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logPanel.appendChild(entry);
            logPanel.scrollTop = logPanel.scrollHeight;
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('logPanel').innerHTML = '';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
