/**
 * 单击滚动修复测试脚本
 * 测试单击格子是否还会导致词库模块刷新回到顶部
 */

(function() {
  'use strict';
  
  console.log('%c🔧 单击滚动修复测试', 'color: #f59e0b; font-size: 18px; font-weight: bold');
  
  const testClickScrollFix = {
    // 测试状态
    testState: {
      initialScrollPosition: 0,
      afterClickScrollPosition: 0,
      testResults: []
    },
    
    // 初始化测试
    initTest() {
      console.log('\n🚀 初始化单击滚动修复测试...');
      
      // 检查必要元素
      const matrixCells = document.querySelectorAll('[data-x][data-y]');
      const scrollContainer = document.querySelector('.word-library-manager .overflow-y-auto');
      const wordLibraryItems = document.querySelectorAll('[data-word-library]');
      
      console.log('环境检查:');
      console.log(`  矩阵单元格: ${matrixCells.length} 个`);
      console.log(`  滚动容器: ${scrollContainer ? '✅ 找到' : '❌ 未找到'}`);
      console.log(`  词库项目: ${wordLibraryItems.length} 个`);
      
      if (!scrollContainer || matrixCells.length === 0 || wordLibraryItems.length === 0) {
        console.error('❌ 测试环境不完整，无法进行测试');
        return false;
      }
      
      // 滚动到中间位置作为测试起点
      const middlePosition = scrollContainer.scrollHeight / 2;
      scrollContainer.scrollTop = middlePosition;
      this.testState.initialScrollPosition = middlePosition;
      
      console.log(`✅ 测试环境就绪，初始滚动位置: ${middlePosition}`);
      return true;
    },
    
    // 测试单击是否导致滚动重置
    testSingleClick(x = 2, y = 2) {
      console.log(`\n🖱️ 测试单击单元格 (${x}, ${y})`);
      
      const scrollContainer = document.querySelector('.word-library-manager .overflow-y-auto');
      if (!scrollContainer) {
        console.error('❌ 未找到滚动容器');
        return false;
      }
      
      // 记录点击前的滚动位置
      const beforeClick = scrollContainer.scrollTop;
      console.log(`点击前滚动位置: ${beforeClick}`);
      
      // 查找目标单元格
      const targetCell = document.querySelector(`[data-x="${x}"][data-y="${y}"]`);
      if (!targetCell) {
        console.error(`❌ 未找到单元格 (${x}, ${y})`);
        return false;
      }
      
      // 创建并触发单击事件
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window
      });
      
      targetCell.dispatchEvent(clickEvent);
      console.log('✅ 单击事件已触发');
      
      // 延迟检查滚动位置变化
      setTimeout(() => {
        const afterClick = scrollContainer.scrollTop;
        const scrollDelta = Math.abs(afterClick - beforeClick);
        
        console.log(`点击后滚动位置: ${afterClick}`);
        console.log(`位置变化: ${scrollDelta}px`);
        
        // 判断是否发生了意外的滚动重置
        const isScrollReset = scrollDelta > 50; // 超过50px认为是重置
        const testResult = {
          cellPosition: { x, y },
          beforeClick,
          afterClick,
          scrollDelta,
          isScrollReset,
          timestamp: Date.now()
        };
        
        this.testState.testResults.push(testResult);
        
        if (isScrollReset) {
          console.log(`❌ 检测到滚动重置: 位置变化 ${scrollDelta}px`);
        } else {
          console.log(`✅ 滚动位置稳定: 位置变化 ${scrollDelta}px`);
        }
        
        return !isScrollReset;
      }, 200);
      
      return true;
    },
    
    // 批量测试多个单元格
    runBatchClickTest() {
      console.log('\n🔄 开始批量单击测试...');
      
      if (!this.initTest()) {
        return;
      }
      
      const testCases = [
        { x: 0, y: 0 },
        { x: 1, y: 1 },
        { x: 2, y: 2 },
        { x: 3, y: 3 },
        { x: 4, y: 4 }
      ];
      
      let currentIndex = 0;
      
      const runNextTest = () => {
        if (currentIndex >= testCases.length) {
          setTimeout(() => this.showTestSummary(), 1000);
          return;
        }
        
        const testCase = testCases[currentIndex];
        console.log(`\n--- 测试 ${currentIndex + 1}/${testCases.length}: (${testCase.x}, ${testCase.y}) ---`);
        
        this.testSingleClick(testCase.x, testCase.y);
        
        currentIndex++;
        setTimeout(runNextTest, 1500); // 1.5秒间隔
      };
      
      runNextTest();
    },
    
    // 测试填词模式下的单击行为
    testWordInputModeClick() {
      console.log('\n📝 测试填词模式下的单击行为...');
      
      // 首先激活填词模式
      const targetCell = document.querySelector('[data-x="1"][data-y="1"]');
      if (!targetCell) {
        console.error('❌ 未找到测试单元格');
        return;
      }
      
      // 双击激活填词模式
      const doubleClickEvent = new MouseEvent('dblclick', {
        bubbles: true,
        cancelable: true,
        view: window
      });
      
      targetCell.dispatchEvent(doubleClickEvent);
      console.log('✅ 双击激活填词模式');
      
      // 等待填词模式激活后测试单击
      setTimeout(() => {
        console.log('🖱️ 测试填词模式下的单击...');
        this.testSingleClick(1, 1); // 点击同一个单元格
        
        setTimeout(() => {
          this.testSingleClick(2, 2); // 点击不同单元格
        }, 1000);
      }, 500);
    },
    
    // 显示测试总结
    showTestSummary() {
      console.log('\n📊 测试总结');
      console.log('='.repeat(50));
      
      const results = this.testState.testResults;
      if (results.length === 0) {
        console.log('📝 暂无测试数据');
        return;
      }
      
      const resetCount = results.filter(r => r.isScrollReset).length;
      const stableCount = results.length - resetCount;
      
      console.log(`总测试次数: ${results.length}`);
      console.log(`滚动稳定: ${stableCount} 次`);
      console.log(`滚动重置: ${resetCount} 次`);
      console.log(`稳定率: ${((stableCount / results.length) * 100).toFixed(1)}%`);
      
      if (resetCount === 0) {
        console.log('🎉 所有测试通过！单击不再导致滚动重置');
      } else {
        console.log('⚠️ 仍有滚动重置问题，需要进一步修复');
        
        console.log('\n问题详情:');
        results.filter(r => r.isScrollReset).forEach((result, index) => {
          console.log(`${index + 1}. 单元格(${result.cellPosition.x}, ${result.cellPosition.y}): ${result.beforeClick} → ${result.afterClick} (Δ${result.scrollDelta}px)`);
        });
      }
    },
    
    // 监听滚动事件
    monitorScrollEvents() {
      console.log('\n👀 开始监听滚动事件...');
      
      const scrollContainer = document.querySelector('.word-library-manager .overflow-y-auto');
      if (!scrollContainer) {
        console.error('❌ 未找到滚动容器');
        return null;
      }
      
      let scrollEventCount = 0;
      
      const handleScroll = (event) => {
        scrollEventCount++;
        const scrollTop = event.target.scrollTop;
        console.log(`📜 滚动事件 #${scrollEventCount}: scrollTop = ${scrollTop}`);
      };
      
      scrollContainer.addEventListener('scroll', handleScroll);
      
      console.log('✅ 滚动监听已启动');
      
      // 返回停止监听的函数
      return () => {
        scrollContainer.removeEventListener('scroll', handleScroll);
        console.log(`⏹️ 滚动监听已停止 (共监听到 ${scrollEventCount} 次滚动)`);
      };
    },
    
    // 手动设置滚动位置进行测试
    setTestScrollPosition(position = 300) {
      console.log(`\n📍 设置测试滚动位置: ${position}px`);
      
      const scrollContainer = document.querySelector('.word-library-manager .overflow-y-auto');
      if (scrollContainer) {
        scrollContainer.scrollTop = position;
        console.log(`✅ 滚动位置已设置为: ${position}px`);
        return true;
      } else {
        console.error('❌ 未找到滚动容器');
        return false;
      }
    }
  };
  
  // 暴露到全局
  window.testClickScrollFix = testClickScrollFix;
  
  // 显示使用说明
  console.log('\n📖 使用说明:');
  console.log('testClickScrollFix.runBatchClickTest() - 批量测试单击行为');
  console.log('testClickScrollFix.testWordInputModeClick() - 测试填词模式下的单击');
  console.log('testClickScrollFix.monitorScrollEvents() - 监听滚动事件');
  console.log('testClickScrollFix.setTestScrollPosition(300) - 设置测试位置');
  console.log('testClickScrollFix.showTestSummary() - 显示测试结果');
  
  console.log('\n🚀 快速开始: testClickScrollFix.runBatchClickTest()');
  
})();
