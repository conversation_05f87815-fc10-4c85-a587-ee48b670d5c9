/**
 * 实时滑动功能测试脚本
 * 在运行的应用中测试双击激活填词模式时的滑动功能
 */

(function() {
  'use strict';
  
  console.log('%c🔴 实时滑动功能测试', 'color: #ef4444; font-size: 18px; font-weight: bold');
  console.log('请在矩阵中双击任意单元格，观察词库面板是否滑动到对应位置');
  
  let testResults = [];
  let isMonitoring = false;
  
  const liveTest = {
    // 开始监控
    startMonitoring() {
      if (isMonitoring) {
        console.log('⚠️ 监控已在运行中');
        return;
      }
      
      isMonitoring = true;
      console.log('🎯 开始监控双击事件和滑动行为...');
      
      // 监控矩阵单元格双击
      this.monitorMatrixDoubleClick();
      
      // 监控词库状态变化
      this.monitorLibraryStateChange();
      
      // 监控滚动事件
      this.monitorScrollEvents();
      
      console.log('✅ 监控已启动，请双击矩阵单元格进行测试');
    },
    
    // 停止监控
    stopMonitoring() {
      isMonitoring = false;
      console.log('⏹️ 监控已停止');
      this.showResults();
    },
    
    // 监控矩阵双击事件
    monitorMatrixDoubleClick() {
      // 查找矩阵容器
      const matrixContainer = document.querySelector('.matrix-container, .test-matrix, [class*="matrix"]');
      if (!matrixContainer) {
        console.warn('⚠️ 未找到矩阵容器');
        return;
      }
      
      console.log('✅ 找到矩阵容器，开始监控双击事件');
      
      // 使用事件委托监听双击
      matrixContainer.addEventListener('dblclick', (event) => {
        const target = event.target;
        const x = target.getAttribute('data-x');
        const y = target.getAttribute('data-y');
        const color = target.getAttribute('data-color');
        const level = target.getAttribute('data-level');
        
        if (x !== null && y !== null) {
          const timestamp = Date.now();
          console.log(`🖱️ 检测到双击: (${x}, ${y})`, {
            color,
            level,
            element: target,
            timestamp
          });
          
          // 记录测试结果
          const testCase = {
            timestamp,
            action: 'doubleClick',
            position: { x: parseInt(x), y: parseInt(y) },
            color,
            level,
            libraryKey: color && level ? `${color}-${level}` : null,
            scrollDetected: false,
            libraryActivated: false
          };
          
          testResults.push(testCase);
          
          // 延迟检查滑动结果
          setTimeout(() => this.checkScrollResult(testCase), 500);
        }
      });
    },
    
    // 监控词库状态变化
    monitorLibraryStateChange() {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            const target = mutation.target;
            const libraryKey = target.getAttribute('data-word-library');
            
            if (libraryKey) {
              const isActive = target.classList.contains('word-library-active') ||
                              target.classList.contains('active') ||
                              target.style.borderColor === 'rgb(75, 85, 99)';
              
              console.log(`📊 词库状态变化: ${libraryKey} -> ${isActive ? '激活' : '未激活'}`);
              
              // 更新最近的测试结果
              const recentTest = testResults[testResults.length - 1];
              if (recentTest && recentTest.libraryKey === libraryKey) {
                recentTest.libraryActivated = isActive;
              }
            }
          }
        });
      });
      
      // 观察所有词库项目
      document.querySelectorAll('[data-word-library]').forEach(item => {
        observer.observe(item, { 
          attributes: true, 
          attributeFilter: ['class', 'style'] 
        });
      });
      
      console.log('✅ 词库状态监控已启动');
    },
    
    // 监控滚动事件
    monitorScrollEvents() {
      const scrollContainer = document.querySelector('.word-library-manager .overflow-y-auto');
      if (!scrollContainer) {
        console.warn('⚠️ 未找到滚动容器');
        return;
      }
      
      console.log('✅ 找到滚动容器，开始监控滚动事件');
      
      let scrollTimeout;
      scrollContainer.addEventListener('scroll', () => {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(() => {
          const scrollTop = scrollContainer.scrollTop;
          console.log(`📜 滚动事件: scrollTop = ${scrollTop}`);
          
          // 更新最近的测试结果
          const recentTest = testResults[testResults.length - 1];
          if (recentTest && Date.now() - recentTest.timestamp < 2000) {
            recentTest.scrollDetected = true;
            recentTest.finalScrollPosition = scrollTop;
          }
        }, 100);
      });
    },
    
    // 检查滑动结果
    checkScrollResult(testCase) {
      console.log(`🔍 检查滑动结果: ${testCase.libraryKey}`);
      
      const targetElement = document.querySelector(`[data-word-library="${testCase.libraryKey}"]`);
      if (!targetElement) {
        console.warn(`⚠️ 未找到目标词库元素: ${testCase.libraryKey}`);
        testCase.error = '目标元素未找到';
        return;
      }
      
      // 检查元素是否在可视区域
      const container = document.querySelector('.word-library-manager .overflow-y-auto');
      if (container) {
        const containerRect = container.getBoundingClientRect();
        const targetRect = targetElement.getBoundingClientRect();
        
        const isVisible = targetRect.top >= containerRect.top && 
                         targetRect.bottom <= containerRect.bottom;
        
        const isInCenter = Math.abs(
          (targetRect.top + targetRect.height / 2) - 
          (containerRect.top + containerRect.height / 2)
        ) < 50;
        
        testCase.isVisible = isVisible;
        testCase.isInCenter = isInCenter;
        testCase.success = testCase.scrollDetected && (isVisible || isInCenter);
        
        console.log(`📊 滑动结果:`, {
          libraryKey: testCase.libraryKey,
          scrollDetected: testCase.scrollDetected,
          libraryActivated: testCase.libraryActivated,
          isVisible,
          isInCenter,
          success: testCase.success
        });
        
        if (testCase.success) {
          console.log(`✅ 滑动测试成功: ${testCase.libraryKey}`);
        } else {
          console.log(`❌ 滑动测试失败: ${testCase.libraryKey}`);
        }
      }
    },
    
    // 显示测试结果
    showResults() {
      console.log('\n📊 测试结果汇总:');
      console.log('='.repeat(50));
      
      if (testResults.length === 0) {
        console.log('📝 暂无测试数据');
        return;
      }
      
      const successCount = testResults.filter(t => t.success).length;
      const totalCount = testResults.length;
      
      console.log(`总测试次数: ${totalCount}`);
      console.log(`成功次数: ${successCount}`);
      console.log(`成功率: ${((successCount / totalCount) * 100).toFixed(1)}%`);
      
      console.log('\n详细结果:');
      testResults.forEach((test, index) => {
        const status = test.success ? '✅' : '❌';
        const time = new Date(test.timestamp).toLocaleTimeString();
        console.log(`${index + 1}. ${status} ${time} - ${test.libraryKey} (${test.position.x}, ${test.position.y})`);
        
        if (!test.success) {
          const issues = [];
          if (!test.scrollDetected) issues.push('未检测到滚动');
          if (!test.libraryActivated) issues.push('词库未激活');
          if (!test.isVisible && !test.isInCenter) issues.push('元素不在可视区域');
          if (test.error) issues.push(test.error);
          
          console.log(`   问题: ${issues.join(', ')}`);
        }
      });
    },
    
    // 手动测试
    manualTest(libraryKey = 'red-1') {
      console.log(`\n🧪 手动测试滑动: ${libraryKey}`);
      
      const container = document.querySelector('.word-library-manager .overflow-y-auto');
      const target = document.querySelector(`[data-word-library="${libraryKey}"]`);
      
      if (!container || !target) {
        console.error('❌ 测试失败: 未找到必要元素');
        return false;
      }
      
      // 记录初始位置
      const initialScroll = container.scrollTop;
      
      // 计算目标位置
      const containerRect = container.getBoundingClientRect();
      const targetRect = target.getBoundingClientRect();
      const targetCenter = targetRect.top + targetRect.height / 2;
      const containerCenter = containerRect.top + containerRect.height / 2;
      const targetPosition = initialScroll + (targetCenter - containerCenter);
      
      console.log('位置信息:', {
        initialScroll,
        targetPosition,
        delta: targetPosition - initialScroll
      });
      
      // 执行滑动
      container.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });
      
      // 高亮目标元素
      target.style.border = '3px solid #ef4444';
      target.style.backgroundColor = 'rgba(239, 68, 68, 0.1)';
      
      setTimeout(() => {
        target.style.border = '';
        target.style.backgroundColor = '';
        
        const finalScroll = container.scrollTop;
        const success = Math.abs(finalScroll - targetPosition) < 20;
        
        console.log(`手动测试结果: ${success ? '✅ 成功' : '❌ 失败'}`);
        console.log(`最终位置: ${finalScroll} (目标: ${targetPosition})`);
      }, 1000);
      
      return true;
    },
    
    // 清空结果
    clearResults() {
      testResults = [];
      console.log('🗑️ 测试结果已清空');
    }
  };
  
  // 暴露到全局
  window.liveTestScroll = liveTest;
  
  // 显示使用说明
  console.log('\n📖 使用说明:');
  console.log('liveTestScroll.startMonitoring() - 开始监控测试');
  console.log('liveTestScroll.stopMonitoring() - 停止监控并显示结果');
  console.log('liveTestScroll.manualTest("red-1") - 手动测试指定词库');
  console.log('liveTestScroll.showResults() - 显示测试结果');
  console.log('liveTestScroll.clearResults() - 清空测试结果');
  
  console.log('\n🚀 快速开始: liveTestScroll.startMonitoring()');
  console.log('然后双击矩阵中的任意单元格进行测试');
  
})();
