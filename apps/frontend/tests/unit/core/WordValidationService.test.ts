/**
 * WordValidationService 单元测试
 * 测试词语验证服务的各种验证规则和场景
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { WordValidationService } from '../../../core/wordLibrary/WordValidationService';
import type { WordLibrary, WordLibraryKey } from '../../../core/matrix/MatrixTypes';
import { createWordEntry } from '../../../core/wordLibrary/WordLibraryCore';

describe('WordValidationService', () => {
  let mockLibraries: Map<WordLibraryKey, WordLibrary>;

  beforeEach(() => {
    // 创建模拟词库数据
    mockLibraries = new Map();
    
    const library1: WordLibrary = {
      key: 'black-1' as WordLibraryKey,
      color: 'black',
      level: 1,
      words: [
        createWordEntry('测试', 'black', 1),
        createWordEntry('示例', 'black', 1),
      ],
      collapsed: false,
      lastUpdated: new Date(),
    };
    
    const library2: WordLibrary = {
      key: 'red-1' as WordLibraryKey,
      color: 'red',
      level: 1,
      words: [
        createWordEntry('红色', 'red', 1),
        createWordEntry('测试', 'red', 1), // 跨词库重复
      ],
      collapsed: false,
      lastUpdated: new Date(),
    };
    
    mockLibraries.set('black-1' as WordLibraryKey, library1);
    mockLibraries.set('red-1' as WordLibraryKey, library2);
  });

  describe('文本格式验证', () => {
    test('应该接受有效的词语', () => {
      const result = WordValidationService.validateWordText('有效词语');
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('应该拒绝过短的词语', () => {
      const result = WordValidationService.validateWordText('短');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('词语长度不能少于2个字符');
    });

    test('应该拒绝过长的词语', () => {
      const result = WordValidationService.validateWordText('这是一个非常长的词语');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('词语长度不能超过4个字符');
    });

    test('应该拒绝空词语', () => {
      const result = WordValidationService.validateWordText('');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('词语不能为空');
    });

    test('应该拒绝只有空格的词语', () => {
      const result = WordValidationService.validateWordText('   ');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('词语不能为空');
    });

    test('应该对特殊字符给出警告', () => {
      const result = WordValidationService.validateWordText('测试@#');
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('词语包含特殊字符，建议使用中文、英文或数字');
    });

    test('应该对纯数字给出警告', () => {
      const result = WordValidationService.validateWordText('123');
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('词语全为数字，建议添加文字说明');
    });

    test('应该对重复字符给出警告', () => {
      const result = WordValidationService.validateWordText('aaaa');
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('词语由相同字符组成，建议使用更有意义的词语');
    });

    test('应该对无意义词语给出警告', () => {
      const result = WordValidationService.validateWordText('测试');
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('建议使用更有意义的词语');
    });
  });

  describe('重复检测', () => {
    test('应该检测同词库内的重复', () => {
      const result = WordValidationService.checkWordDuplicate(
        '测试',
        'black-1' as WordLibraryKey,
        mockLibraries
      );
      
      expect(result.isDuplicate).toBe(true);
      expect(result.conflictType).toBe('same-library');
      expect(result.duplicateLibraries).toContain('black-1');
    });

    test('应该检测跨词库的重复', () => {
      const result = WordValidationService.checkWordDuplicate(
        '测试',
        'blue-1' as WordLibraryKey, // 不存在的词库
        mockLibraries
      );
      
      expect(result.isDuplicate).toBe(true);
      expect(result.conflictType).toBe('cross-library');
      expect(result.duplicateLibraries).toContain('black-1');
      expect(result.duplicateLibraries).toContain('red-1');
    });

    test('应该正确处理无重复的情况', () => {
      const result = WordValidationService.checkWordDuplicate(
        '新词语',
        'black-1' as WordLibraryKey,
        mockLibraries
      );
      
      expect(result.isDuplicate).toBe(false);
      expect(result.conflictType).toBe('none');
      expect(result.duplicateLibraries).toHaveLength(0);
    });
  });

  describe('完整词语验证', () => {
    test('应该拒绝同词库内的重复词语', () => {
      const result = WordValidationService.validateWord(
        '测试',
        'black-1' as WordLibraryKey,
        mockLibraries
      );
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('词语"测试"已存在于当前词库中');
      expect(result.isDuplicate).toBe(true);
    });

    test('应该允许跨词库的重复词语但给出警告', () => {
      const result = WordValidationService.validateWord(
        '测试',
        'blue-1' as WordLibraryKey,
        mockLibraries
      );
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('词语"测试"在其他词库中已存在');
      expect(result.isDuplicate).toBe(true);
    });

    test('应该接受有效的新词语', () => {
      const result = WordValidationService.validateWord(
        '新词语',
        'black-1' as WordLibraryKey,
        mockLibraries
      );
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.isDuplicate).toBe(false);
    });

    test('应该拒绝格式无效的词语', () => {
      const result = WordValidationService.validateWord(
        'a', // 太短
        'black-1' as WordLibraryKey,
        mockLibraries
      );
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('词语长度不能少于2个字符');
    });
  });

  describe('批量验证', () => {
    test('应该正确处理批量验证', () => {
      const words = [
        { text: '新词1', libraryKey: 'black-1' as WordLibraryKey },
        { text: '新词2', libraryKey: 'black-1' as WordLibraryKey },
        { text: '测试', libraryKey: 'black-1' as WordLibraryKey }, // 重复
        { text: 'a', libraryKey: 'black-1' as WordLibraryKey }, // 太短
      ];
      
      const result = WordValidationService.validateWordsBatch(words, mockLibraries);
      
      expect(result.summary.totalWords).toBe(4);
      expect(result.summary.validWords).toBe(2);
      expect(result.summary.invalidWords).toBe(2);
      expect(result.summary.duplicateWords).toBe(1);
      
      expect(result.results.get('新词1')?.isValid).toBe(true);
      expect(result.results.get('新词2')?.isValid).toBe(true);
      expect(result.results.get('测试')?.isValid).toBe(false);
      expect(result.results.get('a')?.isValid).toBe(false);
    });
  });

  describe('输入预检查', () => {
    test('应该为有效输入返回可提交状态', () => {
      const result = WordValidationService.validateInputBeforeSubmit(
        '新词语',
        'black-1' as WordLibraryKey,
        mockLibraries
      );
      
      expect(result.canSubmit).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.suggestions).toHaveLength(0);
    });

    test('应该为无效输入提供建议', () => {
      const result = WordValidationService.validateInputBeforeSubmit(
        'a',
        'black-1' as WordLibraryKey,
        mockLibraries
      );
      
      expect(result.canSubmit).toBe(false);
      expect(result.errors).toContain('词语长度不能少于2个字符');
      expect(result.suggestions).toContain('尝试添加更多字符');
    });

    test('应该为重复词语提供建议', () => {
      const result = WordValidationService.validateInputBeforeSubmit(
        '测试',
        'black-1' as WordLibraryKey,
        mockLibraries
      );
      
      expect(result.canSubmit).toBe(false);
      expect(result.suggestions).toContain('尝试使用不同的词语或在词语后添加数字');
    });
  });

  describe('词语建议', () => {
    test('应该提供相关的词语建议', () => {
      const suggestions = WordValidationService.getWordSuggestions(
        '测',
        'black-1' as WordLibraryKey,
        mockLibraries,
        5
      );
      
      expect(suggestions).toContain('测试');
    });

    test('应该限制建议数量', () => {
      const suggestions = WordValidationService.getWordSuggestions(
        '测',
        'black-1' as WordLibraryKey,
        mockLibraries,
        1
      );
      
      expect(suggestions).toHaveLength(1);
    });

    test('应该为空输入返回空建议', () => {
      const suggestions = WordValidationService.getWordSuggestions(
        '',
        'black-1' as WordLibraryKey,
        mockLibraries
      );
      
      expect(suggestions).toHaveLength(0);
    });
  });

  describe('自定义验证规则', () => {
    test('应该支持添加自定义验证规则', () => {
      WordValidationService.addCustomRule({
        name: 'no-numbers',
        priority: 1,
        validate: (text: string) => {
          const hasNumbers = /\d/.test(text);
          return {
            isValid: !hasNumbers,
            errors: hasNumbers ? ['不允许包含数字'] : [],
          };
        },
      });
      
      const result = WordValidationService.validateWordText('测试123');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('不允许包含数字');
      
      // 清理自定义规则
      WordValidationService.removeCustomRule('no-numbers');
    });

    test('应该支持移除自定义验证规则', () => {
      WordValidationService.addCustomRule({
        name: 'test-rule',
        priority: 1,
        validate: () => ({ isValid: false, errors: ['测试规则'] }),
      });
      
      const removed = WordValidationService.removeCustomRule('test-rule');
      expect(removed).toBe(true);
      
      const result = WordValidationService.validateWordText('测试');
      expect(result.errors).not.toContain('测试规则');
    });
  });
});
