/**
 * MatrixCacheService 单元测试
 * 测试缓存服务的各种功能和边界情况
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { MatrixCacheService } from '../../../core/matrix/MatrixCacheService';

describe('MatrixCacheService', () => {
  let cacheService: MatrixCacheService;

  beforeEach(() => {
    // 每个测试使用新的缓存实例
    cacheService = MatrixCacheService.getInstance({
      maxSize: 10,
      defaultTTL: 1000, // 1秒
      cleanupInterval: 100, // 100ms
      enableStats: true,
    });
    cacheService.clear();
  });

  afterEach(() => {
    cacheService.destroy();
  });

  describe('基础缓存功能', () => {
    test('应该正确设置和获取缓存值', () => {
      const testData = { test: 'data' };
      
      cacheService.set('test-key', testData);
      const retrieved = cacheService.get('test-key');
      
      expect(retrieved).toEqual(testData);
    });

    test('应该正确处理不存在的键', () => {
      const result = cacheService.get('non-existent-key');
      expect(result).toBeNull();
    });

    test('应该正确删除缓存条目', () => {
      cacheService.set('test-key', 'test-value');
      const deleted = cacheService.delete('test-key');
      
      expect(deleted).toBe(true);
      expect(cacheService.get('test-key')).toBeNull();
    });

    test('应该正确清空所有缓存', () => {
      cacheService.set('key1', 'value1');
      cacheService.set('key2', 'value2');
      
      cacheService.clear();
      
      expect(cacheService.get('key1')).toBeNull();
      expect(cacheService.get('key2')).toBeNull();
    });
  });

  describe('TTL功能', () => {
    test('应该正确处理缓存过期', async () => {
      cacheService.set('test-key', 'test-value', 50); // 50ms TTL
      
      expect(cacheService.get('test-key')).toBe('test-value');
      
      // 等待过期
      await new Promise(resolve => setTimeout(resolve, 100));
      
      expect(cacheService.get('test-key')).toBeNull();
    });

    test('应该正确使用自定义TTL', () => {
      const longTTL = 5000; // 5秒
      cacheService.set('test-key', 'test-value', longTTL);
      
      // 立即检查应该存在
      expect(cacheService.get('test-key')).toBe('test-value');
    });

    test('应该正确检查缓存是否存在且未过期', async () => {
      cacheService.set('test-key', 'test-value', 50);
      
      expect(cacheService.has('test-key')).toBe(true);
      
      await new Promise(resolve => setTimeout(resolve, 100));
      
      expect(cacheService.has('test-key')).toBe(false);
    });
  });

  describe('LRU功能', () => {
    test('应该正确实现LRU淘汰策略', () => {
      // 填满缓存
      for (let i = 0; i < 10; i++) {
        cacheService.set(`key${i}`, `value${i}`);
      }
      
      // 添加第11个条目，应该淘汰最旧的
      cacheService.set('key10', 'value10');
      
      // key0应该被淘汰
      expect(cacheService.get('key0')).toBeNull();
      expect(cacheService.get('key10')).toBe('value10');
    });

    test('应该正确更新访问时间', () => {
      // 填满缓存
      for (let i = 0; i < 10; i++) {
        cacheService.set(`key${i}`, `value${i}`);
      }
      
      // 访问key0，更新其访问时间
      cacheService.get('key0');
      
      // 添加新条目，key1应该被淘汰而不是key0
      cacheService.set('key10', 'value10');
      
      expect(cacheService.get('key0')).toBe('value0');
      expect(cacheService.get('key1')).toBeNull();
    });
  });

  describe('批量操作', () => {
    test('应该正确处理批量设置', () => {
      const entries = [
        { key: 'key1', value: 'value1' },
        { key: 'key2', value: 'value2', ttl: 2000 },
        { key: 'key3', value: 'value3' },
      ];
      
      cacheService.setMany(entries);
      
      expect(cacheService.get('key1')).toBe('value1');
      expect(cacheService.get('key2')).toBe('value2');
      expect(cacheService.get('key3')).toBe('value3');
    });

    test('应该正确处理批量获取', () => {
      cacheService.set('key1', 'value1');
      cacheService.set('key2', 'value2');
      cacheService.set('key3', 'value3');
      
      const results = cacheService.getMany(['key1', 'key2', 'non-existent']);
      
      expect(results.size).toBe(2);
      expect(results.get('key1')).toBe('value1');
      expect(results.get('key2')).toBe('value2');
      expect(results.has('non-existent')).toBe(false);
    });
  });

  describe('getOrSet功能', () => {
    test('应该在缓存不存在时调用工厂函数', () => {
      const factory = vi.fn(() => 'computed-value');
      
      const result = cacheService.getOrSet('test-key', factory);
      
      expect(factory).toHaveBeenCalledOnce();
      expect(result).toBe('computed-value');
      expect(cacheService.get('test-key')).toBe('computed-value');
    });

    test('应该在缓存存在时直接返回缓存值', () => {
      cacheService.set('test-key', 'cached-value');
      const factory = vi.fn(() => 'computed-value');
      
      const result = cacheService.getOrSet('test-key', factory);
      
      expect(factory).not.toHaveBeenCalled();
      expect(result).toBe('cached-value');
    });
  });

  describe('统计功能', () => {
    test('应该正确统计缓存命中和未命中', () => {
      cacheService.set('test-key', 'test-value');
      
      // 命中
      cacheService.get('test-key');
      cacheService.get('test-key');
      
      // 未命中
      cacheService.get('non-existent-key');
      
      const stats = cacheService.getStats();
      
      expect(stats.hits).toBe(2);
      expect(stats.misses).toBe(1);
      expect(stats.hitRate).toBe(0.67); // 2/3 ≈ 0.67
    });

    test('应该正确统计缓存条目数量', () => {
      cacheService.set('key1', 'value1');
      cacheService.set('key2', 'value2');
      
      const stats = cacheService.getStats();
      
      expect(stats.totalEntries).toBe(2);
      expect(stats.memoryUsage).toBeGreaterThan(0);
    });
  });

  describe('自动清理功能', () => {
    test('应该自动清理过期的缓存条目', async () => {
      cacheService.set('short-lived', 'value', 50); // 50ms TTL
      cacheService.set('long-lived', 'value', 5000); // 5s TTL
      
      expect(cacheService.getStats().totalEntries).toBe(2);
      
      // 等待清理周期
      await new Promise(resolve => setTimeout(resolve, 200));
      
      const stats = cacheService.getStats();
      expect(stats.totalEntries).toBe(1);
      expect(cacheService.get('long-lived')).toBe('value');
      expect(cacheService.get('short-lived')).toBeNull();
    });
  });

  describe('边界情况', () => {
    test('应该正确处理空值缓存', () => {
      cacheService.set('null-key', null);
      cacheService.set('undefined-key', undefined);
      cacheService.set('empty-string-key', '');
      
      expect(cacheService.get('null-key')).toBeNull();
      expect(cacheService.get('undefined-key')).toBeUndefined();
      expect(cacheService.get('empty-string-key')).toBe('');
    });

    test('应该正确处理复杂对象缓存', () => {
      const complexObject = {
        nested: {
          array: [1, 2, 3],
          map: new Map([['key', 'value']]),
          date: new Date(),
        },
      };
      
      cacheService.set('complex-key', complexObject);
      const retrieved = cacheService.get('complex-key');
      
      expect(retrieved).toEqual(complexObject);
    });

    test('应该正确处理大量数据', () => {
      const largeData = new Array(1000).fill(0).map((_, i) => ({ id: i, data: `data-${i}` }));
      
      cacheService.set('large-data', largeData);
      const retrieved = cacheService.get('large-data');
      
      expect(retrieved).toEqual(largeData);
      expect(retrieved).toHaveLength(1000);
    });
  });
});
