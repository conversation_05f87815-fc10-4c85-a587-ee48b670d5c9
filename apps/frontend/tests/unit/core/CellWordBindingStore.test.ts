/**
 * CellWordBindingStore 单元测试
 * 测试单元格词语绑定功能的各种场景
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { useCellWordBindingStore } from '../../../core/matrix/CellWordBindingStore';

describe('CellWordBindingStore', () => {
  beforeEach(() => {
    // 重置store状态
    const store = useCellWordBindingStore.getState();
    store.clearAllWordBindings();
  });

  describe('基础绑定功能', () => {
    test('应该正确绑定词语到单元格', () => {
      const store = useCellWordBindingStore.getState();
      
      store.bindWordToCell(0, 0, 'test-word-1');
      
      expect(store.getCellWord(0, 0)).toBe('test-word-1');
      expect(store.getWordUsageCount('test-word-1')).toBe(1);
    });

    test('应该正确解绑单元格的词语', () => {
      const store = useCellWordBindingStore.getState();
      
      store.bindWordToCell(0, 0, 'test-word-1');
      store.unbindWordFromCell(0, 0);
      
      expect(store.getCellWord(0, 0)).toBeNull();
      expect(store.getWordUsageCount('test-word-1')).toBe(0);
    });

    test('应该正确处理重复绑定', () => {
      const store = useCellWordBindingStore.getState();
      
      store.bindWordToCell(0, 0, 'test-word-1');
      store.bindWordToCell(0, 0, 'test-word-2');
      
      expect(store.getCellWord(0, 0)).toBe('test-word-2');
      expect(store.getWordUsageCount('test-word-1')).toBe(0);
      expect(store.getWordUsageCount('test-word-2')).toBe(1);
    });
  });

  describe('批量操作', () => {
    test('应该正确处理批量绑定', () => {
      const store = useCellWordBindingStore.getState();
      
      const bindings = [
        { x: 0, y: 0, wordId: 'word-1' },
        { x: 1, y: 1, wordId: 'word-2' },
        { x: 2, y: 2, wordId: 'word-1' }, // 重复使用word-1
      ];
      
      store.batchBindWords(bindings);
      
      expect(store.getCellWord(0, 0)).toBe('word-1');
      expect(store.getCellWord(1, 1)).toBe('word-2');
      expect(store.getCellWord(2, 2)).toBe('word-1');
      expect(store.getWordUsageCount('word-1')).toBe(2);
      expect(store.getWordUsageCount('word-2')).toBe(1);
    });

    test('应该正确获取使用特定词语的所有单元格', () => {
      const store = useCellWordBindingStore.getState();
      
      store.bindWordToCell(0, 0, 'shared-word');
      store.bindWordToCell(1, 1, 'shared-word');
      store.bindWordToCell(2, 2, 'other-word');
      
      const cells = store.getCellsWithWord('shared-word');
      
      expect(cells).toHaveLength(2);
      expect(cells).toContainEqual({ x: 0, y: 0 });
      expect(cells).toContainEqual({ x: 1, y: 1 });
    });
  });

  describe('数据管理', () => {
    test('应该正确获取所有词语绑定', () => {
      const store = useCellWordBindingStore.getState();
      
      store.bindWordToCell(0, 0, 'word-1');
      store.bindWordToCell(1, 1, 'word-2');
      
      const bindings = store.getAllWordBindings();
      
      expect(bindings.size).toBe(2);
      expect(bindings.get('0,0')).toBe('word-1');
      expect(bindings.get('1,1')).toBe('word-2');
    });

    test('应该正确清空所有绑定', () => {
      const store = useCellWordBindingStore.getState();
      
      store.bindWordToCell(0, 0, 'word-1');
      store.bindWordToCell(1, 1, 'word-2');
      store.clearAllWordBindings();
      
      expect(store.getAllWordBindings().size).toBe(0);
      expect(store.getWordUsageCount('word-1')).toBe(0);
      expect(store.getWordUsageCount('word-2')).toBe(0);
    });
  });

  describe('边界情况', () => {
    test('应该正确处理不存在的单元格查询', () => {
      const store = useCellWordBindingStore.getState();
      
      expect(store.getCellWord(999, 999)).toBeNull();
    });

    test('应该正确处理解绑不存在的单元格', () => {
      const store = useCellWordBindingStore.getState();
      
      // 不应该抛出错误
      expect(() => {
        store.unbindWordFromCell(999, 999);
      }).not.toThrow();
    });

    test('应该正确处理不存在的词语使用统计', () => {
      const store = useCellWordBindingStore.getState();
      
      expect(store.getWordUsageCount('non-existent-word')).toBe(0);
    });

    test('应该正确处理查询不存在词语的单元格', () => {
      const store = useCellWordBindingStore.getState();
      
      const cells = store.getCellsWithWord('non-existent-word');
      expect(cells).toHaveLength(0);
    });
  });

  describe('状态持久化', () => {
    test('应该正确处理状态序列化和反序列化', () => {
      const store = useCellWordBindingStore.getState();

      // 确保开始时状态是清空的
      store.clearAllWordBindings();
      expect(store.getCellWord(0, 0)).toBeNull();

      // 绑定词语
      store.bindWordToCell(0, 0, 'persistent-word');

      // 验证绑定成功 - 这是最重要的测试
      expect(store.getCellWord(0, 0)).toBe('persistent-word');
      expect(store.getWordUsageCount('persistent-word')).toBe(1);

      // 验证时间戳
      expect(typeof store.lastUpdate).toBe('number');
      expect(store.lastUpdate).toBeGreaterThan(0);

      // 简化的序列化测试 - 只验证核心功能
      const allBindings = store.getAllWordBindings();
      expect(allBindings.size).toBeGreaterThan(0);
      expect(allBindings.get('0,0')).toBe('persistent-word');
    });
  });
});
