/**
 * useCellStyle Hook 单元测试
 * 测试单元格样式计算Hook的各种功能
 */

import { describe, test, expect } from 'vitest';
import { renderHook } from '@testing-library/react';
import { useCellStyle, useSimpleCellStyle, useThemeStyle } from '../../../hooks/useCellStyle';
import type { CellData, CellRenderData, MatrixConfig } from '../../../core/matrix/MatrixTypes';

// 模拟配置
const mockConfig: MatrixConfig = {
  mode: 'color-word',
  mainMode: 'color',
  contentMode: 'word',
  isColorMode: true,
  isWordMode: true,
  isNumberMode: false,
  isCoordinateMode: false,
};

// 模拟单元格数据
const mockCellData: CellData = {
  x: 0,
  y: 0,
  color: 'red',
  level: 1,
  group: 'A',
  displayCoordinate: 'A1',
  isActive: true,
  isSelected: false,
  isHovered: false,
  isFocused: false,
};

// 模拟渲染数据
const mockRenderData: CellRenderData = {
  content: '测试',
  className: 'test-cell',
  isInteractive: true,
  style: {
    backgroundColor: '#ff0000',
    color: '#ffffff',
  },
};

describe('useCellStyle', () => {
  describe('基础功能', () => {
    test('应该返回基础样式结果', () => {
      const { result } = renderHook(() =>
        useCellStyle({
          coordinate: { x: 0, y: 0 },
          config: mockConfig,
        })
      );

      expect(result.current).toHaveProperty('style');
      expect(result.current).toHaveProperty('className');
      expect(result.current).toHaveProperty('isSpecial');
      expect(result.current).toHaveProperty('priority');
      
      expect(typeof result.current.style).toBe('object');
      expect(typeof result.current.className).toBe('string');
      expect(typeof result.current.isSpecial).toBe('boolean');
      expect(typeof result.current.priority).toBe('number');
    });

    test('应该正确应用渲染数据样式', () => {
      const { result } = renderHook(() =>
        useCellStyle({
          coordinate: { x: 0, y: 0 },
          config: mockConfig,
          renderData: mockRenderData,
        })
      );

      expect(result.current.style.backgroundColor).toBe('#ff0000');
      expect(result.current.style.color).toBe('#ffffff');
      expect(result.current.className).toContain('test-cell');
    });

    test('应该正确应用自定义样式', () => {
      const customStyle = {
        fontSize: '16px',
        fontWeight: 'bold',
      };

      const { result } = renderHook(() =>
        useCellStyle({
          coordinate: { x: 0, y: 0 },
          config: mockConfig,
          customStyle,
        })
      );

      expect(result.current.style.fontSize).toBe('16px');
      expect(result.current.style.fontWeight).toBe('bold');
    });
  });

  describe('增强模式', () => {
    test('应该正确应用增强样式', () => {
      const { result } = renderHook(() =>
        useCellStyle({
          coordinate: { x: 0, y: 0 },
          config: mockConfig,
          isEnhanced: true,
        })
      );

      expect(result.current.style.borderRadius).toBe('50%');
      expect(result.current.style.zIndex).toBe(10);
      expect(result.current.className).toContain('matrix-cell--enhanced');
      expect(result.current.isSpecial).toBe(true);
      expect(result.current.priority).toBeGreaterThanOrEqual(100);
    });

    test('增强样式应该覆盖其他样式', () => {
      const customStyle = { borderRadius: '0px' };

      const { result } = renderHook(() =>
        useCellStyle({
          coordinate: { x: 0, y: 0 },
          config: mockConfig,
          isEnhanced: true,
          customStyle,
        })
      );

      // 增强样式应该覆盖自定义样式
      expect(result.current.style.borderRadius).toBe('50%');
    });
  });

  describe('填词模式', () => {
    test('应该正确应用填词模式样式', () => {
      const { result } = renderHook(() =>
        useCellStyle({
          coordinate: { x: 0, y: 0 },
          config: mockConfig,
          isWordInputActive: true,
        })
      );

      expect(result.current.style.outline).toBe('2px solid #007bff');
      expect(result.current.style.backgroundColor).toBe('#e3f2fd');
      expect(result.current.className).toContain('matrix-cell--word-input-active');
      expect(result.current.isSpecial).toBe(true);
      expect(result.current.priority).toBeGreaterThanOrEqual(50);
    });
  });

  describe('单元格状态', () => {
    test('应该正确应用选中状态样式', () => {
      const selectedCellData = { ...mockCellData, isSelected: true };

      const { result } = renderHook(() =>
        useCellStyle({
          coordinate: { x: 0, y: 0 },
          config: mockConfig,
          cellData: selectedCellData,
        })
      );

      expect(result.current.style.backgroundColor).toBe('#fff3cd');
      expect(result.current.style.borderColor).toBe('#ffc107');
      expect(result.current.className).toContain('matrix-cell--selected');
      expect(result.current.priority).toBeGreaterThanOrEqual(30);
    });

    test('应该正确应用悬停状态样式', () => {
      const hoveredCellData = { ...mockCellData, isHovered: true };

      const { result } = renderHook(() =>
        useCellStyle({
          coordinate: { x: 0, y: 0 },
          config: mockConfig,
          cellData: hoveredCellData,
        })
      );

      expect(result.current.style.backgroundColor).toBe('#f8f9fa');
      expect(result.current.className).toContain('matrix-cell--hovered');
      expect(result.current.priority).toBeGreaterThanOrEqual(10);
    });

    test('应该正确应用焦点状态样式', () => {
      const focusedCellData = { ...mockCellData, isFocused: true };

      const { result } = renderHook(() =>
        useCellStyle({
          coordinate: { x: 0, y: 0 },
          config: mockConfig,
          cellData: focusedCellData,
        })
      );

      expect(result.current.style.outline).toBe('2px solid #007bff');
      expect(result.current.className).toContain('matrix-cell--focused');
      expect(result.current.priority).toBeGreaterThanOrEqual(20);
    });
  });

  describe('主题支持', () => {
    test('应该正确应用自定义主题', () => {
      const customTheme = {
        primaryColor: '#ff6b6b',
        backgroundColor: '#f0f0f0',
        textColor: '#333333',
      };

      const { result } = renderHook(() =>
        useCellStyle({
          coordinate: { x: 0, y: 0 },
          config: mockConfig,
          theme: customTheme,
        })
      );

      expect(result.current.style.backgroundColor).toBe('#f0f0f0');
      expect(result.current.style.color).toBe('#333333');
    });
  });

  describe('优先级计算', () => {
    test('应该正确计算样式优先级', () => {
      const { result: enhancedResult } = renderHook(() =>
        useCellStyle({
          coordinate: { x: 0, y: 0 },
          config: mockConfig,
          isEnhanced: true,
        })
      );

      const { result: wordInputResult } = renderHook(() =>
        useCellStyle({
          coordinate: { x: 0, y: 0 },
          config: mockConfig,
          isWordInputActive: true,
        })
      );

      const { result: selectedResult } = renderHook(() =>
        useCellStyle({
          coordinate: { x: 0, y: 0 },
          config: mockConfig,
          cellData: { ...mockCellData, isSelected: true },
        })
      );

      // 增强模式优先级最高
      expect(enhancedResult.current.priority).toBeGreaterThan(wordInputResult.current.priority);
      expect(wordInputResult.current.priority).toBeGreaterThan(selectedResult.current.priority);
    });
  });

  describe('性能优化', () => {
    test('应该正确缓存计算结果', () => {
      const { result, rerender } = renderHook(
        ({ coordinate }) =>
          useCellStyle({
            coordinate,
            config: mockConfig,
          }),
        {
          initialProps: { coordinate: { x: 0, y: 0 } },
        }
      );

      const firstResult = result.current;

      // 重新渲染但参数不变
      rerender({ coordinate: { x: 0, y: 0 } });

      // 结果应该是同一个对象（由于useMemo缓存）
      expect(result.current).toStrictEqual(firstResult);
    });

    test('参数变化时应该重新计算', () => {
      const { result, rerender } = renderHook(
        ({ isEnhanced }) =>
          useCellStyle({
            coordinate: { x: 0, y: 0 },
            config: mockConfig,
            isEnhanced,
          }),
        {
          initialProps: { isEnhanced: false },
        }
      );

      const firstResult = result.current;

      // 改变参数
      rerender({ isEnhanced: true });

      // 结果应该不同
      expect(result.current).not.toBe(firstResult);
      expect(result.current.isSpecial).toBe(true);
    });
  });
});

describe('useSimpleCellStyle', () => {
  test('应该返回简化的样式对象', () => {
    const { result } = renderHook(() =>
      useSimpleCellStyle(0, 0, false, { fontSize: '14px' })
    );

    expect(typeof result.current).toBe('object');
    expect(result.current.fontSize).toBe('14px');
  });

  test('应该正确应用增强样式', () => {
    const { result } = renderHook(() =>
      useSimpleCellStyle(0, 0, true)
    );

    expect(result.current.borderRadius).toBe('50%');
    expect(result.current.zIndex).toBe(10);
  });
});

describe('useThemeStyle', () => {
  test('应该返回CSS变量对象', () => {
    const theme = {
      primaryColor: '#007bff',
      backgroundColor: '#ffffff',
    };

    const { result } = renderHook(() => useThemeStyle(theme));

    expect(result.current).toHaveProperty('--cell-primary-color', '#007bff');
    expect(result.current).toHaveProperty('--cell-background-color', '#ffffff');
  });

  test('应该使用默认主题值', () => {
    const { result } = renderHook(() => useThemeStyle());

    expect(result.current).toHaveProperty('--cell-primary-color');
    expect(result.current).toHaveProperty('--cell-background-color');
    expect(result.current).toHaveProperty('--cell-text-color');
  });
});
