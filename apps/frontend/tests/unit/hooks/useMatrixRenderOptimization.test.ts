/**
 * useMatrixRenderOptimization Hook 单元测试
 * 测试矩阵渲染优化Hook的各种功能
 */

import { describe, test, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useMatrixRenderOptimization } from '../../../hooks/useMatrixRenderOptimization';
import type { MatrixData, MatrixConfig, CellData, CellRenderData } from '../../../core/matrix/MatrixTypes';

// 模拟配置
const mockConfig: MatrixConfig = {
  mode: 'color-word',
  mainMode: 'color',
  contentMode: 'word',
  isColorMode: true,
  isWordMode: true,
  isNumberMode: false,
  isCoordinateMode: false,
};

// 创建模拟矩阵数据
const createMockMatrixData = (): MatrixData => {
  const cells = new Map<string, CellData>();
  
  // 添加一些测试单元格
  for (let i = 0; i < 10; i++) {
    const x = i % 5;
    const y = Math.floor(i / 5);
    const key = `${x},${y}`;
    
    cells.set(key, {
      x,
      y,
      color: 'red',
      level: (i % 3 + 1) as DataLevel,
      group: 'A',
      displayCoordinate: `A${i + 1}`,
      isActive: true,
      isSelected: i === 0, // 第一个单元格被选中
      isHovered: i === 1,  // 第二个单元格被悬停
      isFocused: i === 2,  // 第三个单元格获得焦点
    });
  }
  
  return {
    cells,
    selectedCells: new Set(['0,0']),
    hoveredCell: "1,0",
    focusedCell: "2,0",
    lastUpdate: Date.now(),
  };
};

// 模拟渲染数据获取函数
const mockGetCellRenderData = vi.fn((x: number, y: number): CellRenderData | undefined => {
  return {
    content: `Cell ${x},${y}`,
    className: 'test-cell',
    isInteractive: true,
    style: {
      backgroundColor: '#ffffff',
      color: '#000000',
    },
  };
});

describe('useMatrixRenderOptimization', () => {
  let mockMatrixData: MatrixData;

  beforeEach(() => {
    mockMatrixData = createMockMatrixData();
    mockGetCellRenderData.mockClear();
  });

  describe('基础功能', () => {
    test('应该返回优化结果对象', () => {
      const { result } = renderHook(() =>
        useMatrixRenderOptimization(
          mockMatrixData,
          mockConfig,
          mockGetCellRenderData
        )
      );

      expect(result.current).toHaveProperty('getOptimizedRenderData');
      expect(result.current).toHaveProperty('currentRenderCells');
      expect(result.current).toHaveProperty('visibleCells');
      expect(result.current).toHaveProperty('metrics');
      expect(result.current).toHaveProperty('clearCache');
      expect(result.current).toHaveProperty('preloadCells');
    });

    test('应该正确计算可见单元格', () => {
      const { result } = renderHook(() =>
        useMatrixRenderOptimization(
          mockMatrixData,
          mockConfig,
          mockGetCellRenderData,
          { enableViewportCulling: false }
        )
      );

      // 没有视口裁剪时，应该包含所有单元格
      expect(result.current.visibleCells.length).toBe(33 * 33);
    });

    test('应该正确处理视口裁剪', () => {
      const viewport = { left: 0, top: 0, right: 4, bottom: 4 };
      
      const { result } = renderHook(() =>
        useMatrixRenderOptimization(
          mockMatrixData,
          mockConfig,
          mockGetCellRenderData,
          { 
            enableViewportCulling: true,
            viewport 
          }
        )
      );

      // 视口裁剪后，应该只包含指定范围内的单元格
      expect(result.current.visibleCells.length).toBe(25); // 5x5
    });
  });

  describe('缓存功能', () => {
    test('应该正确缓存渲染数据', () => {
      const { result } = renderHook(() =>
        useMatrixRenderOptimization(
          mockMatrixData,
          mockConfig,
          mockGetCellRenderData,
          { enableCellCache: true }
        )
      );

      // 第一次调用
      const renderData1 = result.current.getOptimizedRenderData(0, 0);
      expect(mockGetCellRenderData).toHaveBeenCalledWith(0, 0);

      // 第二次调用应该使用缓存
      const renderData2 = result.current.getOptimizedRenderData(0, 0);
      expect(renderData1).toBe(renderData2);
      
      // 应该只调用一次原始函数
      expect(mockGetCellRenderData).toHaveBeenCalledTimes(1);
    });

    test('应该正确清理缓存', () => {
      const { result } = renderHook(() =>
        useMatrixRenderOptimization(
          mockMatrixData,
          mockConfig,
          mockGetCellRenderData,
          { enableCellCache: true }
        )
      );

      // 先缓存一些数据
      result.current.getOptimizedRenderData(0, 0);
      expect(result.current.metrics.cacheHitRate).toBe(0); // 第一次是miss

      result.current.getOptimizedRenderData(0, 0);
      expect(result.current.metrics.cacheHitRate).toBeGreaterThan(0); // 第二次是hit

      // 清理缓存
      act(() => {
        result.current.clearCache();
      });

      // 清理后再次访问应该重新调用原始函数
      result.current.getOptimizedRenderData(0, 0);
      expect(mockGetCellRenderData).toHaveBeenCalledTimes(3); // 初始1次 + 清理后1次
    });
  });

  describe('优先级计算', () => {
    test('应该正确计算单元格优先级', () => {
      const { result } = renderHook(() =>
        useMatrixRenderOptimization(
          mockMatrixData,
          mockConfig,
          mockGetCellRenderData
        )
      );

      const visibleCells = result.current.visibleCells;
      
      // 找到选中的单元格（应该有最高优先级）
      const selectedCell = visibleCells.find(cell => cell.x === 0 && cell.y === 0);
      expect(selectedCell?.priority).toBeGreaterThan(100);

      // 找到悬停的单元格
      const hoveredCell = visibleCells.find(cell => cell.x === 1 && cell.y === 0);
      expect(hoveredCell?.priority).toBeGreaterThan(30);

      // 找到焦点单元格
      const focusedCell = visibleCells.find(cell => cell.x === 2 && cell.y === 0);
      expect(focusedCell?.priority).toBeGreaterThan(50);
    });

    test('选中单元格应该有最高优先级', () => {
      const { result } = renderHook(() =>
        useMatrixRenderOptimization(
          mockMatrixData,
          mockConfig,
          mockGetCellRenderData
        )
      );

      const visibleCells = result.current.visibleCells;
      const selectedCell = visibleCells.find(cell => cell.x === 0 && cell.y === 0);
      const normalCell = visibleCells.find(cell => cell.x === 3 && cell.y === 0);

      expect(selectedCell?.priority).toBeGreaterThan(normalCell?.priority || 0);
    });
  });

  describe('性能指标', () => {
    test('应该正确计算性能指标', () => {
      const { result } = renderHook(() =>
        useMatrixRenderOptimization(
          mockMatrixData,
          mockConfig,
          mockGetCellRenderData,
          { enableViewportCulling: false }
        )
      );

      const metrics = result.current.metrics;

      expect(metrics.totalCells).toBe(33 * 33);
      expect(metrics.visibleCells).toBe(33 * 33);
      expect(metrics.cellsInCurrentFrame).toBe(33 * 33);
      expect(typeof metrics.cacheHitRate).toBe('number');
      expect(typeof metrics.averageRenderTime).toBe('number');
      expect(typeof metrics.skippedCells).toBe('number');
    });

    test('视口裁剪应该正确计算跳过的单元格数', () => {
      const viewport = { left: 0, top: 0, right: 4, bottom: 4 };
      
      const { result } = renderHook(() =>
        useMatrixRenderOptimization(
          mockMatrixData,
          mockConfig,
          mockGetCellRenderData,
          { 
            enableViewportCulling: true,
            viewport 
          }
        )
      );

      const metrics = result.current.metrics;
      const expectedSkipped = (33 * 33) - 25; // 总数 - 可见数

      expect(metrics.skippedCells).toBe(expectedSkipped);
    });
  });

  describe('预加载功能', () => {
    test('应该正确预加载指定单元格', () => {
      const { result } = renderHook(() =>
        useMatrixRenderOptimization(
          mockMatrixData,
          mockConfig,
          mockGetCellRenderData,
          { enableCellCache: true }
        )
      );

      const coordinatesToPreload = [
        { x: 5, y: 5 },
        { x: 6, y: 6 },
        { x: 7, y: 7 },
      ];

      act(() => {
        result.current.preloadCells(coordinatesToPreload);
      });

      // 预加载应该调用渲染数据获取函数
      expect(mockGetCellRenderData).toHaveBeenCalledWith(5, 5);
      expect(mockGetCellRenderData).toHaveBeenCalledWith(6, 6);
      expect(mockGetCellRenderData).toHaveBeenCalledWith(7, 7);
    });
  });

  describe('防抖功能', () => {
    test('应该正确处理数据变化的防抖', async () => {
      const { result, rerender } = renderHook(
        ({ matrixData }) =>
          useMatrixRenderOptimization(
            matrixData,
            mockConfig,
            mockGetCellRenderData,
            { debounceDelay: 100 }
          ),
        { initialProps: { matrixData: mockMatrixData } }
      );

      const initialCellCount = result.current.visibleCells.length;

      // 快速更改数据
      const newMatrixData = { ...mockMatrixData };
      rerender({ matrixData: newMatrixData });

      // 立即检查，应该还是旧数据
      expect(result.current.visibleCells.length).toBe(initialCellCount);

      // 等待防抖延迟
      await new Promise(resolve => setTimeout(resolve, 150));

      // 现在应该使用新数据
      // 注意：由于我们的测试数据结构相同，这里主要测试防抖机制是否工作
    });
  });

  describe('边界情况', () => {
    test('应该正确处理空矩阵数据', () => {
      const emptyMatrixData: MatrixData = {
        cells: new Map(),
        selectedCells: new Set(),
        hoveredCell: null,
        focusedCell: null,
        lastUpdate: Date.now(),
      };

      const { result } = renderHook(() =>
        useMatrixRenderOptimization(
          emptyMatrixData,
          mockConfig,
          mockGetCellRenderData
        )
      );

      expect(result.current.visibleCells.length).toBe(33 * 33); // 仍然渲染所有位置
      expect(result.current.metrics.totalCells).toBe(33 * 33);
    });

    test('应该正确处理无效的视口', () => {
      const invalidViewport = { left: -10, top: -10, right: -5, bottom: -5 };
      
      const { result } = renderHook(() =>
        useMatrixRenderOptimization(
          mockMatrixData,
          mockConfig,
          mockGetCellRenderData,
          { 
            enableViewportCulling: true,
            viewport: invalidViewport 
          }
        )
      );

      // 无效视口应该导致没有可见单元格
      expect(result.current.visibleCells.length).toBe(0);
    });
  });
});
