/**
 * MatrixCell 组件单元测试
 * 测试单元格组件的渲染和交互功能
 */

import React from 'react';
import { describe, test, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { MatrixCell } from '../../../components/MatrixCell';
import type { MatrixConfig, CellData, CellRenderData } from '../../../core/matrix/MatrixTypes';

// 模拟配置
const mockConfig: MatrixConfig = {
  mode: 'color-word',
  mainMode: 'color',
  contentMode: 'word',
  isColorMode: true,
  isWordMode: true,
  isNumberMode: false,
  isCoordinateMode: false,
};

// 模拟单元格数据
const mockCellData: CellData = {
  x: 0,
  y: 0,
  color: 'red',
  level: 1,
  group: 'A',
  displayCoordinate: 'A1',
  isActive: true,
  isSelected: false,
  isHovered: false,
  isFocused: false,
};

// 模拟渲染数据
const mockRenderData: CellRenderData = {
  content: '测试',
  className: 'test-cell',
  isInteractive: true,
  style: {
    backgroundColor: '#ff0000',
    color: '#ffffff',
  },
};

describe('MatrixCell', () => {
  describe('基础渲染', () => {
    test('应该正确渲染基础单元格', () => {
      render(
        <MatrixCell
          x={0}
          y={0}
          config={mockConfig}
        />
      );

      const cell = screen.getByRole('gridcell');
      expect(cell).toBeInTheDocument();
      expect(cell).toHaveAttribute('data-x', '0');
      expect(cell).toHaveAttribute('data-y', '0');
      expect(cell).toHaveAttribute('data-cell', '0,0');
    });

    test('应该正确显示单元格内容', () => {
      render(
        <MatrixCell
          x={0}
          y={0}
          config={mockConfig}
          renderData={mockRenderData}
        />
      );

      const cell = screen.getByRole('gridcell');
      expect(cell).toHaveTextContent('测试');
    });

    test('应该正确应用CSS类名', () => {
      render(
        <MatrixCell
          x={0}
          y={0}
          config={mockConfig}
          renderData={mockRenderData}
          className="custom-class"
        />
      );

      const cell = screen.getByRole('gridcell');
      expect(cell).toHaveClass('test-cell');
      expect(cell).toHaveClass('custom-class');
    });

    test('应该正确应用样式', () => {
      render(
        <MatrixCell
          x={0}
          y={0}
          config={mockConfig}
          renderData={mockRenderData}
        />
      );

      const cell = screen.getByRole('gridcell');
      expect(cell).toHaveStyle({
        backgroundColor: '#ff0000',
        color: '#ffffff',
      });
    });
  });

  describe('增强模式', () => {
    test('应该正确应用增强样式', () => {
      render(
        <MatrixCell
          x={0}
          y={0}
          config={mockConfig}
          cellData={mockCellData}
          isEnhanced={true}
        />
      );

      const cell = screen.getByRole('gridcell');
      expect(cell).toHaveStyle({
        borderRadius: '50%',
        zIndex: '10',
      });
    });

    test('增强样式应该覆盖其他样式', () => {
      render(
        <MatrixCell
          x={0}
          y={0}
          config={mockConfig}
          renderData={mockRenderData}
          isEnhanced={true}
          style={{ borderRadius: '0px' }}
        />
      );

      const cell = screen.getByRole('gridcell');
      // 增强样式应该覆盖自定义样式
      expect(cell).toHaveStyle({ borderRadius: '50%' });
    });
  });

  describe('填词模式', () => {
    test('应该正确显示临时词语', () => {
      render(
        <MatrixCell
          x={0}
          y={0}
          config={mockConfig}
          renderData={mockRenderData}
          isWordInputActive={true}
          temporaryWord="临时词"
        />
      );

      const cell = screen.getByRole('gridcell');
      expect(cell).toHaveTextContent('临时词');
    });

    test('应该在填词模式下添加特殊类名', () => {
      render(
        <MatrixCell
          x={0}
          y={0}
          config={mockConfig}
          renderData={mockRenderData}
          isWordInputActive={true}
        />
      );

      const cell = screen.getByRole('gridcell');
      expect(cell).toHaveClass('word-input-active');
    });

    test('临时词语只在颜色+词语模式下显示', () => {
      const numberConfig = { ...mockConfig, mainMode: 'number' as const };
      
      render(
        <MatrixCell
          x={0}
          y={0}
          config={numberConfig}
          renderData={mockRenderData}
          isWordInputActive={true}
          temporaryWord="临时词"
        />
      );

      const cell = screen.getByRole('gridcell');
      // 应该显示原内容而不是临时词语
      expect(cell).toHaveTextContent('测试');
    });
  });

  describe('交互事件', () => {
    test('应该正确处理点击事件', () => {
      const handleClick = vi.fn();
      
      render(
        <MatrixCell
          x={5}
          y={10}
          config={mockConfig}
          onClick={handleClick}
        />
      );

      const cell = screen.getByRole('gridcell');
      fireEvent.click(cell);

      expect(handleClick).toHaveBeenCalledWith(5, 10, expect.any(Object));
    });

    test('应该正确处理双击事件', () => {
      const handleDoubleClick = vi.fn();
      
      render(
        <MatrixCell
          x={3}
          y={7}
          config={mockConfig}
          onDoubleClick={handleDoubleClick}
        />
      );

      const cell = screen.getByRole('gridcell');
      fireEvent.doubleClick(cell);

      expect(handleDoubleClick).toHaveBeenCalledWith(3, 7, expect.any(Object));
    });

    test('应该正确处理鼠标悬停事件', () => {
      const handleMouseEnter = vi.fn();
      const handleMouseLeave = vi.fn();
      
      render(
        <MatrixCell
          x={1}
          y={2}
          config={mockConfig}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        />
      );

      const cell = screen.getByRole('gridcell');
      
      fireEvent.mouseEnter(cell);
      expect(handleMouseEnter).toHaveBeenCalledWith(1, 2, expect.any(Object));
      
      fireEvent.mouseLeave(cell);
      expect(handleMouseLeave).toHaveBeenCalledWith(1, 2, expect.any(Object));
    });

    test('应该正确处理焦点事件', () => {
      const handleFocus = vi.fn();
      const handleBlur = vi.fn();
      
      render(
        <MatrixCell
          x={8}
          y={9}
          config={mockConfig}
          onFocus={handleFocus}
          onBlur={handleBlur}
        />
      );

      const cell = screen.getByRole('gridcell');
      
      fireEvent.focus(cell);
      expect(handleFocus).toHaveBeenCalledWith(8, 9, expect.any(Object));
      
      fireEvent.blur(cell);
      expect(handleBlur).toHaveBeenCalledWith(8, 9, expect.any(Object));
    });
  });

  describe('无障碍性', () => {
    test('应该有正确的ARIA标签', () => {
      render(
        <MatrixCell
          x={2}
          y={3}
          config={mockConfig}
          renderData={mockRenderData}
        />
      );

      const cell = screen.getByRole('gridcell');
      expect(cell).toHaveAttribute('aria-label', '单元格 2,3: 测试');
      expect(cell).toHaveAttribute('tabIndex', '0');
    });

    test('空单元格应该有基础ARIA标签', () => {
      render(
        <MatrixCell
          x={4}
          y={5}
          config={mockConfig}
        />
      );

      const cell = screen.getByRole('gridcell');
      expect(cell).toHaveAttribute('aria-label', '单元格 4,5');
    });
  });

  describe('边界情况', () => {
    test('应该正确处理空的渲染数据', () => {
      render(
        <MatrixCell
          x={0}
          y={0}
          config={mockConfig}
          renderData={undefined}
        />
      );

      const cell = screen.getByRole('gridcell');
      expect(cell).toBeInTheDocument();
      expect(cell).toHaveClass('matrix-cell'); // 默认类名
    });

    test('应该正确处理空的单元格数据', () => {
      render(
        <MatrixCell
          x={0}
          y={0}
          config={mockConfig}
          cellData={undefined}
        />
      );

      const cell = screen.getByRole('gridcell');
      expect(cell).toBeInTheDocument();
    });

    test('应该正确处理负坐标', () => {
      render(
        <MatrixCell
          x={-1}
          y={-1}
          config={mockConfig}
        />
      );

      const cell = screen.getByRole('gridcell');
      expect(cell).toHaveAttribute('data-x', '-1');
      expect(cell).toHaveAttribute('data-y', '-1');
    });
  });
});
