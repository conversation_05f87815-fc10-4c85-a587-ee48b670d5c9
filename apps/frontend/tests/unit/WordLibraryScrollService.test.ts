/**
 * 词库滑动服务测试
 * 🎯 核心价值：验证重构后的滑动逻辑是否正确工作
 * 📦 功能范围：测试位置计算、滑动触发、状态管理
 * 🔄 架构设计：单元测试和集成测试
 */

import type { WordLibraryKey } from '@/core/matrix/MatrixTypes';
import { WordLibraryScrollService } from '@/core/services/WordLibraryScrollService';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// ===== 测试工具 =====

/**
 * 创建模拟的DOM元素
 */
const createMockElement = (rect: Partial<DOMRect>, scrollTop = 0, scrollHeight = 1000, clientHeight = 400) => {
  const element = document.createElement('div');

  // 模拟getBoundingClientRect
  vi.spyOn(element, 'getBoundingClientRect').mockReturnValue({
    top: rect.top || 0,
    bottom: rect.bottom || 100,
    left: rect.left || 0,
    right: rect.right || 100,
    width: rect.width || 100,
    height: rect.height || 100,
    x: rect.x || 0,
    y: rect.y || 0,
    toJSON: () => ({})
  } as DOMRect);

  // 模拟滚动属性
  Object.defineProperty(element, 'scrollTop', {
    value: scrollTop,
    writable: true
  });
  Object.defineProperty(element, 'scrollHeight', {
    value: scrollHeight,
    writable: true
  });
  Object.defineProperty(element, 'clientHeight', {
    value: clientHeight,
    writable: true
  });

  // 模拟scrollTo方法
  const scrollToMock = vi.fn();
  element.scrollTo = scrollToMock;

  return { element, scrollToMock };
};

/**
 * 创建模拟的词库元素
 */
const createMockLibraryElement = (libraryKey: WordLibraryKey, rect: Partial<DOMRect>) => {
  const element = document.createElement('div');
  element.setAttribute('data-word-library', libraryKey);

  vi.spyOn(element, 'getBoundingClientRect').mockReturnValue({
    top: rect.top || 0,
    bottom: rect.bottom || 100,
    left: rect.left || 0,
    right: rect.right || 100,
    width: rect.width || 100,
    height: rect.height || 100,
    x: rect.x || 0,
    y: rect.y || 0,
    toJSON: () => ({})
  } as DOMRect);

  return element;
};

// ===== 测试套件 =====

describe('WordLibraryScrollService', () => {
  let service: WordLibraryScrollService;
  let mockContainer: HTMLElement;
  let scrollToMock: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    // 重置DOM
    document.body.innerHTML = '';

    // 获取服务实例
    service = WordLibraryScrollService.getInstance();

    // 创建模拟容器，设置正确的位置信息
    const containerSetup = createMockElement({
      top: 0,
      bottom: 400,
      height: 400,
      width: 300
    }, 0, 1000, 400);
    mockContainer = containerSetup.element;
    scrollToMock = containerSetup.scrollToMock;

    // 添加容器到DOM
    mockContainer.className = 'overflow-y-auto';
    const wrapper = document.createElement('div');
    wrapper.className = 'word-library-manager';
    wrapper.appendChild(mockContainer);
    document.body.appendChild(wrapper);

    // 初始化服务
    service.initializeContainer(mockContainer);
    service.resetScrollState();
  });

  afterEach(() => {
    vi.clearAllMocks();
    document.body.innerHTML = '';
  });

  describe('位置计算逻辑', () => {
    it('应该正确计算不需要滑动的情况（delta = 0）', () => {
      // 创建已在可视区域中心的词库元素
      // 容器: top=0, height=400, center=200
      // 元素: top=150, height=100, center=200 (与容器中心对齐)
      const libraryKey: WordLibraryKey = 'red-1';
      const libraryElement = createMockLibraryElement(libraryKey, {
        top: 150,  // 元素顶部
        bottom: 250, // 元素底部
        height: 100  // 元素高度
      });

      mockContainer.appendChild(libraryElement);

      const result = service.triggerScroll(libraryKey, { debug: true });

      expect(result.success).toBe(false);
      expect(result.delta).toBe(0);
      expect(scrollToMock).not.toHaveBeenCalled();
    });

    it('应该正确计算需要上移的情况（delta < 0）', () => {
      // 创建在可视区域下方的词库元素
      const libraryKey: WordLibraryKey = 'blue-2';
      const libraryElement = createMockLibraryElement(libraryKey, {
        top: 500,  // 在容器下方
        bottom: 600,
        height: 100
      });

      mockContainer.appendChild(libraryElement);

      const result = service.triggerScroll(libraryKey, { debug: true });

      expect(result.success).toBe(true);
      expect(result.delta).toBeGreaterThan(0); // 需要下移（正值）
      expect(scrollToMock).toHaveBeenCalledWith({
        top: expect.any(Number),
        behavior: 'smooth'
      });
    });

    it('应该正确计算需要下移的情况（delta > 0）', () => {
      // 设置容器已滚动到底部
      Object.defineProperty(mockContainer, 'scrollTop', { value: 300, writable: true });

      // 创建在可视区域上方的词库元素
      const libraryKey: WordLibraryKey = 'green-3';
      const libraryElement = createMockLibraryElement(libraryKey, {
        top: -200,  // 在容器上方
        bottom: -100,
        height: 100
      });

      mockContainer.appendChild(libraryElement);

      const result = service.triggerScroll(libraryKey, { debug: true });

      expect(result.success).toBe(true);
      expect(result.delta).toBeLessThan(0); // 需要上移（负值）
      expect(scrollToMock).toHaveBeenCalledWith({
        top: expect.any(Number),
        behavior: 'smooth'
      });
    });
  });

  describe('重复滑动防护', () => {
    it('应该防止对同一词库的重复滑动', () => {
      const libraryKey: WordLibraryKey = 'yellow-4';
      const libraryElement = createMockLibraryElement(libraryKey, {
        top: 500,
        bottom: 600,
        height: 100
      });

      mockContainer.appendChild(libraryElement);

      // 第一次滑动
      const result1 = service.triggerScroll(libraryKey);
      expect(result1.success).toBe(true);
      expect(scrollToMock).toHaveBeenCalledTimes(1);

      // 第二次滑动（应该被跳过）
      const result2 = service.triggerScroll(libraryKey);
      expect(result2.success).toBe(false);
      expect(result2.delta).toBe(0);
      expect(scrollToMock).toHaveBeenCalledTimes(1); // 没有增加
    });

    it('应该允许强制滑动', () => {
      const libraryKey: WordLibraryKey = 'purple-1';
      const libraryElement = createMockLibraryElement(libraryKey, {
        top: 500,
        bottom: 600,
        height: 100
      });

      mockContainer.appendChild(libraryElement);

      // 第一次滑动
      service.triggerScroll(libraryKey);
      expect(scrollToMock).toHaveBeenCalledTimes(1);

      // 强制第二次滑动
      const result = service.triggerScroll(libraryKey, { force: true });
      expect(result.success).toBe(true);
      expect(scrollToMock).toHaveBeenCalledTimes(2);
    });
  });

  describe('状态管理', () => {
    it('应该正确记录和获取当前激活的词库', () => {
      const libraryKey: WordLibraryKey = 'orange-2';

      expect(service.getCurrentActiveLibrary()).toBeNull();

      service.setCurrentActiveLibrary(libraryKey);
      expect(service.getCurrentActiveLibrary()).toBe(libraryKey);

      service.resetScrollState();
      expect(service.getCurrentActiveLibrary()).toBeNull();
    });
  });

  describe('错误处理', () => {
    it('应该处理找不到滚动容器的情况', () => {
      // 移除容器并重新初始化服务
      document.body.innerHTML = '';
      service.initializeContainer(null as any); // 强制设置为null

      const result = service.triggerScroll('red-1' as WordLibraryKey);

      expect(result.success).toBe(false);
      expect(result.error).toContain('未找到滚动容器');
    });

    it('应该处理找不到词库元素的情况', () => {
      const result = service.triggerScroll('nonexistent-library' as WordLibraryKey);

      expect(result.success).toBe(false);
      expect(result.error).toContain('未找到词库元素');
    });
  });
});

describe('滑动逻辑集成测试', () => {
  it('应该模拟完整的词库激活滑动流程', () => {
    // 这个测试模拟用户双击矩阵单元格激活词库的完整流程
    const service = WordLibraryScrollService.getInstance();

    // 设置DOM环境
    const containerSetup = createMockElement({ top: 0, bottom: 400 }, 100, 1000, 400);
    const mockContainer = containerSetup.element;
    const scrollToMock = containerSetup.scrollToMock;

    mockContainer.className = 'overflow-y-auto';
    const wrapper = document.createElement('div');
    wrapper.className = 'word-library-manager';
    wrapper.appendChild(mockContainer);
    document.body.appendChild(wrapper);

    // 创建多个词库元素
    const libraries: Array<{ key: WordLibraryKey; rect: Partial<DOMRect> }> = [
      { key: 'red-1', rect: { top: 50, bottom: 150, height: 100 } },
      { key: 'blue-2', rect: { top: 200, bottom: 300, height: 100 } },
      { key: 'green-3', rect: { top: 600, bottom: 700, height: 100 } }, // 需要滑动
    ];

    libraries.forEach(({ key, rect }) => {
      const element = createMockLibraryElement(key, rect);
      mockContainer.appendChild(element);
    });

    service.initializeContainer(mockContainer);
    service.resetScrollState();

    // 模拟激活green-3词库（需要滑动）
    const result = service.triggerScroll('green-3', { debug: true });

    expect(result.success).toBe(true);
    expect(result.delta).toBeGreaterThan(0); // 需要下移
    expect(scrollToMock).toHaveBeenCalledWith({
      top: expect.any(Number),
      behavior: 'smooth'
    });

    // 验证状态已更新
    expect(service.getCurrentActiveLibrary()).toBe('green-3');
  });
});
