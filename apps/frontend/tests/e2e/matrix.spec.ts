import { test, expect } from '@playwright/test'

test.describe('矩阵应用', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('应该加载主页面', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle(/Cube1 Matrix/)
    
    // 检查矩阵容器是否存在
    await expect(page.locator('[data-testid="matrix-container"]')).toBeVisible()
  })

  test('应该能够与矩阵单元格交互', async ({ page }) => {
    // 等待矩阵加载完成
    await page.waitForSelector('[data-testid="matrix-container"]')
    
    // 点击第一个单元格
    const firstCell = page.locator('[data-testid="cell-0-0"]').first()
    await firstCell.click()
    
    // 验证单元格状态变化
    await expect(firstCell).toHaveClass(/selected/)
  })

  test('应该能够切换不同的数据组', async ({ page }) => {
    // 等待控制面板加载
    await page.waitForSelector('[data-testid="controls-panel"]')
    
    // 切换到B组数据
    const groupSelector = page.locator('[data-testid="group-selector"]')
    await groupSelector.selectOption('B')
    
    // 验证数据组切换成功
    await expect(page.locator('[data-testid="current-group"]')).toContainText('B')
  })

  test('应该能够保存和加载数据', async ({ page }) => {
    // 修改一些单元格
    await page.locator('[data-testid="cell-0-0"]').click()
    await page.locator('[data-testid="cell-1-1"]').click()
    
    // 保存数据
    await page.locator('[data-testid="save-button"]').click()
    
    // 刷新页面
    await page.reload()
    
    // 验证数据是否保持
    await expect(page.locator('[data-testid="cell-0-0"]')).toHaveClass(/selected/)
    await expect(page.locator('[data-testid="cell-1-1"]')).toHaveClass(/selected/)
  })
})
