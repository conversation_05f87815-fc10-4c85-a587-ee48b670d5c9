#!/usr/bin/env tsx

/**
 * 测试环境设置脚本
 * 用于初始化测试环境和清理测试数据
 */

import { execSync } from 'child_process'
import { existsSync, mkdirSync, writeFileSync } from 'fs'
import { join } from 'path'

const TESTS_DIR = join(process.cwd(), 'tests')
const E2E_DIR = join(TESTS_DIR, 'e2e')
const UNIT_DIR = join(TESTS_DIR, 'unit')
const INTEGRATION_DIR = join(TESTS_DIR, 'integration')

function createTestDirectories() {
  console.log('📁 创建测试目录结构...')
  
  const dirs = [TESTS_DIR, E2E_DIR, UNIT_DIR, INTEGRATION_DIR]
  
  dirs.forEach(dir => {
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true })
      console.log(`✅ 创建目录: ${dir}`)
    } else {
      console.log(`⏭️  目录已存在: ${dir}`)
    }
  })
}

function installTestDependencies() {
  console.log('📦 安装测试依赖...')
  
  try {
    execSync('pnpm install', { stdio: 'inherit' })
    console.log('✅ 测试依赖安装完成')
  } catch (error) {
    console.error('❌ 测试依赖安装失败:', error)
    process.exit(1)
  }
}

function setupPlaywright() {
  console.log('🎭 设置 Playwright...')
  
  try {
    execSync('npx playwright install', { stdio: 'inherit' })
    console.log('✅ Playwright 浏览器安装完成')
  } catch (error) {
    console.error('❌ Playwright 设置失败:', error)
    process.exit(1)
  }
}

function runTests() {
  console.log('🧪 运行测试...')
  
  try {
    // 运行单元测试
    console.log('运行单元测试...')
    execSync('pnpm run test', { stdio: 'inherit' })
    
    // 运行 E2E 测试（需要先启动开发服务器）
    console.log('运行 E2E 测试...')
    execSync('pnpm run test:e2e', { stdio: 'inherit' })
    
    console.log('✅ 所有测试通过')
  } catch (error) {
    console.error('❌ 测试失败:', error)
    process.exit(1)
  }
}

function main() {
  console.log('🚀 开始设置测试环境...\n')
  
  createTestDirectories()
  installTestDependencies()
  setupPlaywright()
  
  console.log('\n✅ 测试环境设置完成!')
  console.log('\n可用的测试命令:')
  console.log('  pnpm run test          - 运行单元测试')
  console.log('  pnpm run test:watch    - 监视模式运行单元测试')
  console.log('  pnpm run test:coverage - 运行测试并生成覆盖率报告')
  console.log('  pnpm run test:e2e      - 运行端到端测试')
  console.log('  pnpm run test:e2e:ui   - 运行端到端测试（UI模式）')
  
  // 询问是否立即运行测试
  const shouldRunTests = process.argv.includes('--run-tests')
  if (shouldRunTests) {
    console.log('\n🧪 立即运行测试...')
    runTests()
  }
}

if (require.main === module) {
  main()
}
