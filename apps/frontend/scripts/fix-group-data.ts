/**
 * 修复GroupAData中缺失的颜色定义
 */

import { readFileSync, writeFileSync } from 'fs';

const filePath = 'core/data/GroupAData.ts';

function fixGroupData() {
  let content = readFileSync(filePath, 'utf-8');
  
  // 修复level1Offsets中缺失的颜色
  // 查找所有level1Offsets定义并添加缺失的颜色
  const level1OffsetsRegex = /level1Offsets:\s*\{([^}]+)\}/g;
  
  content = content.replace(level1OffsetsRegex, (match, offsetsContent) => {
    // 如果已经包含white, gray, brown，则跳过
    if (offsetsContent.includes('white:') && offsetsContent.includes('gray:') && offsetsContent.includes('brown:')) {
      return match;
    }
    
    // 提取black的偏移值作为默认值
    const blackMatch = offsetsContent.match(/black:\s*\[([^\]]+)\]/);
    const defaultOffset = blackMatch ? `[${blackMatch[1]}]` : '[0, 0]';
    
    // 添加缺失的颜色
    const newOffsetsContent = offsetsContent.trim();
    const additionalColors = `white: ${defaultOffset}, gray: ${defaultOffset}, brown: ${defaultOffset}`;
    
    return `level1Offsets: {${newOffsetsContent},\n      ${additionalColors}}`;
  });
  
  writeFileSync(filePath, content, 'utf-8');
  console.log('✅ 修复GroupAData完成');
}

fixGroupData();
