/**
 * 类型修复脚本
 * 批量修复TypeScript类型错误
 */

import { readFileSync, writeFileSync } from 'fs';
import { glob } from 'glob';

// 修复规则
const fixes = [
  // 修复测试文件中的配置类型
  {
    pattern: /businessMode: 'color-word'/g,
    replacement: "mode: 'color-word'"
  },
  {
    pattern: /const mockConfig: MatrixConfig = \{[\s\S]*?\}/g,
    replacement: `const mockConfig: MatrixConfig = {
  mode: 'color-word',
  mainMode: 'color',
  contentMode: 'word',
  isColorMode: true,
  isWordMode: true,
  isNumberMode: false,
  isCoordinateMode: false,
}`
  },
  // 修复CellRenderData缺少isInteractive
  {
    pattern: /(const mockRenderData: CellRenderData = \{[\s\S]*?style: \{[\s\S]*?\},)\s*\}/g,
    replacement: '$1\n  isInteractive: true,\n}'
  },
  // 修复MatrixData中的坐标类型
  {
    pattern: /hoveredCell: \{ x: (\d+), y: (\d+) \}/g,
    replacement: 'hoveredCell: "$1,$2"'
  },
  {
    pattern: /focusedCell: \{ x: (\d+), y: (\d+) \}/g,
    replacement: 'focusedCell: "$1,$2"'
  },
  // 修复level类型
  {
    pattern: /level: i % 3 \+ 1/g,
    replacement: 'level: (i % 3 + 1) as DataLevel'
  },
  // 修复warnings属性
  {
    pattern: /warnings: warnings\.length > 0 \? warnings : undefined/g,
    replacement: 'warnings'
  }
];

async function fixFiles() {
  // 获取所有需要修复的文件
  const files = await glob('**/*.{ts,tsx}', {
    ignore: ['node_modules/**', 'dist/**', '.next/**'],
    cwd: process.cwd()
  });

  console.log(`找到 ${files.length} 个文件需要检查`);

  let fixedCount = 0;

  for (const file of files) {
    try {
      let content = readFileSync(file, 'utf-8');
      let hasChanges = false;

      // 应用所有修复规则
      for (const fix of fixes) {
        const newContent = content.replace(fix.pattern, fix.replacement);
        if (newContent !== content) {
          content = newContent;
          hasChanges = true;
        }
      }

      // 如果有变化，写回文件
      if (hasChanges) {
        writeFileSync(file, content, 'utf-8');
        console.log(`✅ 修复: ${file}`);
        fixedCount++;
      }
    } catch (error) {
      console.error(`❌ 处理文件失败: ${file}`, error);
    }
  }

  console.log(`\n修复完成！共修复 ${fixedCount} 个文件`);
}

// 运行修复
fixFiles().catch(console.error);
