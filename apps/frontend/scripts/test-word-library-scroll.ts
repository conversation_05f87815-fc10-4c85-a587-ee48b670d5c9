/**
 * 词库滑动功能测试脚本
 * 🎯 核心价值：测试双击激活填词模式时词库面板滑动功能
 * 📦 功能范围：模拟双击事件、状态变化、滑动触发
 * 🔄 架构设计：端到端测试流程
 */

// 这个脚本用于在浏览器控制台中测试词库滑动功能

declare global {
  interface Window {
    testWordLibraryScroll: {
      simulateDoubleClick: (x: number, y: number) => Promise<void>;
      checkScrollService: () => void;
      checkWordInputStore: () => void;
      checkDOMElements: () => void;
      runFullTest: () => Promise<void>;
    };
  }
}

// 测试工具函数
const testUtils = {
  log: (message: string, type: 'info' | 'success' | 'error' | 'warn' = 'info') => {
    const styles = {
      info: 'color: #3b82f6',
      success: 'color: #10b981',
      error: 'color: #ef4444',
      warn: 'color: #f59e0b'
    };
    console.log(`%c[WordLibraryScrollTest] ${message}`, styles[type]);
  },

  delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  // 获取矩阵数据
  getMatrixData: (x: number, y: number) => {
    // 模拟矩阵数据获取
    const colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'cyan'];
    const color = colors[x % colors.length];
    const level = (y % 4) + 1;
    return { color, level };
  },

  // 检查DOM元素
  checkDOMElements: () => {
    testUtils.log('检查DOM元素...');
    
    const wordLibraryManager = document.querySelector('.word-library-manager');
    const scrollContainer = document.querySelector('.word-library-manager .overflow-y-auto');
    const wordLibraryItems = document.querySelectorAll('[data-word-library]');
    
    testUtils.log(`词库管理器: ${wordLibraryManager ? '✓ 找到' : '✗ 未找到'}`, 
      wordLibraryManager ? 'success' : 'error');
    testUtils.log(`滚动容器: ${scrollContainer ? '✓ 找到' : '✗ 未找到'}`, 
      scrollContainer ? 'success' : 'error');
    testUtils.log(`词库项目: ${wordLibraryItems.length} 个`, 
      wordLibraryItems.length > 0 ? 'success' : 'error');
    
    if (wordLibraryItems.length > 0) {
      testUtils.log('词库项目列表:');
      wordLibraryItems.forEach((item, index) => {
        const libraryKey = item.getAttribute('data-word-library');
        console.log(`  ${index + 1}. ${libraryKey}`);
      });
    }
    
    return {
      wordLibraryManager: !!wordLibraryManager,
      scrollContainer: !!scrollContainer,
      wordLibraryItems: wordLibraryItems.length
    };
  },

  // 检查滑动服务
  checkScrollService: () => {
    testUtils.log('检查滑动服务...');
    
    try {
      // 尝试访问滑动服务
      const result = (window as any).triggerWordLibraryScroll?.('red-1', { debug: true });
      testUtils.log('滑动服务可用', 'success');
      return true;
    } catch (error) {
      testUtils.log(`滑动服务不可用: ${error}`, 'error');
      return false;
    }
  },

  // 检查词库状态
  checkWordInputStore: () => {
    testUtils.log('检查词库状态...');
    
    try {
      // 尝试访问词库状态（这需要在React应用中运行）
      testUtils.log('词库状态检查需要在React应用中运行', 'warn');
      return true;
    } catch (error) {
      testUtils.log(`词库状态检查失败: ${error}`, 'error');
      return false;
    }
  },

  // 模拟双击事件
  simulateDoubleClick: async (x: number, y: number) => {
    testUtils.log(`模拟双击单元格 (${x}, ${y})...`);
    
    // 获取单元格数据
    const { color, level } = testUtils.getMatrixData(x, y);
    const libraryKey = `${color}-${level}`;
    
    testUtils.log(`目标词库: ${libraryKey}`);
    
    // 查找对应的单元格元素
    const cellElement = document.querySelector(`[data-x="${x}"][data-y="${y}"]`);
    if (!cellElement) {
      testUtils.log(`未找到单元格元素 (${x}, ${y})`, 'error');
      return false;
    }
    
    testUtils.log('找到单元格元素', 'success');
    
    // 触发双击事件
    const doubleClickEvent = new MouseEvent('dblclick', {
      bubbles: true,
      cancelable: true,
      view: window
    });
    
    cellElement.dispatchEvent(doubleClickEvent);
    testUtils.log('双击事件已触发', 'success');
    
    // 等待状态更新
    await testUtils.delay(200);
    
    // 检查滑动是否触发
    const targetElement = document.querySelector(`[data-word-library="${libraryKey}"]`);
    if (targetElement) {
      testUtils.log(`找到目标词库元素: ${libraryKey}`, 'success');
      
      // 检查是否有激活样式
      const isActive = targetElement.classList.contains('word-library-active') || 
                      targetElement.querySelector('.word-library-active');
      testUtils.log(`词库激活状态: ${isActive ? '已激活' : '未激活'}`, 
        isActive ? 'success' : 'warn');
    } else {
      testUtils.log(`未找到目标词库元素: ${libraryKey}`, 'error');
    }
    
    return true;
  },

  // 运行完整测试
  runFullTest: async () => {
    testUtils.log('开始完整测试流程...', 'info');
    
    // 1. 检查DOM元素
    const domCheck = testUtils.checkDOMElements();
    if (!domCheck.wordLibraryManager || !domCheck.scrollContainer) {
      testUtils.log('DOM元素检查失败，测试终止', 'error');
      return;
    }
    
    // 2. 检查滑动服务
    const serviceCheck = testUtils.checkScrollService();
    if (!serviceCheck) {
      testUtils.log('滑动服务检查失败，继续测试...', 'warn');
    }
    
    // 3. 测试几个不同的单元格
    const testCases = [
      { x: 0, y: 0 }, // red-1
      { x: 1, y: 1 }, // blue-2
      { x: 2, y: 2 }, // green-3
      { x: 3, y: 3 }, // yellow-4
    ];
    
    for (const testCase of testCases) {
      testUtils.log(`\n--- 测试用例: (${testCase.x}, ${testCase.y}) ---`);
      await testUtils.simulateDoubleClick(testCase.x, testCase.y);
      await testUtils.delay(1000); // 等待滑动完成
    }
    
    testUtils.log('\n完整测试流程结束', 'success');
  }
};

// 将测试工具暴露到全局
if (typeof window !== 'undefined') {
  window.testWordLibraryScroll = {
    simulateDoubleClick: testUtils.simulateDoubleClick,
    checkScrollService: testUtils.checkScrollService,
    checkWordInputStore: testUtils.checkWordInputStore,
    checkDOMElements: testUtils.checkDOMElements,
    runFullTest: testUtils.runFullTest
  };
  
  console.log('%c[WordLibraryScrollTest] 测试工具已加载', 'color: #10b981; font-weight: bold');
  console.log('使用方法:');
  console.log('  window.testWordLibraryScroll.checkDOMElements() - 检查DOM元素');
  console.log('  window.testWordLibraryScroll.simulateDoubleClick(0, 0) - 模拟双击');
  console.log('  window.testWordLibraryScroll.runFullTest() - 运行完整测试');
}

export default testUtils;
