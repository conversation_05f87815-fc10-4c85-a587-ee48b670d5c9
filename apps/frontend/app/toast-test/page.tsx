/**
 * Toast系统测试页面
 * 用于验证新的toast系统是否正常工作
 */

'use client';

import React from 'react';
import { toast } from '@/core/ui/ToastStore';
import Button from '@/components/ui/Button';

export default function ToastTestPage() {
  const handleShowSuccess = () => {
    toast.success('操作成功！这是一个成功消息。');
  };

  const handleShowError = () => {
    toast.error('操作失败！这是一个错误消息。');
  };

  const handleShowWarning = () => {
    toast.warning('注意！这是一个警告消息。');
  };

  const handleShowInfo = () => {
    toast.info('提示：这是一个信息消息。');
  };

  const handleShowCenter = () => {
    toast.warning('请先填入词语', {
      position: 'center',
      duration: 3000
    });
  };

  const handleShowCustom = () => {
    toast.show('这是一个自定义消息', 'info', {
      duration: 10000,
      closable: true,
      className: 'custom-toast'
    });
  };

  const handleShowMultiple = () => {
    toast.success('第一个消息');
    setTimeout(() => toast.warning('第二个消息'), 500);
    setTimeout(() => toast.error('第三个消息'), 1000);
    setTimeout(() => toast.info('第四个消息'), 1500);
  };

  const handleShowPositions = () => {
    toast.info('右上角消息', { position: 'top-right' });
    setTimeout(() => toast.success('左上角消息', { position: 'top-left' }), 300);
    setTimeout(() => toast.warning('中心消息', { position: 'center' }), 600);
    setTimeout(() => toast.error('右下角消息', { position: 'bottom-right' }), 900);
  };

  const handleClearAll = () => {
    toast.clear();
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">Toast系统测试</h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">基础Toast类型</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <Button onClick={handleShowSuccess} variant="primary">
              成功消息
            </Button>
            <Button onClick={handleShowError} variant="danger">
              错误消息
            </Button>
            <Button onClick={handleShowWarning} variant="warning">
              警告消息
            </Button>
            <Button onClick={handleShowInfo} variant="secondary">
              信息消息
            </Button>
          </div>

          <h2 className="text-xl font-semibold mb-4">位置测试</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            <Button onClick={handleShowCenter} variant="warning">
              中心提示（矩阵专用）
            </Button>
            <Button onClick={handleShowPositions} variant="outline">
              多位置展示
            </Button>
          </div>

          <h2 className="text-xl font-semibold mb-4">高级功能</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <Button onClick={handleShowCustom} variant="outline">
              自定义配置
            </Button>
            <Button onClick={handleShowMultiple} variant="outline">
              多个消息
            </Button>
            <Button onClick={handleClearAll} variant="outline">
              清空所有
            </Button>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold mb-2">设计说明：</h3>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li><strong>黑白灰简约设计</strong>：采用现代化的单色配色方案</li>
              <li><strong>中心提示</strong>：重要提示（如"请先填入词语"）在页面中心显示</li>
              <li><strong>多位置支持</strong>：支持右上角、左上角、中心、右下角等位置</li>
              <li><strong>自动消失</strong>：成功3秒，信息3秒，警告4秒，错误5秒</li>
              <li><strong>手动关闭</strong>：可以点击×按钮手动关闭</li>
              <li><strong>动画效果</strong>：流畅的进入和退出动画</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
