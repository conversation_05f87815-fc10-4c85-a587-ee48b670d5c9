/**
 * 布局测试页面
 * 用于验证Controls组件的布局修改是否正确
 */

'use client';

import Controls from '@/components/Controls';

export default function LayoutTestPage() {
  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-4 text-center">控制面板布局测试</h1>
        
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="h-[600px]">
            <Controls />
          </div>
        </div>
        
        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <h2 className="font-semibold mb-2">测试说明：</h2>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>顶部的模式选择器应该固定显示</li>
            <li>词库管理标题和操作按钮应该固定显示</li>
            <li>只有29个词库列表应该可以滚动</li>
            <li>底部状态栏应该固定显示</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
