{"name": "cube1-matrix", "version": "2.0.0", "private": true, "description": "数据驱动的33×33矩阵系统 - 激进重构版本", "scripts": {"dev": "next dev -H 0.0.0.0 -p 4096", "build": "next build", "start": "next start -H 0.0.0.0 -p 4096", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "clean": "rm -rf .next", "dev:clean": "pnpm run clean && pnpm run dev", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed"}, "dependencies": {"clsx": "^2.1.1", "immer": "^10.1.1", "next": "15.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@playwright/test": "^1.54.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^20.19.7", "@types/react": "^18.3.23", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^8", "eslint-config-next": "^15.3.5", "jsdom": "^26.0.0", "postcss": "^8", "prettier": "^3.3.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vitest": "^3.2.4"}}