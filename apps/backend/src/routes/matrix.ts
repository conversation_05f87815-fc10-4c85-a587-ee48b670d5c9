/**
 * 矩阵相关API路由
 * 🎯 核心价值：提供高性能的矩阵数据API，支持实时更新和批量操作
 * 📦 功能范围：矩阵数据CRUD、实时同步、批量操作、数据验证
 * 🔄 架构设计：RESTful API + WebSocket，支持缓存和分页
 */

import { Router } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { MatrixService } from '../services/MatrixService';
import { CacheService } from '../services/CacheService';
import { ValidationService } from '../services/ValidationService';
import { RateLimitService } from '../middleware/RateLimitService';
import { AuthService } from '../middleware/AuthService';

const router = Router();

// ===== 中间件 =====

/**
 * 验证请求参数
 */
const validateRequest = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array(),
      message: '请求参数验证失败'
    });
  }
  next();
};

/**
 * 缓存中间件
 */
const cacheMiddleware = (duration: number = 300) => {
  return async (req: any, res: any, next: any) => {
    const cacheKey = `api:${req.method}:${req.originalUrl}`;
    
    try {
      const cached = await CacheService.get(cacheKey);
      if (cached) {
        return res.json(cached);
      }
      
      // 保存原始的json方法
      const originalJson = res.json;
      res.json = function(data: any) {
        // 缓存响应数据
        CacheService.set(cacheKey, data, duration);
        return originalJson.call(this, data);
      };
      
      next();
    } catch (error) {
      next();
    }
  };
};

// ===== 矩阵数据路由 =====

/**
 * 获取矩阵数据
 * GET /api/matrix/:groupId
 */
router.get(
  '/:groupId',
  [
    param('groupId').isString().isLength({ min: 1, max: 10 }),
    query('includeMetadata').optional().isBoolean(),
    query('format').optional().isIn(['json', 'csv', 'xml']),
  ],
  validateRequest,
  cacheMiddleware(300), // 5分钟缓存
  async (req, res) => {
    try {
      const { groupId } = req.params;
      const { includeMetadata = true, format = 'json' } = req.query;

      const matrixData = await MatrixService.getMatrixData(groupId, {
        includeMetadata: includeMetadata === 'true',
        format: format as string
      });

      res.json({
        success: true,
        data: matrixData,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '获取矩阵数据失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
);

/**
 * 批量获取矩阵数据
 * POST /api/matrix/batch
 */
router.post(
  '/batch',
  [
    body('groupIds').isArray().isLength({ min: 1, max: 20 }),
    body('groupIds.*').isString(),
    body('options.includeMetadata').optional().isBoolean(),
    body('options.format').optional().isIn(['json', 'csv']),
  ],
  validateRequest,
  RateLimitService.createLimiter({ windowMs: 60000, max: 10 }), // 每分钟10次
  async (req, res) => {
    try {
      const { groupIds, options = {} } = req.body;

      const results = await MatrixService.getBatchMatrixData(groupIds, options);

      res.json({
        success: true,
        data: results,
        count: results.length,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '批量获取矩阵数据失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
);

/**
 * 更新矩阵数据
 * PUT /api/matrix/:groupId
 */
router.put(
  '/:groupId',
  [
    param('groupId').isString().isLength({ min: 1, max: 10 }),
    body('data').isObject(),
    body('data.cells').isArray(),
    body('metadata').optional().isObject(),
  ],
  validateRequest,
  AuthService.requireAuth,
  async (req, res) => {
    try {
      const { groupId } = req.params;
      const { data, metadata } = req.body;

      // 验证数据
      const validation = await ValidationService.validateMatrixData(data);
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: validation.errors
        });
      }

      const result = await MatrixService.updateMatrixData(groupId, data, metadata);

      // 清除相关缓存
      await CacheService.deletePattern(`api:*:*matrix*${groupId}*`);

      res.json({
        success: true,
        data: result,
        message: '矩阵数据更新成功'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '更新矩阵数据失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
);

/**
 * 部分更新矩阵数据
 * PATCH /api/matrix/:groupId/cells
 */
router.patch(
  '/:groupId/cells',
  [
    param('groupId').isString().isLength({ min: 1, max: 10 }),
    body('updates').isArray().isLength({ min: 1, max: 100 }),
    body('updates.*.x').isInt({ min: 0, max: 32 }),
    body('updates.*.y').isInt({ min: 0, max: 32 }),
    body('updates.*.data').isObject(),
  ],
  validateRequest,
  AuthService.requireAuth,
  RateLimitService.createLimiter({ windowMs: 60000, max: 30 }), // 每分钟30次
  async (req, res) => {
    try {
      const { groupId } = req.params;
      const { updates } = req.body;

      const result = await MatrixService.updateMatrixCells(groupId, updates);

      // 清除相关缓存
      await CacheService.deletePattern(`api:*:*matrix*${groupId}*`);

      res.json({
        success: true,
        data: result,
        updatedCount: updates.length,
        message: '单元格数据更新成功'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '更新单元格数据失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
);

/**
 * 删除矩阵数据
 * DELETE /api/matrix/:groupId
 */
router.delete(
  '/:groupId',
  [
    param('groupId').isString().isLength({ min: 1, max: 10 }),
    query('confirm').equals('true'),
  ],
  validateRequest,
  AuthService.requireAuth,
  AuthService.requireRole('admin'),
  async (req, res) => {
    try {
      const { groupId } = req.params;

      await MatrixService.deleteMatrixData(groupId);

      // 清除相关缓存
      await CacheService.deletePattern(`api:*:*matrix*${groupId}*`);

      res.json({
        success: true,
        message: '矩阵数据删除成功'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '删除矩阵数据失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
);

// ===== 矩阵统计路由 =====

/**
 * 获取矩阵统计信息
 * GET /api/matrix/:groupId/stats
 */
router.get(
  '/:groupId/stats',
  [
    param('groupId').isString().isLength({ min: 1, max: 10 }),
    query('period').optional().isIn(['day', 'week', 'month']),
  ],
  validateRequest,
  cacheMiddleware(600), // 10分钟缓存
  async (req, res) => {
    try {
      const { groupId } = req.params;
      const { period = 'day' } = req.query;

      const stats = await MatrixService.getMatrixStats(groupId, period as string);

      res.json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '获取统计信息失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
);

/**
 * 获取矩阵数据导出
 * GET /api/matrix/:groupId/export
 */
router.get(
  '/:groupId/export',
  [
    param('groupId').isString().isLength({ min: 1, max: 10 }),
    query('format').isIn(['json', 'csv', 'excel']),
    query('includeHistory').optional().isBoolean(),
  ],
  validateRequest,
  AuthService.requireAuth,
  RateLimitService.createLimiter({ windowMs: 300000, max: 5 }), // 每5分钟5次
  async (req, res) => {
    try {
      const { groupId } = req.params;
      const { format, includeHistory = false } = req.query;

      const exportData = await MatrixService.exportMatrixData(groupId, {
        format: format as string,
        includeHistory: includeHistory === 'true'
      });

      // 设置下载头
      const filename = `matrix-${groupId}-${new Date().toISOString().split('T')[0]}.${format}`;
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      
      if (format === 'json') {
        res.setHeader('Content-Type', 'application/json');
        res.send(JSON.stringify(exportData, null, 2));
      } else if (format === 'csv') {
        res.setHeader('Content-Type', 'text/csv');
        res.send(exportData);
      } else if (format === 'excel') {
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.send(exportData);
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '导出数据失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
);

export default router;
