/**
 * 词库相关API路由
 * 🎯 核心价值：提供高效的词库管理API，支持智能搜索和批量操作
 * 📦 功能范围：词库CRUD、搜索建议、验证、统计分析
 * 🔄 架构设计：RESTful API，支持全文搜索和智能推荐
 */

import { Router } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { WordLibraryService } from '../services/WordLibraryService';
import { SearchService } from '../services/SearchService';
import { CacheService } from '../services/CacheService';
import { RateLimitService } from '../middleware/RateLimitService';
import { AuthService } from '../middleware/AuthService';

const router = Router();

// ===== 中间件 =====

const validateRequest = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array(),
      message: '请求参数验证失败'
    });
  }
  next();
};

// ===== 词库管理路由 =====

/**
 * 获取词库列表
 * GET /api/wordLibrary
 */
router.get(
  '/',
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('color').optional().isString(),
    query('level').optional().isInt({ min: 1, max: 3 }),
    query('search').optional().isString(),
  ],
  validateRequest,
  async (req, res) => {
    try {
      const {
        page = 1,
        limit = 20,
        color,
        level,
        search
      } = req.query;

      const options = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        color: color as string,
        level: level ? parseInt(level as string) : undefined,
        search: search as string
      };

      const result = await WordLibraryService.getWordLibraries(options);

      res.json({
        success: true,
        data: result.libraries,
        pagination: {
          page: options.page,
          limit: options.limit,
          total: result.total,
          pages: Math.ceil(result.total / options.limit)
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '获取词库列表失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
);

/**
 * 获取特定词库
 * GET /api/wordLibrary/:libraryKey
 */
router.get(
  '/:libraryKey',
  [
    param('libraryKey').matches(/^[a-z]+-[1-3]$/),
    query('includeStats').optional().isBoolean(),
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { libraryKey } = req.params;
      const { includeStats = false } = req.query;

      const library = await WordLibraryService.getWordLibrary(libraryKey);
      
      if (!library) {
        return res.status(404).json({
          success: false,
          message: '词库不存在'
        });
      }

      let stats = null;
      if (includeStats === 'true') {
        stats = await WordLibraryService.getLibraryStats(libraryKey);
      }

      res.json({
        success: true,
        data: {
          library,
          stats
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '获取词库失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
);

/**
 * 添加词语到词库
 * POST /api/wordLibrary/:libraryKey/words
 */
router.post(
  '/:libraryKey/words',
  [
    param('libraryKey').matches(/^[a-z]+-[1-3]$/),
    body('text').isString().isLength({ min: 1, max: 10 }),
    body('metadata').optional().isObject(),
  ],
  validateRequest,
  AuthService.requireAuth,
  RateLimitService.createLimiter({ windowMs: 60000, max: 20 }), // 每分钟20次
  async (req, res) => {
    try {
      const { libraryKey } = req.params;
      const { text, metadata = {} } = req.body;

      // 验证词语
      const validation = await WordLibraryService.validateWord(text, libraryKey);
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          message: '词语验证失败',
          errors: validation.errors
        });
      }

      const word = await WordLibraryService.addWord(libraryKey, text, metadata);

      // 清除相关缓存
      await CacheService.deletePattern(`wordLibrary:${libraryKey}:*`);

      res.status(201).json({
        success: true,
        data: word,
        message: '词语添加成功'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '添加词语失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
);

/**
 * 批量添加词语
 * POST /api/wordLibrary/:libraryKey/words/batch
 */
router.post(
  '/:libraryKey/words/batch',
  [
    param('libraryKey').matches(/^[a-z]+-[1-3]$/),
    body('words').isArray().isLength({ min: 1, max: 50 }),
    body('words.*.text').isString().isLength({ min: 1, max: 10 }),
    body('words.*.metadata').optional().isObject(),
  ],
  validateRequest,
  AuthService.requireAuth,
  RateLimitService.createLimiter({ windowMs: 300000, max: 5 }), // 每5分钟5次
  async (req, res) => {
    try {
      const { libraryKey } = req.params;
      const { words } = req.body;

      const result = await WordLibraryService.addWordsBatch(libraryKey, words);

      // 清除相关缓存
      await CacheService.deletePattern(`wordLibrary:${libraryKey}:*`);

      res.status(201).json({
        success: true,
        data: result,
        message: `成功添加 ${result.successful.length} 个词语`
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '批量添加词语失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
);

/**
 * 更新词语
 * PUT /api/wordLibrary/:libraryKey/words/:wordId
 */
router.put(
  '/:libraryKey/words/:wordId',
  [
    param('libraryKey').matches(/^[a-z]+-[1-3]$/),
    param('wordId').isString(),
    body('text').optional().isString().isLength({ min: 1, max: 10 }),
    body('metadata').optional().isObject(),
  ],
  validateRequest,
  AuthService.requireAuth,
  async (req, res) => {
    try {
      const { libraryKey, wordId } = req.params;
      const updates = req.body;

      const word = await WordLibraryService.updateWord(libraryKey, wordId, updates);

      if (!word) {
        return res.status(404).json({
          success: false,
          message: '词语不存在'
        });
      }

      // 清除相关缓存
      await CacheService.deletePattern(`wordLibrary:${libraryKey}:*`);

      res.json({
        success: true,
        data: word,
        message: '词语更新成功'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '更新词语失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
);

/**
 * 删除词语
 * DELETE /api/wordLibrary/:libraryKey/words/:wordId
 */
router.delete(
  '/:libraryKey/words/:wordId',
  [
    param('libraryKey').matches(/^[a-z]+-[1-3]$/),
    param('wordId').isString(),
  ],
  validateRequest,
  AuthService.requireAuth,
  async (req, res) => {
    try {
      const { libraryKey, wordId } = req.params;

      const success = await WordLibraryService.deleteWord(libraryKey, wordId);

      if (!success) {
        return res.status(404).json({
          success: false,
          message: '词语不存在'
        });
      }

      // 清除相关缓存
      await CacheService.deletePattern(`wordLibrary:${libraryKey}:*`);

      res.json({
        success: true,
        message: '词语删除成功'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '删除词语失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
);

// ===== 搜索和建议路由 =====

/**
 * 搜索词语
 * GET /api/wordLibrary/search
 */
router.get(
  '/search',
  [
    query('q').isString().isLength({ min: 1, max: 20 }),
    query('libraries').optional().isString(),
    query('limit').optional().isInt({ min: 1, max: 50 }),
    query('fuzzy').optional().isBoolean(),
  ],
  validateRequest,
  async (req, res) => {
    try {
      const {
        q: query,
        libraries,
        limit = 20,
        fuzzy = false
      } = req.query;

      const libraryKeys = libraries ? (libraries as string).split(',') : undefined;

      const results = await SearchService.searchWords(query as string, {
        libraries: libraryKeys,
        limit: parseInt(limit as string),
        fuzzy: fuzzy === 'true'
      });

      res.json({
        success: true,
        data: results,
        query: query,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '搜索失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
);

/**
 * 获取词语建议
 * GET /api/wordLibrary/suggestions
 */
router.get(
  '/suggestions',
  [
    query('input').optional().isString().isLength({ max: 10 }),
    query('color').optional().isString(),
    query('level').optional().isInt({ min: 1, max: 3 }),
    query('context').optional().isString(),
    query('limit').optional().isInt({ min: 1, max: 20 }),
  ],
  validateRequest,
  async (req, res) => {
    try {
      const {
        input = '',
        color,
        level,
        context,
        limit = 10
      } = req.query;

      const suggestions = await SearchService.getSuggestions(input as string, {
        color: color as string,
        level: level ? parseInt(level as string) : undefined,
        context: context as string,
        limit: parseInt(limit as string)
      });

      res.json({
        success: true,
        data: suggestions,
        input: input,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '获取建议失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
);

/**
 * 验证词语
 * POST /api/wordLibrary/validate
 */
router.post(
  '/validate',
  [
    body('text').isString().isLength({ min: 1, max: 10 }),
    body('libraryKey').matches(/^[a-z]+-[1-3]$/),
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { text, libraryKey } = req.body;

      const validation = await WordLibraryService.validateWord(text, libraryKey);

      res.json({
        success: true,
        data: validation,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '验证失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
);

export default router;
