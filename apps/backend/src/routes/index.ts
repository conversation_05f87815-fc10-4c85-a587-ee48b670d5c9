/**
 * API路由主入口
 * 🎯 核心价值：统一的API路由管理，提供清晰的接口结构和版本控制
 * 📦 功能范围：路由注册、中间件配置、错误处理、API文档
 * 🔄 架构设计：模块化路由，支持版本控制和中间件链
 */

import { Router } from 'express';
import { rateLimit } from 'express-rate-limit';
import cors from 'cors';
import helmet from 'helmet';

// 导入路由模块
import healthRoutes from './health';
import matrixRoutes from './matrix';
import wordLibraryRoutes from './wordLibrary';

// 导入中间件
import { LoggingService } from '../middleware/LoggingService';
import { ErrorHandlerService } from '../middleware/ErrorHandlerService';
import { AuthService } from '../middleware/AuthService';

const router = Router();

// ===== 全局中间件 =====

/**
 * 安全中间件
 */
router.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false,
}));

/**
 * CORS配置
 */
router.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count']
}));

/**
 * 全局限流
 */
const globalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 每个IP最多1000次请求
  message: {
    success: false,
    message: '请求过于频繁，请稍后再试',
    retryAfter: '15分钟'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

router.use(globalLimiter);

/**
 * 请求日志
 */
router.use(LoggingService.requestLogger);

/**
 * 请求解析
 */
router.use(express.json({ limit: '10mb' }));
router.use(express.urlencoded({ extended: true, limit: '10mb' }));

// ===== API根路径 =====

/**
 * API根路径信息
 * GET /api
 */
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Cube1 Matrix API',
    version: process.env.APP_VERSION || '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/api/health',
      matrix: '/api/matrix',
      wordLibrary: '/api/wordLibrary',
      docs: '/api/docs'
    },
    documentation: {
      swagger: '/api/docs',
      postman: '/api/docs/postman',
      openapi: '/api/docs/openapi.json'
    }
  });
});

// ===== 路由注册 =====

/**
 * 健康检查路由 (无需认证)
 */
router.use('/health', healthRoutes);

/**
 * 矩阵数据路由
 */
router.use('/matrix', matrixRoutes);

/**
 * 词库管理路由
 */
router.use('/wordLibrary', wordLibraryRoutes);

// ===== API文档路由 =====

/**
 * API文档
 * GET /api/docs
 */
router.get('/docs', (req, res) => {
  res.json({
    success: true,
    message: 'API文档',
    version: process.env.APP_VERSION || '1.0.0',
    documentation: {
      description: 'Cube1 Matrix API提供矩阵数据管理和词库管理功能',
      baseUrl: `${req.protocol}://${req.get('host')}/api`,
      authentication: 'Bearer Token (JWT)',
      rateLimit: {
        global: '1000 requests per 15 minutes per IP',
        authenticated: '更高的限制根据具体端点而定'
      },
      endpoints: {
        health: {
          path: '/health',
          description: '系统健康检查',
          methods: ['GET'],
          authentication: false
        },
        matrix: {
          path: '/matrix',
          description: '矩阵数据管理',
          methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
          authentication: 'GET请求无需认证，其他需要认证'
        },
        wordLibrary: {
          path: '/wordLibrary',
          description: '词库管理',
          methods: ['GET', 'POST', 'PUT', 'DELETE'],
          authentication: 'GET请求无需认证，其他需要认证'
        }
      }
    },
    examples: {
      getMatrix: {
        url: '/api/matrix/groupA',
        method: 'GET',
        description: '获取groupA的矩阵数据'
      },
      addWord: {
        url: '/api/wordLibrary/red-1/words',
        method: 'POST',
        body: {
          text: '示例词语',
          metadata: {}
        },
        description: '向red-1词库添加词语'
      }
    }
  });
});

/**
 * OpenAPI规范
 * GET /api/docs/openapi.json
 */
router.get('/docs/openapi.json', (req, res) => {
  const openApiSpec = {
    openapi: '3.0.0',
    info: {
      title: 'Cube1 Matrix API',
      version: process.env.APP_VERSION || '1.0.0',
      description: '矩阵数据和词库管理API',
      contact: {
        name: 'API Support',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: `${req.protocol}://${req.get('host')}/api`,
        description: '当前服务器'
      }
    ],
    paths: {
      '/health': {
        get: {
          summary: '健康检查',
          tags: ['Health'],
          responses: {
            200: {
              description: '系统健康',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      status: { type: 'string' },
                      timestamp: { type: 'string' },
                      uptime: { type: 'number' }
                    }
                  }
                }
              }
            }
          }
        }
      },
      '/matrix/{groupId}': {
        get: {
          summary: '获取矩阵数据',
          tags: ['Matrix'],
          parameters: [
            {
              name: 'groupId',
              in: 'path',
              required: true,
              schema: { type: 'string' }
            }
          ],
          responses: {
            200: {
              description: '矩阵数据',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      success: { type: 'boolean' },
                      data: { type: 'object' }
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      }
    }
  };

  res.json(openApiSpec);
});

// ===== 错误处理 =====

/**
 * 404处理
 */
router.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    path: req.originalUrl,
    method: req.method,
    timestamp: new Date().toISOString(),
    availableEndpoints: [
      '/api/health',
      '/api/matrix',
      '/api/wordLibrary',
      '/api/docs'
    ]
  });
});

/**
 * 全局错误处理
 */
router.use(ErrorHandlerService.handleError);

// ===== 导出 =====

export default router;
