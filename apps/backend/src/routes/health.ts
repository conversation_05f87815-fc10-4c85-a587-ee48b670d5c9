/**
 * 健康检查和系统状态API路由
 * 🎯 核心价值：提供统一的系统健康监控，支持多层级检查和性能指标
 * 📦 功能范围：基础健康检查、详细状态、性能指标、依赖检查
 * 🔄 架构设计：分层检查，支持快速响应和详细诊断
 */

import { Router } from 'express';
import { query, validationResult } from 'express-validator';
import { HealthService } from '../services/HealthService';
import { MetricsService } from '../services/MetricsService';
import { DatabaseService } from '../services/DatabaseService';
import { CacheService } from '../services/CacheService';

const router = Router();

// ===== 基础健康检查 =====

/**
 * 基础健康检查
 * GET /api/health
 */
router.get('/', async (req, res) => {
  try {
    const startTime = Date.now();
    
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.APP_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      responseTime: Date.now() - startTime
    };

    res.json(health);
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 详细健康检查
 * GET /api/health/detailed
 */
router.get('/detailed', async (req, res) => {
  try {
    const startTime = Date.now();
    
    // 并行检查各个组件
    const [
      databaseHealth,
      cacheHealth,
      memoryUsage,
      diskUsage
    ] = await Promise.allSettled([
      HealthService.checkDatabase(),
      HealthService.checkCache(),
      HealthService.getMemoryUsage(),
      HealthService.getDiskUsage()
    ]);

    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.APP_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      responseTime: Date.now() - startTime,
      components: {
        database: databaseHealth.status === 'fulfilled' ? databaseHealth.value : { status: 'error', error: databaseHealth.reason },
        cache: cacheHealth.status === 'fulfilled' ? cacheHealth.value : { status: 'error', error: cacheHealth.reason },
        memory: memoryUsage.status === 'fulfilled' ? memoryUsage.value : { status: 'error', error: memoryUsage.reason },
        disk: diskUsage.status === 'fulfilled' ? diskUsage.value : { status: 'error', error: diskUsage.reason }
      }
    };

    // 检查是否有组件不健康
    const hasUnhealthyComponent = Object.values(health.components).some(
      (component: any) => component.status === 'error' || component.status === 'unhealthy'
    );

    if (hasUnhealthyComponent) {
      health.status = 'degraded';
      res.status(503);
    }

    res.json(health);
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 就绪检查 (Kubernetes readiness probe)
 * GET /api/health/ready
 */
router.get('/ready', async (req, res) => {
  try {
    const isReady = await HealthService.checkReadiness();
    
    if (isReady) {
      res.json({
        status: 'ready',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(503).json({
        status: 'not-ready',
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    res.status(503).json({
      status: 'not-ready',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 存活检查 (Kubernetes liveness probe)
 * GET /api/health/live
 */
router.get('/live', async (req, res) => {
  try {
    const isAlive = await HealthService.checkLiveness();
    
    if (isAlive) {
      res.json({
        status: 'alive',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(503).json({
        status: 'dead',
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    res.status(503).json({
      status: 'dead',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

// ===== 性能指标 =====

/**
 * 系统指标
 * GET /api/health/metrics
 */
router.get('/metrics', 
  [
    query('format').optional().isIn(['json', 'prometheus']),
    query('period').optional().isIn(['1m', '5m', '15m', '1h']),
  ],
  async (req, res) => {
    try {
      const { format = 'json', period = '5m' } = req.query;

      const metrics = await MetricsService.getMetrics(period as string);

      if (format === 'prometheus') {
        res.setHeader('Content-Type', 'text/plain');
        res.send(MetricsService.formatPrometheus(metrics));
      } else {
        res.json({
          success: true,
          data: metrics,
          period: period,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '获取指标失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
);

/**
 * 应用指标
 * GET /api/health/app-metrics
 */
router.get('/app-metrics', async (req, res) => {
  try {
    const appMetrics = await MetricsService.getApplicationMetrics();

    res.json({
      success: true,
      data: appMetrics,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取应用指标失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

// ===== 依赖检查 =====

/**
 * 数据库连接检查
 * GET /api/health/database
 */
router.get('/database', async (req, res) => {
  try {
    const dbHealth = await HealthService.checkDatabase();
    
    if (dbHealth.status === 'healthy') {
      res.json(dbHealth);
    } else {
      res.status(503).json(dbHealth);
    }
  } catch (error) {
    res.status(503).json({
      status: 'error',
      message: '数据库检查失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 缓存连接检查
 * GET /api/health/cache
 */
router.get('/cache', async (req, res) => {
  try {
    const cacheHealth = await HealthService.checkCache();
    
    if (cacheHealth.status === 'healthy') {
      res.json(cacheHealth);
    } else {
      res.status(503).json(cacheHealth);
    }
  } catch (error) {
    res.status(503).json({
      status: 'error',
      message: '缓存检查失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    });
  }
});

// ===== 系统信息 =====

/**
 * 系统信息
 * GET /api/health/info
 */
router.get('/info', async (req, res) => {
  try {
    const systemInfo = {
      application: {
        name: 'Cube1 Matrix API',
        version: process.env.APP_VERSION || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        startTime: new Date(Date.now() - process.uptime() * 1000).toISOString(),
        uptime: process.uptime()
      },
      runtime: {
        node: process.version,
        platform: process.platform,
        arch: process.arch,
        pid: process.pid
      },
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      features: {
        matrix: true,
        wordLibrary: true,
        cache: Boolean(process.env.REDIS_URL),
        database: Boolean(process.env.DATABASE_URL),
        auth: Boolean(process.env.JWT_SECRET)
      }
    };

    res.json({
      success: true,
      data: systemInfo,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取系统信息失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 配置检查
 * GET /api/health/config
 */
router.get('/config', async (req, res) => {
  try {
    const configStatus = {
      database: {
        configured: Boolean(process.env.DATABASE_URL),
        type: process.env.DB_TYPE || 'unknown'
      },
      cache: {
        configured: Boolean(process.env.REDIS_URL),
        type: 'redis'
      },
      auth: {
        configured: Boolean(process.env.JWT_SECRET),
        provider: process.env.AUTH_PROVIDER || 'jwt'
      },
      storage: {
        configured: Boolean(process.env.STORAGE_PATH),
        type: process.env.STORAGE_TYPE || 'local'
      },
      logging: {
        level: process.env.LOG_LEVEL || 'info',
        format: process.env.LOG_FORMAT || 'json'
      }
    };

    res.json({
      success: true,
      data: configStatus,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取配置状态失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

export default router;
