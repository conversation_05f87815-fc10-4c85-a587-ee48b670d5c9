"""
主应用测试
"""
import pytest
from fastapi.testclient import TestClient


def test_read_root(client: TestClient):
    """测试根路径"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data


def test_health_check(client: TestClient):
    """测试健康检查"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"


def test_api_docs_accessible(client: TestClient):
    """测试API文档可访问"""
    response = client.get("/docs")
    assert response.status_code == 200
