{"name": "cube1-group-monorepo", "version": "1.0.1", "private": true, "description": "Cube1 Group Monorepo - 前端和后端应用的统一工作区", "scripts": {"dev": "turbo dev", "build": "turbo build", "lint": "turbo lint", "test": "turbo test", "clean": "turbo clean", "dev:frontend": "pnpm --filter cube1-matrix dev", "build:frontend": "pnpm --filter cube1-matrix build", "test:frontend": "pnpm --filter cube1-matrix test", "test:e2e": "pnpm --filter cube1-matrix test:e2e", "test:e2e:ci": "pnpm --filter cube1-matrix playwright test --config=playwright-ci.config.ts", "dev:backend": "cd apps/backend && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "turbo type-check"}, "devDependencies": {"turbo": "^2.3.0", "prettier": "^3.3.3", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "eslint": "^8.57.1", "typescript": "^5.8.3"}, "packageManager": "pnpm@9.15.0", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "workspaces": ["apps/*", "packages/*"]}