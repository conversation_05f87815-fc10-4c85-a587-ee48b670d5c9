# 代码简化重构 - 快速开始指南

## 🚀 立即开始

### 第一步：环境准备 (15分钟)

```bash
# 1. 创建功能分支
git checkout -b feature/code-simplification-refactor
git push -u origin feature/code-simplification-refactor

# 2. 创建备份标签
git tag backup-before-refactor-$(date +%Y%m%d)
git push origin backup-before-refactor-$(date +%Y%m%d)

# 3. 确保测试环境正常
cd apps/frontend
npm install
npm run test
npm run build

cd ../backend
poetry install
poetry run pytest
```

### 第二步：建立基线 (10分钟)

```bash
# 代码质量基线
echo "=== 代码质量基线 ===" > baseline-report.txt
find apps/frontend/src -name "*.ts" -o -name "*.tsx" | xargs wc -l >> baseline-report.txt
npm run lint >> baseline-report.txt

# 性能基线
npm run test:performance >> baseline-report.txt

# 提交基线报告
git add baseline-report.txt
git commit -m "建立重构前基线数据"
```

### 第三步：配置监控 (10分钟)

```bash
# 安装代码质量工具
npm install --save-dev complexity-report
npm install --save-dev @typescript-eslint/eslint-plugin

# 配置性能监控
cat > apps/frontend/core/monitoring/PerformanceMonitor.ts << 'EOF'
export class PerformanceMonitor {
  private static metrics = new Map<string, number[]>();
  
  static startTiming(label: string): () => void {
    const start = performance.now();
    return () => {
      const end = performance.now();
      const duration = end - start;
      
      if (!this.metrics.has(label)) {
        this.metrics.set(label, []);
      }
      this.metrics.get(label)!.push(duration);
      
      if (duration > 100) {
        console.warn(`慢操作: ${label} 耗时 ${duration.toFixed(2)}ms`);
      }
    };
  }
}
EOF
```

## 📋 第一周任务清单

### Day 1: 环境准备和分析
- [x] 创建功能分支和备份
- [ ] 建立代码质量基线
- [ ] 配置监控工具
- [ ] 分析MatrixStore.ts结构

### Day 2-3: 拆分MatrixStore
- [ ] 创建CellWordBindingStore.ts
- [ ] 创建MatrixCacheService.ts  
- [ ] 创建MatrixDataInitializer.ts
- [ ] 编写单元测试

### Day 4: 通用工具创建
- [ ] 创建StateUpdateUtils.ts
- [ ] 重构现有状态更新调用
- [ ] 验证功能正常

### Day 5: 测试和验证
- [ ] 运行完整测试套件
- [ ] 性能基准测试
- [ ] 代码审查

## 🛠️ 关键代码模板

### CellWordBindingStore 模板
```typescript
// apps/frontend/core/matrix/CellWordBindingStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface CellWordBindingState {
  cellWordBindings: Map<string, string>;
  bindWordToCell: (x: number, y: number, wordId: string) => void;
  unbindWordFromCell: (x: number, y: number) => void;
  getCellWord: (x: number, y: number) => string | null;
}

export const useCellWordBindingStore = create<CellWordBindingState>()(
  persist(
    (set, get) => ({
      cellWordBindings: new Map(),
      
      bindWordToCell: (x: number, y: number, wordId: string) => {
        set((state) => {
          const key = `${x},${y}`;
          state.cellWordBindings.set(key, wordId);
          return state;
        });
      },
      
      unbindWordFromCell: (x: number, y: number) => {
        set((state) => {
          const key = `${x},${y}`;
          state.cellWordBindings.delete(key);
          return state;
        });
      },
      
      getCellWord: (x: number, y: number) => {
        const key = `${x},${y}`;
        return get().cellWordBindings.get(key) || null;
      },
    }),
    {
      name: 'cell-word-binding-store',
      partialize: (state) => ({
        cellWordBindings: Array.from(state.cellWordBindings.entries()),
      }),
      onRehydrateStorage: () => (state) => {
        if (state?.cellWordBindings) {
          state.cellWordBindings = new Map(state.cellWordBindings as any);
        }
      },
    }
  )
);
```

### StateUpdateUtils 模板
```typescript
// apps/frontend/core/utils/StateUpdateUtils.ts
import { produce } from 'immer';

export const createStateUpdater = <T>(
  updateFn: (state: T) => void
) => {
  return produce((state: T) => {
    updateFn(state);
    // 添加通用的状态更新逻辑
    if ('lastUpdate' in state) {
      (state as any).lastUpdate = Date.now();
    }
    if ('isDirty' in state) {
      (state as any).isDirty = true;
    }
  });
};

export const createBatchStateUpdater = <T>(
  updateFns: Array<(state: T) => void>
) => {
  return produce((state: T) => {
    updateFns.forEach(fn => fn(state));
    if ('lastUpdate' in state) {
      (state as any).lastUpdate = Date.now();
    }
    if ('isDirty' in state) {
      (state as any).isDirty = true;
    }
  });
};
```

## 🧪 测试模板

### 单元测试模板
```typescript
// apps/frontend/tests/CellWordBindingStore.test.ts
import { useCellWordBindingStore } from '@/core/matrix/CellWordBindingStore';

describe('CellWordBindingStore', () => {
  beforeEach(() => {
    // 重置store状态
    useCellWordBindingStore.getState().cellWordBindings.clear();
  });

  test('应该正确绑定词语到单元格', () => {
    const store = useCellWordBindingStore.getState();
    store.bindWordToCell(0, 0, 'test-word');
    
    expect(store.getCellWord(0, 0)).toBe('test-word');
  });

  test('应该正确解绑词语', () => {
    const store = useCellWordBindingStore.getState();
    store.bindWordToCell(0, 0, 'test-word');
    store.unbindWordFromCell(0, 0);
    
    expect(store.getCellWord(0, 0)).toBeNull();
  });
});
```

## 📊 进度跟踪

### 每日检查清单
```bash
# 运行每日质量检查
npm run lint
npm run type-check
npm run test
npm run test:coverage

# 检查性能指标
npm run test:performance

# 检查文件大小
find apps/frontend/core -name "*.ts" -exec wc -l {} + | sort -n

# 提交进度
git add .
git commit -m "Day X: 完成XXX功能重构"
git push
```

### 周度回顾模板
```markdown
## 第X周重构进度报告

### 完成的任务
- [ ] 任务1
- [ ] 任务2

### 遇到的问题
- 问题1: 描述和解决方案
- 问题2: 描述和解决方案

### 质量指标
- 代码行数变化: -X%
- 测试覆盖率: X%
- 性能指标: 初始化时间 Xms

### 下周计划
- 任务1
- 任务2
```

## 🚨 常见问题和解决方案

### Q: 重构后测试失败怎么办？
```bash
# 1. 检查具体失败的测试
npm run test -- --verbose

# 2. 逐个修复测试
npm run test -- --testNamePattern="特定测试名称"

# 3. 如果问题严重，回滚到上一个稳定版本
git checkout HEAD~1 -- apps/frontend/core/matrix/MatrixStore.ts
```

### Q: 性能下降怎么办？
```bash
# 1. 运行性能分析
npm run test:performance

# 2. 使用浏览器开发工具分析
# 3. 检查是否有不必要的重渲染

# 4. 如果性能下降超过10%，暂停重构并分析原因
```

### Q: 如何确保功能不变？
```bash
# 1. 运行完整的E2E测试
npm run test:e2e

# 2. 手动测试关键功能
# 3. 对比重构前后的行为

# 4. 使用快照测试确保输出一致
npm run test -- --updateSnapshot
```

## 📞 支持和帮助

- **技术问题**: 在项目Issue中提问
- **进度同步**: 每日站会讨论
- **紧急情况**: 立即联系项目负责人

---

**开始时间**: 立即  
**预计完成**: 第一周结束  
**负责人**: 开发团队  
**状态**: 准备就绪 ✅
