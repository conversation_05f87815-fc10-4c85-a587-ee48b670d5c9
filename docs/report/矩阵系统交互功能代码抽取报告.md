# 矩阵系统交互功能代码抽取报告

## 概述

本报告详细抽取了项目中关于矩阵系统单击/双击功能以及控制面板词库管理滑动关联功能的所有相关代码文件和实现细节。

## 一、矩阵系统单击和双击功能

### 1.1 核心交互处理文件

#### 1.1.1 MatrixInteractionService.ts
**路径**: `apps/frontend/core/services/MatrixInteractionService.ts`

**核心功能**:
- 单击处理器 (`clickHandler`)
- 双击处理器 (`doubleClickHandler`)
- 交互事件统一处理

**关键代码片段**:
```typescript
// 单击处理器
const clickHandler: InteractionHandler = {
  name: 'click',
  priority: 100,
  canHandle: (context) => context.event.type === 'click',
  handle: (context) => {
    const { targetCell, config, currentSelection } = context;
    // 处理选择逻辑
    if (context.event.modifiers?.ctrl || context.event.modifiers?.meta) {
      // Ctrl/Cmd + 点击：多选模式
    } else {
      // 普通点击：单选模式
    }
  }
};

// 双击处理器
const doubleClickHandler: InteractionHandler = {
  name: 'doubleClick',
  priority: 90,
  canHandle: (context) => context.event.type === 'doubleClick',
  handle: (context) => {
    // 双击进入编辑模式（如果是词语模式）
    if (ModeChecker.isWordMode(config)) {
      triggeredEvents.push('enter-edit-mode');
    }
    // 双击激活单元格
    if (!targetCell.isActive) {
      updatedCells.set(cellKey, { isActive: true });
      triggeredEvents.push('cell-activated');
    }
  }
};
```

#### 1.1.2 Matrix.tsx
**路径**: `apps/frontend/components/Matrix.tsx`

**核心功能**:
- 矩阵主组件
- 单击和双击事件绑定
- 交互事件创建和分发

**关键代码片段**:
```typescript
// 处理单元格点击
const handleCellClick = useCallback((x: number, y: number, event: React.MouseEvent) => {
  const coordinate = { x, y };
  
  // 更新状态
  selectCell(x, y, event.ctrlKey || event.metaKey);
  
  // 创建交互事件
  const interactionEvent = createInteractionEvent('click', coordinate, {
    ctrl: event.ctrlKey,
    shift: event.shiftKey,
    alt: event.altKey,
  });
  
  // 处理业务逻辑
  const cell = matrixData?.cells.get(`${x},${y}`);
  if (cell) {
    matrixCore.handleInteraction(interactionEvent, cell, finalConfig);
  }
  
  // 调用外部回调
  onCellClick?.(coordinate, event);
}, [matrixData?.cells, finalConfig, selectCell, onCellClick]);

// 处理双击
const handleCellDoubleClick = useCallback((x: number, y: number, event: React.MouseEvent) => {
  const coordinate = { x, y };
  onCellDoubleClick?.(coordinate, event);
}, [onCellDoubleClick]);
```

#### 1.1.3 MatrixCell.tsx
**路径**: `apps/frontend/components/MatrixCell.tsx`

**核心功能**:
- 单个单元格组件
- 事件处理器绑定
- 样式和状态管理

**关键代码片段**:
```typescript
// 事件处理器
const handleClick = useCallback((event: React.MouseEvent) => {
  onClick?.(x, y, event);
}, [x, y, onClick]);

const handleDoubleClick = useCallback((event: React.MouseEvent) => {
  onDoubleClick?.(x, y, event);
}, [x, y, onDoubleClick]);

// JSX渲染
<div
  data-x={x}
  data-y={y}
  data-cell={`${x},${y}`}
  className={cellClassName}
  style={cellStyle}
  onClick={handleClick}
  onDoubleClick={handleDoubleClick}
  onMouseEnter={handleMouseEnter}
  onMouseLeave={handleMouseLeave}
  onFocus={handleFocus}
  onBlur={handleBlur}
  tabIndex={0}
  role="gridcell"
  aria-label={`单元格 ${x},${y}${displayContent ? `: ${displayContent}` : ''}`}
>
  {displayContent}
</div>
```

### 1.2 样式和状态管理

#### 1.2.1 useCellStyle.ts
**路径**: `apps/frontend/hooks/useCellStyle.ts`

**核心功能**:
- 单元格样式计算
- 状态样式应用（选中、悬停、焦点）
- 主题和自定义样式合并

**关键样式常量**:
```typescript
const SELECTED_STYLE = {
  backgroundColor: '#e3f2fd',
  borderColor: '#2196f3',
  borderWidth: '2px',
  zIndex: 5,
};

const HOVERED_STYLE = {
  backgroundColor: '#f8f9fa',
  transform: 'translateY(-1px)',
  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
};

const FOCUSED_STYLE = {
  outline: '2px solid #007bff',
  outlineOffset: '1px',
};
```

#### 1.2.2 globals.css
**路径**: `apps/frontend/styles/globals.css`

**核心功能**:
- 矩阵单元格基础样式
- 交互状态样式
- 动画和过渡效果

**关键CSS样式**:
```css
/* 矩阵单元格 - 现代化极简设计 */
.matrix-cell {
  transition: all 0.2s ease;
  box-sizing: border-box;
  user-select: none;
  cursor: pointer;
  border-radius: 6px;
  font-weight: bold;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.matrix-cell:hover {
  transform: translateY(-1px);
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.matrix-cell.selected {
  box-shadow: 0 0 0 2px #3b82f6, 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 填词模式下的格子样式 - 深灰色加粗边框 */
.matrix-cell.word-input-active {
  border-width: 3px !important;
  border-style: solid !important;
  border-color: #4b5563 !important;
  box-shadow: 0 0 0 1px #4b5563, 0 4px 12px rgba(75, 85, 99, 0.3) !important;
  z-index: 20;
}
```

## 二、控制面板词库管理滑动关联功能

### 2.1 核心组件文件

#### 2.1.1 WordLibraryManager.tsx
**路径**: `apps/frontend/components/WordLibraryManager.tsx`

**核心功能**:
- 词库管理主组件
- 滑动关联逻辑实现
- 可视区域检测

**关键滑动逻辑**:
```typescript
// 检查元素是否在可视区域内
const isElementInViewport = useCallback((element: HTMLElement): boolean => {
  const rect = element.getBoundingClientRect();
  const container = element.closest('.overflow-y-auto');

  if (container) {
    const containerRect = container.getBoundingClientRect();
    return (
      rect.top >= containerRect.top &&
      rect.bottom <= containerRect.bottom
    );
  }

  // 如果没有找到滚动容器，检查是否在窗口可视区域内
  return (
    rect.top >= 0 &&
    rect.bottom <= window.innerHeight
  );
}, []);

// 滑动到可视区域中间 - 确保只在双击激活时触发一次
useEffect(() => {
  // 如果不是激活状态，重置滑动标记
  if (!isActiveLibrary) {
    hasScrolledForCurrentActivation.current = false;
    lastActiveLibraryRef.current = null;
    activationTimestampRef.current = 0;
    return;
  }

  // 如果没有元素引用，跳过
  if (!itemRef.current) {
    return;
  }

  // 检查是否是新的激活状态（不同的词库被激活）
  const currentLibraryKey = matchedLibrary;
  const isNewActivation = lastActiveLibraryRef.current !== currentLibraryKey;

  // 如果不是新的激活状态且已经滑动过，跳过
  if (!isNewActivation && hasScrolledForCurrentActivation.current) {
    return;
  }

  // 如果是新的激活状态，记录时间戳并立即执行滑动
  if (isNewActivation) {
    activationTimestampRef.current = Date.now();

    // 检查是否已经在可视区域内
    if (isElementInViewport(itemRef.current)) {
      // 词库已在可视区域内，无需滑动
      hasScrolledForCurrentActivation.current = true;
      lastActiveLibraryRef.current = currentLibraryKey;
      return;
    }

    // 只有当元素不在可视区域时才滑动
    itemRef.current.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    });

    // 标记已滑动
    hasScrolledForCurrentActivation.current = true;
    lastActiveLibraryRef.current = currentLibraryKey;
  }
}, [isActiveLibrary, matchedLibrary, libraryKey]);
```

#### 2.1.2 CombinedWordLibraryInput.tsx
**路径**: `apps/frontend/components/ui/CombinedWordLibraryInput.tsx`

**核心功能**:
- 合并式词库输入组件
- 词语显示和编辑
- 折叠/展开状态管理

**关键交互逻辑**:
```typescript
// 处理键盘事件
const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
  if (e.key === 'Enter') {
    e.preventDefault();
    // Enter键直接确认输入
    handleInputConfirm();
  } else if (e.key === ',' || e.key === '，') {
    // 逗号键不阻止默认行为，让输入变化事件处理实时识别
    // 这样可以实现更灵敏的识别
  }
};

// 切换展开/折叠
const handleToggleExpanded = () => {
  setIsExpanded(!isExpanded);
  toggleLibraryCollapse(libraryKey);
};
```

### 2.2 状态管理

#### 2.2.1 WordLibraryStore.ts
**路径**: `apps/frontend/core/wordLibrary/WordLibraryStore.ts`

**核心功能**:
- 词库状态管理
- 填词模式状态
- 滑动触发状态

**关键状态接口**:
```typescript
interface WordInputStore {
  // 填词模式状态
  isActive: boolean;
  selectedCell: { x: number; y: number } | null;
  matchedLibrary: WordLibraryKey | null;
  selectedWordIndex: number;
  availableWords: WordEntry[];
  temporaryWord: string | null;
  isWordBound: boolean;

  // 操作方法
  activateWordInput: (x: number, y: number, color: BasicColorType, level: DataLevel, boundWordId?: string) => Promise<void>;
  deactivateWordInput: () => void;
  selectNextWord: () => void;
  selectPreviousWord: () => void;
  confirmWordSelection: () => WordEntry | null;
  clearCellWord: () => void;
  checkLibraryEmpty: () => boolean;
}
```

**滑动触发逻辑**:
```typescript
activateWordInput: async (x: number, y: number, color: BasicColorType, level: DataLevel, boundWordId?: string) => {
  const libraryKey = createWordLibraryKey(color, level);
  const wordLibraryStore = useWordLibraryStore.getState();
  const library = wordLibraryStore.getLibrary(libraryKey);

  let selectedIndex = 0;
  let temporaryWord = library?.words?.[0]?.text || null;
  let isWordBound = false;

  // 如果传递了绑定的词语ID，定位到该词语
  if (boundWordId && library?.words) {
    const boundWordIndex = library.words.findIndex(word => word.id === boundWordId);
    if (boundWordIndex !== -1) {
      selectedIndex = boundWordIndex;
      temporaryWord = library.words[boundWordIndex].text;
      isWordBound = true;
    }
  }

  set({
    isActive: true,
    selectedCell: { x, y },
    matchedLibrary: libraryKey,
    selectedWordIndex: selectedIndex,
    availableWords: library?.words || [],
    temporaryWord,
    isWordBound
  });
},
```

### 2.3 样式和视觉反馈

#### 2.3.1 词库激活样式
**路径**: `apps/frontend/styles/globals.css`

```css
/* 词库高亮样式 - 深灰色加粗边框 */
.word-library-active {
  border-width: 3px !important;
  border-style: solid !important;
  border-color: #4b5563 !important;
  box-shadow: 0 0 0 1px #4b5563, 0 2px 8px rgba(75, 85, 99, 0.2) !important;
  background-color: rgba(75, 85, 99, 0.05) !important;
}
```

#### 2.3.2 滚动容器样式
**路径**: `apps/frontend/components/WordLibraryManager.tsx`

```typescript
{/* 词库列表 - 可滚动容器 */}
<div className="flex-1 overflow-y-auto space-y-1 pr-2">
  {AVAILABLE_WORD_LIBRARIES.map(({ color, level }) => {
    const libraryKey = `${color}-${level}` as WordLibraryKey;
    return (
      <WordLibraryItem
        key={libraryKey}
        color={color}
        level={level}
        libraryKey={libraryKey}
      />
    );
  })}
</div>
```

## 三、关键技术特性

### 3.1 性能优化
- 使用 `useCallback` 和 `useMemo` 避免不必要的重渲染
- 防抖机制防止频繁滑动
- 可视区域检测避免无效滑动

### 3.2 用户体验
- 平滑滑动动画 (`behavior: 'smooth'`)
- 智能滑动触发（仅在需要时滑动）
- 视觉反馈（高亮激活的词库）

### 3.3 状态管理
- 统一的状态管理架构
- 清晰的状态流转
- 可靠的状态同步机制

## 四、文件清单

### 4.1 矩阵系统单击/双击相关文件
1. `apps/frontend/core/services/MatrixInteractionService.ts` - 交互处理服务
2. `apps/frontend/components/Matrix.tsx` - 矩阵主组件
3. `apps/frontend/components/MatrixCell.tsx` - 单元格组件
4. `apps/frontend/hooks/useCellStyle.ts` - 单元格样式Hook
5. `apps/frontend/hooks/useCellClassName.ts` - 单元格类名Hook
6. `apps/frontend/core/matrix/MatrixStore.ts` - 矩阵状态管理
7. `apps/frontend/core/matrix/MatrixCore.ts` - 矩阵核心逻辑
8. `apps/frontend/styles/globals.css` - 全局样式

### 4.2 词库管理滑动关联相关文件
1. `apps/frontend/components/WordLibraryManager.tsx` - 词库管理主组件
2. `apps/frontend/components/ui/CombinedWordLibraryInput.tsx` - 词库输入组件
3. `apps/frontend/core/wordLibrary/WordLibraryStore.ts` - 词库状态管理
4. `apps/frontend/components/WordSelector.tsx` - 词语选择器
5. `apps/frontend/components/Controls.tsx` - 控制面板
6. `apps/frontend/hooks/useClickOutside.ts` - 点击外部检测Hook

---

*报告生成时间: 2025-08-07*
*项目路径: /Users/<USER>/Desktop/cube1_group*
