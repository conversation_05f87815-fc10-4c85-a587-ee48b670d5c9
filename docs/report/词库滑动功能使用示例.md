# 词库滑动功能使用示例

## 重构前后对比

### 重构前（复杂实现）

```typescript
// WordLibraryItem组件中的复杂滑动逻辑 (92行代码)
const WordLibraryItem = ({ color, level, libraryKey }) => {
  const itemRef = useRef<HTMLDivElement>(null);
  const hasScrolledForCurrentActivation = useRef(false);
  const lastActiveLibraryRef = useRef<string | null>(null);
  const activationTimestampRef = useRef<number>(0);

  // 复杂的可视区域检测
  const isElementInViewport = useCallback((element: HTMLElement): boolean => {
    const rect = element.getBoundingClientRect();
    const container = element.closest('.overflow-y-auto');

    if (container) {
      const containerRect = container.getBoundingClientRect();
      return (
        rect.top >= containerRect.top &&
        rect.bottom <= containerRect.bottom
      );
    }

    return (
      rect.top >= 0 &&
      rect.bottom <= window.innerHeight
    );
  }, []);

  // 复杂的滑动逻辑 (50+行)
  useEffect(() => {
    if (!isActiveLibrary) {
      hasScrolledForCurrentActivation.current = false;
      lastActiveLibraryRef.current = null;
      activationTimestampRef.current = 0;
      return;
    }

    if (!itemRef.current) {
      return;
    }

    const currentLibraryKey = matchedLibrary;
    const isNewActivation = lastActiveLibraryRef.current !== currentLibraryKey;

    if (!isNewActivation && hasScrolledForCurrentActivation.current) {
      return;
    }

    if (isNewActivation) {
      activationTimestampRef.current = Date.now();

      if (isElementInViewport(itemRef.current)) {
        hasScrolledForCurrentActivation.current = true;
        lastActiveLibraryRef.current = currentLibraryKey;
        return;
      }

      itemRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });

      hasScrolledForCurrentActivation.current = true;
      lastActiveLibraryRef.current = currentLibraryKey;
    }
  }, [isActiveLibrary, matchedLibrary, libraryKey]);

  // ... 其他组件逻辑
};
```

### 重构后（简洁实现）

```typescript
// WordLibraryItem组件中的简洁滑动逻辑 (6行代码)
const WordLibraryItem = ({ color, level, libraryKey }) => {
  const { isActive: isWordInputActive, matchedLibrary } = useWordInputStore();
  
  const isActiveLibrary = useMemo(() => {
    return isWordInputActive && matchedLibrary === libraryKey;
  }, [isWordInputActive, matchedLibrary, libraryKey]);

  // 一行代码实现自动滑动 - 简化的滑动逻辑
  // Hook会自动计算位置差值：0=不移动，负数=上移，正数=下移
  useAutoWordLibraryScroll(isActiveLibrary, libraryKey, {
    behavior: 'smooth',
    block: 'center',
    debug: false
  });

  // ... 其他组件逻辑
};
```

## 使用方法详解

### 1. 自动滑动（推荐）

最简单的使用方式，适用于大多数场景：

```typescript
import { useAutoWordLibraryScroll } from '@/hooks/useWordLibraryScroll';

const MyComponent = () => {
  const { isActive, matchedLibrary } = useWordInputStore();
  
  // 自动滑动到激活的词库
  useAutoWordLibraryScroll(isActive, matchedLibrary);
  
  return <div>我的组件</div>;
};
```

### 2. 手动控制滑动

需要精确控制滑动时机的场景：

```typescript
import { useWordLibraryScroll } from '@/hooks/useWordLibraryScroll';

const MyComponent = () => {
  const { scrollToLibrary, resetScrollState } = useWordLibraryScroll({
    behavior: 'smooth',
    block: 'center'
  });
  
  const handleLibraryActivate = (libraryKey: WordLibraryKey) => {
    const result = scrollToLibrary(libraryKey);
    
    if (result.success) {
      console.log(`滑动执行: ${result.delta > 0 ? '下移' : '上移'} ${Math.abs(result.delta)}px`);
    } else {
      console.log('无需滑动或滑动失败');
    }
  };
  
  return (
    <div>
      <button onClick={() => handleLibraryActivate('red-1')}>
        激活红色1级词库
      </button>
      <button onClick={resetScrollState}>
        重置滑动状态
      </button>
    </div>
  );
};
```

### 3. 直接使用服务API

在非React组件中使用：

```typescript
import { 
  triggerWordLibraryScroll, 
  resetWordLibraryScrollState 
} from '@/core/services/WordLibraryScrollService';

// 在任何地方触发滑动
const activateLibrary = (libraryKey: WordLibraryKey) => {
  const result = triggerWordLibraryScroll(libraryKey, {
    behavior: 'smooth',
    block: 'center',
    force: false,
    debug: true
  });
  
  console.log('滑动结果:', {
    success: result.success,
    delta: result.delta,
    action: result.delta === 0 ? '不移动' : 
            result.delta < 0 ? '上移' : '下移',
    error: result.error
  });
};

// 重置状态
resetWordLibraryScrollState();
```

## 配置选项

### ScrollTriggerOptions

```typescript
interface ScrollTriggerOptions {
  /** 滑动行为: 'smooth' | 'auto' */
  behavior?: ScrollBehavior;
  
  /** 对齐方式: 'start' | 'center' | 'end' */
  block?: ScrollLogicalPosition;
  
  /** 是否强制滑动（忽略重复检查） */
  force?: boolean;
  
  /** 是否启用调试日志 */
  debug?: boolean;
}
```

### 使用示例

```typescript
// 平滑滑动到顶部
useAutoWordLibraryScroll(isActive, libraryKey, {
  behavior: 'smooth',
  block: 'start'
});

// 立即滑动到中心
useAutoWordLibraryScroll(isActive, libraryKey, {
  behavior: 'auto',
  block: 'center'
});

// 强制滑动（忽略重复检查）
useAutoWordLibraryScroll(isActive, libraryKey, {
  force: true,
  debug: true
});
```

## 扩展到其他组件

新的滑动系统可以轻松扩展到任何需要滑动功能的组件：

```typescript
import { useScrollManager } from '@/hooks/useScrollManager';

const MyScrollableList = () => {
  const { scrollToTarget } = useScrollManager({
    behavior: 'smooth',
    block: 'center'
  });
  
  const handleItemClick = (itemElement: HTMLElement) => {
    // 滑动到任意元素
    const success = scrollToTarget(itemElement);
    console.log('滑动结果:', success);
  };
  
  return <div>可滚动列表</div>;
};
```

## 调试和监控

### 启用调试日志

```typescript
useAutoWordLibraryScroll(isActive, libraryKey, {
  debug: true
});
```

### 调试输出示例

```
[WordLibraryScrollService] 位置计算: {
  libraryKey: 'red-1',
  current: 100,
  target: 300,
  delta: 200,
  action: '下移'
}
[WordLibraryScrollService] 执行下移滑动: {
  from: 100,
  to: 300,
  delta: 200
}
```

## 性能特性

### 1. 智能滑动
- 自动检测元素是否已在正确位置
- 避免不必要的滑动操作
- 防止重复滑动同一词库

### 2. 状态管理
- 单例模式的服务实例
- 最小化状态存储
- 及时清理无效状态

### 3. 错误处理
- 容器不存在时的优雅降级
- 目标元素不存在时的错误提示
- 滑动失败时的错误信息

## 总结

重构后的词库滑动功能实现了：

✅ **代码简化**: 从92行复杂逻辑简化为6行Hook调用  
✅ **统一管理**: 所有滑动需求使用统一的服务和接口  
✅ **高度可复用**: 支持任意组件的滑动需求  
✅ **性能优化**: 智能的位置检测和防重复机制  
✅ **易于测试**: 清晰的接口和可预测的行为  
✅ **易于维护**: 模块化的架构和清晰的职责分离  

---

*使用示例更新时间: 2025-08-07*
