# Cube1 Group 系统时序图

## 概述

本文档展示了 Cube1 Group 33x33矩阵数据可视化系统的详细交互流程。通过时序图描述了系统各组件之间的协作关系，包括应用初始化、模式切换、单元格交互、词语管理等核心功能。

## 1. 应用初始化时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Browser as 浏览器
    participant NextJS as Next.js应用
    participant MatrixStore as MatrixStore
    participant WordStore as WordLibraryStore
    participant MatrixCore as MatrixCore
    participant GroupData as GroupAData
    participant Cache as 缓存系统
    participant Backend as FastAPI后端

    Note over User,Backend: 应用启动和初始化流程
    
    User->>Browser: 访问应用URL
    Browser->>NextJS: 请求页面
    NextJS->>NextJS: 初始化App Router
    NextJS->>MatrixStore: 创建状态管理实例
    NextJS->>WordStore: 创建词库状态实例
    
    Note over MatrixStore,Cache: 矩阵数据初始化
    MatrixStore->>MatrixCore: initializeMatrix()
    MatrixCore->>GroupData: 获取A-M组数据
    GroupData->>Cache: 检查数据缓存
    
    alt 缓存存在
        Cache-->>GroupData: 返回缓存数据
    else 缓存不存在
        GroupData->>GroupData: 生成矩阵数据
        GroupData->>Cache: 存储到缓存
    end
    
    GroupData-->>MatrixCore: 返回矩阵数据(1089个单元格)
    MatrixCore->>MatrixCore: 处理数据并创建默认单元格
    MatrixCore-->>MatrixStore: 返回初始化结果
    
    Note over WordStore,Backend: 词库数据初始化
    WordStore->>WordStore: 加载默认词库
    WordStore->>WordStore: 初始化词语绑定状态
    
    Note over NextJS,Backend: 后端连接检查
    MatrixStore->>Backend: 发送健康检查请求
    Backend->>Backend: 检查系统状态
    Backend-->>MatrixStore: 返回健康状态
    
    Note over NextJS,User: 渲染完成
    MatrixStore-->>NextJS: 状态初始化完成
    WordStore-->>NextJS: 词库初始化完成
    NextJS->>NextJS: 渲染Matrix组件(33x33网格)
    NextJS->>NextJS: 渲染Controls组件
    NextJS->>NextJS: 渲染WordLibraryManager组件
    NextJS-->>Browser: 返回完整页面
    Browser-->>User: 显示应用界面
```

## 2. 模式切换时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Controls as Controls组件
    participant MatrixStore as MatrixStore
    participant MatrixCore as MatrixCore
    participant ModeHandler as 模式处理器
    participant Matrix as Matrix组件
    participant Cache as 缓存系统

    Note over User,Cache: 业务模式切换流程
    
    User->>Controls: 点击模式切换按钮(coordinate/color/level/word)
    Controls->>MatrixStore: setMode(newMode)
    
    MatrixStore->>MatrixStore: 验证模式有效性
    MatrixStore->>MatrixCore: switchMode(mode, data, config)
    
    MatrixCore->>ModeHandler: 获取对应模式处理器
    
    alt 坐标模式
        ModeHandler->>ModeHandler: 生成坐标显示内容
        ModeHandler->>ModeHandler: 设置默认样式
    else 颜色模式
        ModeHandler->>ModeHandler: 应用数据颜色
        ModeHandler->>ModeHandler: 清空文本内容
    else 等级模式
        ModeHandler->>ModeHandler: 显示等级数值
        ModeHandler->>ModeHandler: 设置等级样式
    else 词语模式
        ModeHandler->>MatrixStore: 获取词语绑定数据
        MatrixStore-->>ModeHandler: 返回绑定的词语
        ModeHandler->>ModeHandler: 显示词语文本
    end
    
    ModeHandler->>MatrixCore: processData(data)
    MatrixCore->>MatrixCore: 生成渲染数据
    MatrixCore->>Cache: 更新计算属性缓存
    MatrixCore-->>MatrixStore: 返回处理结果
    
    MatrixStore->>MatrixStore: 更新状态
    MatrixStore->>MatrixStore: 触发状态变更通知
    MatrixStore-->>Matrix: 通知重新渲染
    
    Matrix->>Matrix: 重新渲染1089个单元格
    Matrix->>Matrix: 应用新的样式和内容
    Matrix-->>User: 显示新模式效果
```

## 3. 单元格交互时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Matrix as Matrix组件
    participant MatrixCore as MatrixCore
    participant MatrixStore as MatrixStore
    participant WordSelector as WordSelector
    participant WordStore as WordLibraryStore

    Note over User,WordStore: 单元格点击和交互流程
    
    User->>Matrix: 点击单元格(x, y)
    Matrix->>MatrixCore: createInteractionEvent(x, y, 'click')
    MatrixCore->>MatrixCore: handleInteraction(event)
    
    MatrixCore->>MatrixStore: selectCell(x, y)
    MatrixStore->>MatrixStore: 更新选中状态
    MatrixStore->>MatrixStore: 清除其他单元格选中状态
    MatrixStore-->>Matrix: 通知状态变更
    
    Matrix->>Matrix: 更新单元格样式(添加选中效果)
    Matrix-->>User: 显示选中反馈
    
    Note over User,WordStore: 悬停交互
    User->>Matrix: 鼠标悬停单元格
    Matrix->>MatrixStore: hoverCell(x, y)
    MatrixStore->>MatrixStore: 更新悬停状态
    MatrixStore-->>Matrix: 通知状态变更
    Matrix->>Matrix: 显示悬停效果
    Matrix-->>User: 显示悬停反馈
    
    Note over User,WordStore: 词语模式下的词语绑定
    alt 当前为词语模式且单元格被选中
        Matrix->>WordSelector: 显示词语选择器
        WordSelector->>WordStore: 获取可用词库
        WordStore-->>WordSelector: 返回词库列表
        WordSelector-->>User: 显示词语选择界面
        
        User->>WordSelector: 选择词语
        WordSelector->>MatrixStore: bindWordToCell(x, y, wordId)
        MatrixStore->>MatrixStore: 更新词语绑定
        MatrixStore->>MatrixStore: 更新单元格内容
        MatrixStore-->>Matrix: 通知状态变更
        Matrix->>Matrix: 更新单元格显示
        Matrix-->>User: 显示绑定的词语
    end
```

## 4. 词库管理时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant WordManager as WordLibraryManager
    participant WordStore as WordLibraryStore
    participant WordCore as WordLibraryCore
    participant MatrixStore as MatrixStore
    participant Storage as 本地存储

    Note over User,Storage: 词库创建流程
    
    User->>WordManager: 点击"创建词库"
    WordManager->>WordManager: 显示创建表单
    User->>WordManager: 输入词库名称和描述
    User->>WordManager: 点击"确认创建"
    
    WordManager->>WordStore: createLibrary(name, description)
    WordStore->>WordCore: 验证词库信息
    WordCore->>WordCore: 生成唯一ID
    WordCore->>WordCore: 创建词库对象
    WordCore-->>WordStore: 返回新词库
    
    WordStore->>WordStore: 添加到词库列表
    WordStore->>Storage: 持久化词库数据
    WordStore-->>WordManager: 通知创建成功
    WordManager-->>User: 显示成功消息
    
    Note over User,Storage: 词语添加流程
    
    User->>WordManager: 选择词库
    User->>WordManager: 点击"添加词语"
    WordManager->>WordManager: 显示词语输入框
    User->>WordManager: 输入词语文本
    User->>WordManager: 点击"确认添加"
    
    WordManager->>WordStore: addWordToLibrary(libraryId, text)
    WordStore->>WordCore: 验证词语文本
    WordCore->>WordCore: 生成词语对象
    WordCore-->>WordStore: 返回新词语
    
    WordStore->>WordStore: 添加到指定词库
    WordStore->>Storage: 持久化更新
    WordStore-->>WordManager: 通知添加成功
    WordManager-->>User: 更新词语列表
    
    Note over User,Storage: 词库删除流程
    
    User->>WordManager: 点击"删除词库"
    WordManager->>WordManager: 显示确认对话框
    User->>WordManager: 确认删除
    
    WordManager->>WordStore: deleteLibrary(libraryId)
    WordStore->>MatrixStore: 清理相关词语绑定
    MatrixStore->>MatrixStore: 移除绑定关系
    WordStore->>WordStore: 从列表中移除词库
    WordStore->>Storage: 持久化更新
    WordStore-->>WordManager: 通知删除成功
    WordManager-->>User: 更新界面
```

## 5. 数据更新和缓存时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Controls as Controls组件
    participant MatrixStore as MatrixStore
    participant MatrixCore as MatrixCore
    participant GroupData as GroupAData
    participant Cache as 缓存系统
    participant Matrix as Matrix组件

    Note over User,Matrix: 矩阵重置流程
    
    User->>Controls: 点击"重置矩阵"
    Controls->>Controls: 显示确认对话框
    User->>Controls: 确认重置
    
    Controls->>MatrixStore: clearMatrix()
    MatrixStore->>MatrixStore: 清空当前数据
    MatrixStore->>MatrixStore: 清空选中状态
    MatrixStore->>MatrixStore: 清空词语绑定
    
    MatrixStore->>Cache: 清除所有缓存
    Cache->>Cache: 清空计算属性缓存
    Cache->>Cache: 清空数据缓存
    
    MatrixStore->>MatrixCore: initializeMatrix()
    MatrixCore->>GroupData: 重新获取数据
    GroupData->>GroupData: 生成新的矩阵数据
    GroupData-->>MatrixCore: 返回新数据
    
    MatrixCore->>MatrixCore: 处理新数据
    MatrixCore->>Cache: 更新缓存
    MatrixCore-->>MatrixStore: 返回初始化结果
    
    MatrixStore->>MatrixStore: 更新状态
    MatrixStore-->>Matrix: 通知重新渲染
    Matrix->>Matrix: 重新渲染所有单元格
    Matrix-->>User: 显示重置后的矩阵
    
    Note over User,Matrix: 性能优化缓存流程
    
    MatrixStore->>Cache: 检查计算属性缓存
    Cache->>Cache: 验证缓存有效性
    
    alt 缓存有效
        Cache-->>MatrixStore: 返回缓存结果
        MatrixStore-->>Matrix: 快速渲染
    else 缓存无效或不存在
        Cache->>MatrixCore: 重新计算
        MatrixCore->>MatrixCore: 执行数据处理
        MatrixCore-->>Cache: 返回计算结果
        Cache->>Cache: 更新缓存
        Cache-->>MatrixStore: 返回新结果
        MatrixStore-->>Matrix: 渲染更新
    end
```

## 6. API通信时序图

```mermaid
sequenceDiagram
    participant Frontend as 前端应用
    participant APIRouter as API路由
    participant HealthAPI as 健康检查API
    participant Services as 业务服务
    participant Models as 数据模型
    participant Logging as 日志系统

    Note over Frontend,Logging: API健康检查流程
    
    Frontend->>APIRouter: GET /api/v1/health
    APIRouter->>HealthAPI: 路由到健康检查
    HealthAPI->>HealthAPI: 检查系统状态
    HealthAPI->>Logging: 记录检查日志
    HealthAPI-->>APIRouter: 返回健康状态
    APIRouter-->>Frontend: 返回JSON响应
    
    Note over Frontend,Logging: 业务API调用流程
    
    Frontend->>APIRouter: POST /api/v1/data
    APIRouter->>APIRouter: 验证请求
    APIRouter->>Services: 调用业务服务
    Services->>Models: 数据验证和处理
    Models->>Models: Pydantic数据验证
    Models-->>Services: 返回验证结果
    Services->>Services: 执行业务逻辑
    Services->>Logging: 记录操作日志
    Services-->>APIRouter: 返回处理结果
    APIRouter-->>Frontend: 返回JSON响应
    
    Note over Frontend,Logging: 错误处理流程
    
    Frontend->>APIRouter: 发送错误请求
    APIRouter->>Services: 调用服务
    Services->>Services: 处理过程中发生错误
    Services->>Logging: 记录错误日志
    Services-->>APIRouter: 抛出异常
    APIRouter->>APIRouter: 全局异常处理
    APIRouter->>Logging: 记录异常信息
    APIRouter-->>Frontend: 返回错误响应
```

## 流程详细说明

### 1. 应用初始化流程

**目标**: 系统启动时完成所有必要的初始化工作

**关键步骤**:
1. Next.js应用启动和路由初始化
2. 状态管理实例创建(MatrixStore, WordLibraryStore)
3. 矩阵数据加载和处理(1089个单元格)
4. 词库数据初始化
5. 后端健康检查
6. UI组件渲染完成

**性能优化**:
- 缓存机制减少重复计算
- 并行初始化提升启动速度
- 懒加载非关键资源

### 2. 模式切换流程

**目标**: 在四种业务模式间无缝切换

**支持模式**:
- **坐标模式**: 显示单元格坐标信息
- **颜色模式**: 根据数据显示不同颜色
- **等级模式**: 显示数据等级数值
- **词语模式**: 显示绑定的词语文本

**关键特性**:
- 配置驱动的模式处理
- 高效的状态更新机制
- 实时的视觉反馈

### 3. 单元格交互流程

**目标**: 提供丰富的用户交互体验

**交互类型**:
- **点击选择**: 单元格选中和取消
- **悬停效果**: 实时悬停反馈
- **词语绑定**: 词语模式下的词语关联
- **多选操作**: 支持批量操作

**用户体验**:
- 即时的视觉反馈
- 流畅的动画效果
- 直观的操作提示

### 4. 词库管理流程

**目标**: 完整的词库生命周期管理

**功能特性**:
- **词库CRUD**: 创建、读取、更新、删除词库
- **词语管理**: 词语的添加、编辑、删除
- **数据持久化**: 本地存储保证数据安全
- **关联清理**: 删除时自动清理相关绑定

### 5. 数据更新和缓存流程

**目标**: 高性能的数据管理和缓存策略

**缓存策略**:
- **计算属性缓存**: 避免重复计算
- **数据缓存**: 减少数据获取开销
- **缓存失效**: 智能的缓存更新机制

**性能优化**:
- 增量更新减少重渲染
- 批量操作提升效率
- 内存管理防止泄漏

### 6. API通信流程

**目标**: 稳定可靠的前后端通信

**通信特性**:
- **RESTful API**: 标准的API设计
- **错误处理**: 完善的异常处理机制
- **日志记录**: 详细的操作日志
- **健康监控**: 实时的服务状态检查

## 技术特性总结

### 响应式架构
- 基于Zustand的响应式状态管理
- 自动的状态同步和UI更新
- 高效的变更检测和传播

### 性能优化
- 多层缓存策略
- 计算属性优化
- 批量更新机制
- 内存使用优化

### 用户体验
- 实时交互反馈
- 流畅的动画效果
- 直观的操作界面
- 完善的错误提示

### 可维护性
- 模块化的架构设计
- 清晰的数据流向
- 完整的类型定义
- 详细的日志记录

---

*生成时间: 2025-01-25*
*版本: v2.0*
*文档类型: 系统时序图*