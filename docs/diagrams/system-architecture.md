# Cube1 Group 系统架构图

## 概述

本文档展示了 Cube1 Group 33x33矩阵数据可视化系统的完整架构设计。系统采用现代化的全栈架构，前端使用 Next.js + React + Zustand，后端使用 FastAPI，专注于高性能 1089 个单元格的实时渲染和交互。

## 整体系统架构

```mermaid
graph TB
    %% 用户层
    subgraph "用户层 (User Layer)"
        User[用户浏览器]
    end

    %% 前端应用层
    subgraph "前端应用层 (Frontend Application)"
        NextJS[Next.js 15.1.0<br/>App Router]
        
        subgraph "UI组件层 (UI Components)"
            Matrix[Matrix组件<br/>33x33网格渲染]
            Controls[Controls组件<br/>模式切换控制]
            WordLibrary[WordLibraryManager<br/>词库管理]
            WordSelector[WordSelector<br/>词语选择器]
            UIComponents[UI组件库<br/>Button, Icons等]
        end
        
        subgraph "状态管理层 (State Management)"
            MatrixStore[MatrixStore<br/>Zustand状态管理]
            WordStore[WordLibraryStore<br/>词库状态管理]
            ToastStore[ToastStore<br/>消息提示状态]
            Persist[持久化中间件<br/>数据持久化]
        end
        
        subgraph "业务逻辑层 (Business Logic)"
            MatrixCore[MatrixCore<br/>矩阵核心引擎]
            WordCore[WordLibraryCore<br/>词库核心逻辑]
            
            subgraph "模式处理器 (Mode Handlers)"
                CoordMode[坐标模式<br/>coordinate]
                ColorMode[颜色模式<br/>color]
                LevelMode[等级模式<br/>level]
                WordMode[词语模式<br/>word]
            end
        end
        
        subgraph "数据层 (Data Layer)"
            GroupData[GroupAData<br/>A-M组数据源]
            DataCache[数据缓存<br/>CacheManager]
            MatrixTypes[MatrixTypes<br/>类型定义]
        end
    end

    %% 后端服务层
    subgraph "后端服务层 (Backend Services)"
        FastAPI[FastAPI应用<br/>main.py]
        
        subgraph "API层 (API Layer)"
            APIRouter[API路由<br/>v1/router.py]
            HealthAPI[健康检查<br/>health.py]
            Endpoints[API端点<br/>endpoints/]
        end
        
        subgraph "核心服务 (Core Services)"
            Config[配置管理<br/>config.py]
            Logging[日志系统<br/>logging.py]
            Exceptions[异常处理<br/>exceptions.py]
        end
        
        subgraph "业务服务 (Business Services)"
            Services[业务服务<br/>services/]
            CRUD[数据操作<br/>crud/]
            Models[数据模型<br/>models/]
            Tasks[后台任务<br/>tasks/]
        end
    end

    %% 基础设施层
    subgraph "基础设施层 (Infrastructure)"
        subgraph "构建工具 (Build Tools)"
            Turbo[Turbo Monorepo<br/>构建系统]
            PNPM[pnpm 9.15.0<br/>包管理]
        end
        
        subgraph "开发工具 (Development Tools)"
            TypeScript[TypeScript 5.8.3<br/>类型系统]
            ESLint[ESLint<br/>代码检查]
            Prettier[Prettier<br/>代码格式化]
            Vitest[Vitest<br/>单元测试]
            Playwright[Playwright<br/>E2E测试]
        end
        
        subgraph "样式系统 (Styling)"
            TailwindCSS[Tailwind CSS<br/>样式框架]
            PostCSS[PostCSS<br/>CSS处理]
        end
        
        subgraph "Python后端工具 (Python Backend Tools)"
            Poetry[Poetry<br/>依赖管理]
            Uvicorn[Uvicorn<br/>ASGI服务器]
            Pydantic[Pydantic<br/>数据验证]
            Pytest[Pytest<br/>测试框架]
        end
    end

    %% 连接关系
    User --> NextJS
    
    %% 前端内部连接
    NextJS --> Matrix
    NextJS --> Controls
    NextJS --> WordLibrary
    NextJS --> WordSelector
    NextJS --> UIComponents
    
    Matrix --> MatrixStore
    Controls --> MatrixStore
    WordLibrary --> WordStore
    WordSelector --> WordStore
    
    MatrixStore --> MatrixCore
    MatrixStore --> Persist
    WordStore --> WordCore
    
    MatrixCore --> CoordMode
    MatrixCore --> ColorMode
    MatrixCore --> LevelMode
    MatrixCore --> WordMode
    
    MatrixCore --> GroupData
    MatrixCore --> DataCache
    MatrixCore --> MatrixTypes
    
    %% 前后端通信
    MatrixStore -.->|HTTP API| APIRouter
    WordStore -.->|HTTP API| APIRouter
    
    %% 后端内部连接
    FastAPI --> APIRouter
    APIRouter --> HealthAPI
    APIRouter --> Endpoints
    
    FastAPI --> Config
    FastAPI --> Logging
    FastAPI --> Exceptions
    
    Endpoints --> Services
    Services --> CRUD
    Services --> Models
    Services --> Tasks
    
    %% 基础设施连接
    NextJS -.->|构建| Turbo
    FastAPI -.->|依赖管理| Poetry
    Turbo -.->|包管理| PNPM
    FastAPI -.->|服务器| Uvicorn
    
    NextJS -.->|类型检查| TypeScript
    NextJS -.->|样式| TailwindCSS
    NextJS -.->|测试| Vitest
    NextJS -.->|E2E测试| Playwright
    
    FastAPI -.->|数据验证| Pydantic
    FastAPI -.->|测试| Pytest

    %% 样式定义
    classDef userLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef frontendLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef backendLayer fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef infraLayer fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef uiComponents fill:#e1f5fe,stroke:#0277bd,stroke-width:1px
    classDef stateManagement fill:#f3e5f5,stroke:#7b1fa2,stroke-width:1px
    classDef businessLogic fill:#e8f5e8,stroke:#388e3c,stroke-width:1px
    classDef dataLayer fill:#fff8e1,stroke:#f9a825,stroke-width:1px
    
    class User userLayer
    class NextJS,Matrix,Controls,WordLibrary,WordSelector,UIComponents,MatrixStore,WordStore,ToastStore,Persist,MatrixCore,WordCore,CoordMode,ColorMode,LevelMode,WordMode,GroupData,DataCache,MatrixTypes frontendLayer
    class FastAPI,APIRouter,HealthAPI,Endpoints,Config,Logging,Exceptions,Services,CRUD,Models,Tasks backendLayer
    class Turbo,PNPM,TypeScript,ESLint,Prettier,Vitest,Playwright,TailwindCSS,PostCSS,Poetry,Uvicorn,Pydantic,Pytest infraLayer
    
    class Matrix,Controls,WordLibrary,WordSelector,UIComponents uiComponents
    class MatrixStore,WordStore,ToastStore,Persist stateManagement
    class MatrixCore,WordCore,CoordMode,ColorMode,LevelMode,WordMode businessLogic
    class GroupData,DataCache,MatrixTypes dataLayer
```

## 架构层次详细说明

### 1. 用户层 (User Layer)
- **用户浏览器**: 系统的最终用户接入点

### 2. 前端应用层 (Frontend Application)

#### 2.1 UI组件层 (UI Components)
- **Matrix组件**: 负责33x33网格(1089个单元格)的高性能渲染和交互
- **Controls组件**: 提供模式切换、重置等操作控制
- **WordLibraryManager**: 词库管理界面，支持词库的增删改查
- **WordSelector**: 词语选择器，用于单元格词语绑定
- **UI组件库**: 统一的按钮、图标等基础组件

#### 2.2 状态管理层 (State Management)
- **MatrixStore**: 基于Zustand的矩阵状态管理，支持响应式更新
- **WordLibraryStore**: 词库状态管理，处理词库数据和词语绑定
- **ToastStore**: 消息提示状态管理
- **持久化中间件**: 支持状态持久化和恢复

#### 2.3 业务逻辑层 (Business Logic)
- **MatrixCore**: 矩阵核心引擎，统一的数据处理管道
- **WordLibraryCore**: 词库核心逻辑，处理词库操作
- **模式处理器**: 支持坐标、颜色、等级、词语四种显示模式

#### 2.4 数据层 (Data Layer)
- **GroupAData**: A-M组数据源，提供矩阵基础数据
- **数据缓存**: 高性能缓存管理器，优化数据访问
- **MatrixTypes**: 统一的类型定义系统

### 3. 后端服务层 (Backend Services)

#### 3.1 API层 (API Layer)
- **API路由**: 模块化的API路由管理
- **健康检查**: 系统健康状态监控
- **API端点**: 具体的业务API端点

#### 3.2 核心服务 (Core Services)
- **配置管理**: 应用配置和环境管理
- **日志系统**: 结构化日志记录
- **异常处理**: 统一的异常处理机制

#### 3.3 业务服务 (Business Services)
- **业务服务**: 核心业务逻辑处理
- **数据操作**: CRUD操作封装
- **数据模型**: Pydantic数据模型定义
- **后台任务**: 异步任务处理

### 4. 基础设施层 (Infrastructure)

#### 4.1 构建工具
- **Turbo**: Monorepo构建系统，支持增量构建
- **pnpm**: 高效的包管理工具

#### 4.2 开发工具
- **TypeScript**: 完整的类型系统支持
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Vitest**: 快速的单元测试框架
- **Playwright**: 端到端测试框架

#### 4.3 样式系统
- **Tailwind CSS**: 实用优先的CSS框架
- **PostCSS**: CSS后处理工具

#### 4.4 Python后端工具
- **Poetry**: Python依赖管理
- **Uvicorn**: 高性能ASGI服务器
- **Pydantic**: 数据验证和序列化
- **Pytest**: Python测试框架

## 技术特性

### 高性能特性
- **无虚拟化渲染**: 1089个单元格同时渲染，无需虚拟滚动
- **响应式状态管理**: 基于Zustand的高效状态更新
- **多层缓存机制**: 计算属性缓存、数据缓存优化性能
- **增量构建**: Turbo支持的快速构建和热更新

### 架构特性
- **模块化设计**: 清晰的分层架构，易于维护和扩展
- **类型安全**: 完整的TypeScript类型系统
- **插件式业务模式**: 支持灵活的显示模式扩展
- **Monorepo架构**: 前后端代码统一管理

### 开发体验
- **自动API文档**: FastAPI自动生成的Swagger文档
- **热更新**: 开发环境的快速反馈
- **完整测试**: 单元测试和E2E测试覆盖
- **代码质量**: ESLint和Prettier保证代码质量

## 数据流向

1. **用户交互** → UI组件 → 状态管理 → 业务逻辑 → 数据层
2. **数据更新** → 缓存检查 → 状态更新 → 视图重新渲染
3. **模式切换** → 业务逻辑处理 → 渲染数据生成 → UI更新
4. **API调用** → 前端状态 → 后端路由 → 业务服务 → 数据返回

---

*生成时间: 2025-01-25*
*版本: v2.0*
*架构类型: 全栈Monorepo架构*