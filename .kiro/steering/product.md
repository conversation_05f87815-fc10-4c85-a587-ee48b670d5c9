---
inclusion: always
---

# Cube1 Group - 产品开发指南

## 核心产品规格

### 网格系统

- **矩阵规格**: 33x33 网格 (1089 个单元格)
- **渲染策略**: 零虚拟化，全量实时渲染
- **坐标系统**: 基于0的索引 (0-32)
- **性能要求**: 无虚拟化条件下保持流畅交互

### 数据架构

- **颜色系统**: 8种颜色类别 (红、青、黄、紫、橙、绿、蓝、粉)
- **数据层级**: 4层分层结构 (Level 1-4)
- **存储策略**: LocalStorage + API 混合同步
- **状态持久化**: 网格状态和用户偏好必须持久化

## 架构原则

### 状态管理 (Zustand)

- **basicDataStore**: 网格数据、单元格坐标、颜色映射
- **styleStore**: UI配置、主题设置、显示偏好
- **dynamicStyleStore**: 计算样式、缓存、响应式样式
- **gridConfigStore**: 网格配置、显示模式管理
- 避免属性钻取，使用store进行跨组件通信

### 性能优化

- 所有网格组件必须使用 React.memo
- 用户交互防抖 (最少100ms)
- 使用 useMemo/useCallback 缓存昂贵计算
- 批量状态更新防止渲染抖动
- 实现错误边界处理网格渲染失败

### 数据验证

- 颜色值必须符合8色系统规范
- 层级数据 (1-4) 必须是有效整数
- 实现乐观更新和回滚机制
- 所有API响应必须通过TypeScript模式验证

## 开发约定

### 命名规范

- **网格相关**: `cellIndex`, `colorCategory`, `dataLevel`
- **组件命名**: `GridCell`, `CellData`, `ColorSystem`
- **函数命名**: 使用描述性动词，如 `updateCellColor`, `validateDataLevel`
- **复杂逻辑**: 必须添加JSDoc注释

### 组件设计

- 严格分离显示组件与业务逻辑
- 复杂UI使用复合组件模式
- 统一的TypeScript接口定义
- 用户友好的错误消息处理

### API集成

- 使用 @tanstack/react-query 处理数据获取
- 实现重试逻辑和缓存策略
- 支持离线模式的本地缓存
- 严格的响应数据验证

## AI助手指导原则

### 代码生成时

- 始终考虑33x33网格的性能影响
- 优先使用已定义的store而非新建状态
- 确保颜色操作符合8色系统
- 为网格操作添加适当的错误处理

### 问题解决时

- 性能问题优先检查是否正确使用React.memo
- 状态问题优先检查store结构和数据流
- 渲染问题考虑1089个单元格的渲染负载
- 数据问题验证是否符合4层级结构

### 测试建议

- 必须测试满载1089个单元格的性能
- 验证颜色系统的完整性和一致性
- 测试数据持久化和同步场景
- 进行视觉回归测试确保网格布局正确
