---
inclusion: always
---

# Cube1 Group - 技术架构指南

## 关键架构约束

### 网格系统核心规格

- **33x33 网格 (1089 单元格)** - 零虚拟化全量渲染
- **8色系统**: 红、青、黄、紫、橙、绿、蓝、粉
- **4层数据结构**: Level 1-4
- **基于0索引**: 坐标范围 0-32

### 性能要求 (严格遵守)

- 所有网格组件必须使用 `React.memo`
- 用户交互防抖 ≥100ms
- 使用 `useMemo`/`useCallback` 缓存昂贵计算
- 批量状态更新防止渲染抖动

### 技术栈

- **前端**: Next.js 15.1.0 + React 18.3.1 + TypeScript 5.8.3
- **状态**: Zustand 5.0.6 + 持久化中间件
- **后端**: FastAPI 0.116.1 + SQLModel 0.0.24
- **测试**: Vitest 3.2.4 + Playwright 1.54.0

## 状态管理架构 (Zustand)

### Store 结构

- `basicDataStore`: 网格数据、坐标、颜色映射
- `styleStore`: UI配置、主题、显示偏好
- `dynamicStyleStore`: 计算样式、缓存
- `gridConfigStore`: 网格配置、显示模式

### 数据流原则

- 避免属性钻取，使用store跨组件通信
- 网格状态必须持久化到 LocalStorage
- 实现乐观更新和回滚机制

## 开发规范

### 命名约定

#### 前端 (TypeScript/React)

- **组件**: PascalCase (`GridCell`, `ColorSystem`)
- **钩子**: camelCase + 'use' (`useCellDataManager`)
- **Store**: camelCase + 'Store' (`basicDataStore`)
- **类型**: PascalCase (`CellData`, `GridConfig`)
- **常量**: SCREAMING_SNAKE_CASE (`MAX_GRID_SIZE`)

#### 后端 (Python)

- **模块**: snake_case (`grid_data.py`)
- **类**: PascalCase (`GridData`)
- **函数/变量**: snake_case (`get_grid_data`, `cell_data`)

#### 网格术语 (必须统一)

- `cellIndex`: 单元格索引 (0-1088)
- `colorCategory`: 颜色类别 (8色系统)
- `dataLevel`: 数据层级 (1-4)
- `gridCoordinate`: 网格坐标 (x,y: 0-32)

### 数据验证规则

- 颜色值必须符合8色系统
- 数据层级必须是1-4的整数
- 网格坐标使用0-32索引
- 所有API响应必须通过TypeScript验证

### 代码质量要求

- TypeScript严格模式
- 复杂逻辑必须添加JSDoc注释
- 错误边界处理网格渲染失败
- 统一的接口定义和错误处理

## AI助手指导原则

### 代码生成必须遵循

1. **性能优先**: 每个网格组件都用 `React.memo` 包装
2. **使用现有Store**: 优先使用已定义的4个store，避免创建新状态
3. **8色系统约束**: 颜色操作必须在8色范围内
4. **防抖处理**: 用户交互添加≥100ms防抖
5. **批量更新**: 使用Zustand的批量更新防止渲染抖动

### 组件模式示例

```typescript
// 正确的网格组件模式
const GridCell = React.memo(({ cellIndex, colorCategory }: CellProps) => {
  const updateCell = useBasicDataStore(state => state.updateCell);
  
  const handleClick = useMemo(() => 
    debounce((index: number) => updateCell(index), 100), []
  );
  
  return <div onClick={() => handleClick(cellIndex)} />;
});
```

### 问题诊断优先级

1. **性能问题** → 检查React.memo使用
2. **状态问题** → 检查store结构和数据流
3. **渲染问题** → 考虑1089单元格渲染负载
4. **数据问题** → 验证4层级结构和8色系统

### 测试要求

- 必须测试满载1089单元格的性能
- 验证颜色系统完整性
- 测试数据持久化和同步
- 视觉回归测试确保网格布局

### 开发命令

```bash
pnpm run dev        # 前端开发服务器
pnpm run test       # 运行测试
poetry run uvicorn app.main:app --reload  # 后端服务器
```
